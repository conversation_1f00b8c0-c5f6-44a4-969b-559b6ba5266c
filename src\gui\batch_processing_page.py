# src/gui/batch_processing_page.py

import os
import logging
from PySide6.QtWidgets import QWidget, QMessageBox
from PySide6.QtCore import Slot, Signal

from src.gui.ui.batch_processing_page_ui import BatchProcessingPageUI
from src.gui.handlers.batch_processing_handler import BatchProcessingHandler

logger = logging.getLogger(__name__)

class BatchProcessingPage(QWidget, BatchProcessingPageUI, BatchProcessingHandler):
    """Batch Processing page for automated analysis of multiple images."""

    # Define signals
    switch_page_requested = Signal(str)  # page_name

    def __init__(self, parent=None):
        QWidget.__init__(self, parent)
        BatchProcessingPageUI.__init__(self)
        BatchProcessingHandler.__init__(self)

        # Set up the UI
        self.setup_batch_processing_page()

        # Connect signals
        self.setup_batch_processing_connections()

        logger.info("Batch Processing page initialized")

    def apply_theme(self, theme_name="dark", font_family=None, font_size=None, style_params=None):
        """Apply the current theme to the batch processing page components.

        Args:
            theme_name (str): The name of the theme to apply
            font_family (str, optional): The font family to use
            font_size (int, optional): The font size to use
            style_params (dict, optional): Custom style parameters to use
        """
        try:
            # Import theme-related modules
            from src.gui.styles.theme_aware_buttons import get_theme_colors

            # Get current theme colors
            theme_colors = get_theme_colors(theme_name)
            is_dark = "dark" in theme_name.lower()

            # Update button styles
            self._update_button_styles(theme_colors, is_dark)

            # Log theme change
            logger.info(f"Applied theme {theme_name} to Batch Processing page")
        except Exception as e:
            logger.error(f"Error applying theme to Batch Processing page: {e}")

    def _update_button_styles(self, theme_colors, is_dark):
        """Update the styles of all buttons in the Batch Processing page.

        Args:
            theme_colors (dict): Dictionary of theme colors
            is_dark (bool): Whether the theme is dark or light
        """
        # Define primary button style (for main actions)
        primary_button_style = f"""
            QPushButton {{
                background-color: {theme_colors['highlight'].name()};
                color: {theme_colors['highlighted-text'].name()};
                border: 1px solid {theme_colors['highlight'].name()};
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
                min-height: 25px;
            }}
            QPushButton:hover {{
                background-color: {theme_colors['highlight'].lighter(110).name()};
                border-color: {theme_colors['highlight'].lighter(110).name()};
            }}
            QPushButton:pressed {{
                background-color: {theme_colors['highlight'].darker(110).name()};
                border-color: {theme_colors['highlight'].darker(110).name()};
            }}
            QPushButton:disabled {{
                background-color: {theme_colors['button'].darker(110).name() if is_dark else theme_colors['button'].lighter(110).name()};
                color: {theme_colors['button-text'].darker(150).name() if is_dark else theme_colors['button-text'].lighter(150).name()};
                border-color: {theme_colors['button'].darker(110).name() if is_dark else theme_colors['button'].lighter(110).name()};
            }}
        """

        # Define secondary button style (for regular actions)
        secondary_button_style = f"""
            QPushButton {{
                background-color: {theme_colors['button'].name()};
                color: {theme_colors['button-text'].name()};
                border: 1px solid {theme_colors['alternate-base'].name()};
                border-radius: 4px;
                padding: 6px 12px;
                min-height: 25px;
            }}
            QPushButton:hover {{
                background-color: {theme_colors['button'].lighter(110).name()};
                border-color: {theme_colors['highlight'].name()};
            }}
            QPushButton:pressed {{
                background-color: {theme_colors['button'].darker(110).name()};
            }}
            QPushButton:disabled {{
                background-color: {theme_colors['button'].darker(110).name() if is_dark else theme_colors['button'].lighter(110).name()};
                color: {theme_colors['button-text'].darker(150).name() if is_dark else theme_colors['button-text'].lighter(150).name()};
            }}
        """

        # Define danger button style (for stop/cancel actions)
        danger_button_style = f"""
            QPushButton {{
                background-color: #d32f2f;
                color: white;
                border: 1px solid #d32f2f;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
                min-height: 25px;
            }}
            QPushButton:hover {{
                background-color: #f44336;
                border-color: #f44336;
            }}
            QPushButton:pressed {{
                background-color: #b71c1c;
                border-color: #b71c1c;
            }}
            QPushButton:disabled {{
                background-color: #666;
                color: #999;
                border-color: #666;
            }}
        """

        # Apply styles to specific buttons
        if hasattr(self, 'start_batch_button'):
            self.start_batch_button.setStyleSheet(primary_button_style)

        if hasattr(self, 'stop_batch_button'):
            self.stop_batch_button.setStyleSheet(danger_button_style)

        if hasattr(self, 'back_to_hub_button'):
            self.back_to_hub_button.setStyleSheet(secondary_button_style)

        if hasattr(self, 'export_results_button'):
            self.export_results_button.setStyleSheet(secondary_button_style)

        if hasattr(self, 'generate_report_button'):
            self.generate_report_button.setStyleSheet(primary_button_style)

        if hasattr(self, 'clear_log_button'):
            self.clear_log_button.setStyleSheet(secondary_button_style)

        if hasattr(self, 'save_log_button'):
            self.save_log_button.setStyleSheet(secondary_button_style)

        # Update group box styles
        group_box_style = f"""
            QGroupBox {{
                font-weight: bold;
                border: 2px solid {theme_colors['alternate-base'].name()};
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: {theme_colors['text'].name()};
            }}
        """

        # Apply group box styles
        for attr_name in ['parameters_group', 'selected_images_group', 'task_group', 'progress_group', 'results_group']:
            if hasattr(self, attr_name):
                getattr(self, attr_name).setStyleSheet(group_box_style)

    def set_project(self, project):
        """Set the current project.

        Args:
            project: The project object
        """
        self.current_project = project
        logger.info(f"Set project for batch processing: {project.name if project else 'None'}")

    @Slot()
    def _back_to_project_hub(self):
        """Navigate back to the project hub."""
        self.switch_page_requested.emit("Project Hub")

    def load_images_for_batch_processing(self, image_paths, image_infos):
        """Load images for batch processing.

        Args:
            image_paths (list): List of image file paths
            image_infos (list): List of ImageInfo objects
        """
        logger.info(f"Loading {len(image_paths)} images for batch processing")
        
        # Call the handler method
        BatchProcessingHandler.load_images_for_batch_processing(self, image_paths, image_infos)
        
        # Update UI elements specific to the page
        self._update_page_state()
        
    def load_images_from_project_hub(self, image_paths, image_infos):
        """Load images from project hub (alias for load_images_for_batch_processing).

        Args:
            image_paths (list): List of image file paths
            image_infos (list): List of ImageInfo objects
        """
        self.load_images_for_batch_processing(image_paths, image_infos)
        
    def _update_page_state(self):
        """Update the page state based on loaded images."""
        has_images = len(self.selected_images) > 0
        
        # Enable/disable controls based on whether images are loaded
        if hasattr(self, 'start_batch_button'):
            self.start_batch_button.setEnabled(has_images)
            
        if hasattr(self, 'task_combo'):
            self.task_combo.setEnabled(has_images)
            
        # Update parameters group
        if hasattr(self, 'parameters_group'):
            self.parameters_group.setEnabled(has_images)
            
    def get_processing_results(self):
        """Get the current processing results.
        
        Returns:
            dict: Dictionary of processing results keyed by image path
        """
        return self.processing_results.copy()
        
    def clear_results(self):
        """Clear all processing results."""
        self.processing_results.clear()
        self._update_results_grid()
        self._add_log_message("Results cleared.")
        
    def is_processing(self):
        """Check if batch processing is currently running.
        
        Returns:
            bool: True if processing is running, False otherwise
        """
        return (self.processing_worker is not None and 
                self.processing_worker.isRunning())
                
    def get_selected_images_count(self):
        """Get the number of selected images.
        
        Returns:
            int: Number of selected images
        """
        return len(self.selected_images)
        
    def get_completed_images_count(self):
        """Get the number of completed images.
        
        Returns:
            int: Number of completed images
        """
        return len(self.processing_results)