# VisionLab AI Website

A complete, professional website for VisionLab AI software featuring a landing page, waitlist registration system, and contact form.

## Features

- **Professional Landing Page**: Modern, responsive design showcasing VisionLab AI
- **Waitlist Registration**: Email collection with confirmation system
- **Email Verification**: Automated email confirmation with unique tokens
- **Contact Form**: Professional inquiry system with admin notifications
- **Responsive Design**: Optimized for desktop, tablet, and mobile devices
- **Database Integration**: MySQL backend for data storage
- **Security Features**: Input validation, CSRF protection, rate limiting
- **Analytics Ready**: Google Analytics integration support

## File Structure

```
website/
├── index.html              # Main landing page
├── styles.css              # Complete CSS styling
├── script.js               # JavaScript functionality
├── process_waitlist.php    # Waitlist form handler
├── process_contact.php     # Contact form handler
├── confirm.php             # Email confirmation page
├── database_setup.sql      # Database schema
└── README.md              # This file
```

## Prerequisites

- Web hosting with PHP support (PHP 7.4 or higher)
- MySQL database
- Email sending capability (SMTP or PHP mail)
- SSL certificate (recommended)

## Hostinger Deployment Guide

### Step 1: Prepare Your Hostinger Account

1. **Login to Hostinger Control Panel**
2. **Access File Manager** or use FTP client
3. **Navigate to public_html** directory

### Step 2: Upload Files

1. Upload all website files to `public_html/` directory:
   - `index.html`
   - `styles.css`
   - `script.js`
   - `process_waitlist.php`
   - `process_contact.php`
   - `confirm.php`

### Step 3: Database Setup

1. **Access MySQL Databases** in Hostinger control panel
2. **Create a new database**:
   - Database name: `visionlab_waitlist`
   - Create database user with full privileges
3. **Import database schema**:
   - Use phpMyAdmin or MySQL command line
   - Execute the SQL commands from `database_setup.sql`

### Step 4: Configure Database Connection

Update database credentials in these files:

#### In `process_waitlist.php`:
```php
$db_config = [
    'host' => 'localhost',           // Usually 'localhost' on Hostinger
    'dbname' => 'your_db_name',      // Your database name
    'username' => 'your_db_user',    // Your database username
    'password' => 'your_db_password' // Your database password
];
```

#### In `process_contact.php`:
```php
$db_config = [
    'host' => 'localhost',
    'dbname' => 'your_db_name',
    'username' => 'your_db_user',
    'password' => 'your_db_password'
];
```

#### In `confirm.php`:
```php
$db_config = [
    'host' => 'localhost',
    'dbname' => 'your_db_name',
    'username' => 'your_db_user',
    'password' => 'your_db_password'
];
```

### Step 5: Email Configuration

#### Option A: Using SMTP (Recommended)

1. **Install PHPMailer** (if not available):
   ```bash
   composer require phpmailer/phpmailer
   ```

2. **Configure SMTP settings** in both `process_waitlist.php` and `process_contact.php`:
   ```php
   $mail_config = [
       'smtp_host' => 'smtp.hostinger.com',  // Hostinger SMTP
       'smtp_port' => 587,
       'smtp_username' => '<EMAIL>',
       'smtp_password' => 'your-email-password',
       'from_email' => '<EMAIL>',
       'from_name' => 'VisionLab AI',
       'admin_email' => '<EMAIL>'
   ];
   ```

#### Option B: Using PHP mail() function

The code includes fallback to PHP's built-in `mail()` function if PHPMailer is not available.

### Step 6: Domain and SSL Setup

1. **Point your domain** to Hostinger nameservers
2. **Enable SSL certificate** in Hostinger control panel
3. **Update URLs** in the code to use your domain:
   - Update confirmation links in email templates
   - Update any hardcoded URLs

### Step 7: Security Configuration

1. **Set proper file permissions**:
   - PHP files: 644
   - Directories: 755

2. **Create .htaccess file** for additional security:
   ```apache
   # Prevent access to sensitive files
   <Files "database_setup.sql">
       Order allow,deny
       Deny from all
   </Files>
   
   <Files "README.md">
       Order allow,deny
       Deny from all
   </Files>
   
   # Enable HTTPS redirect
   RewriteEngine On
   RewriteCond %{HTTPS} off
   RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
   ```

### Step 8: Testing

1. **Test the website**:
   - Visit your domain
   - Test waitlist registration
   - Check email confirmation
   - Test contact form

2. **Check database**:
   - Verify data is being stored
   - Check email logs

3. **Test email functionality**:
   - Register for waitlist
   - Check confirmation email
   - Click confirmation link

## Customization

### Branding

1. **Update company information** in `index.html`:
   - Company name
   - Logo
   - Contact details
   - Social media links

2. **Customize colors** in `styles.css`:
   - Primary color: `#2563eb`
   - Secondary color: `#3b82f6`
   - Accent colors as needed

3. **Update content**:
   - Software features
   - Timeline information
   - About section

### Email Templates

Customize email templates in:
- `process_waitlist.php` (confirmation email)
- `process_contact.php` (contact notifications)

### Analytics

Add Google Analytics by updating the tracking ID in `script.js`:
```javascript
// Replace 'GA_MEASUREMENT_ID' with your actual ID
gtag('config', 'GA_MEASUREMENT_ID');
```

## Maintenance

### Regular Tasks

1. **Monitor database growth**
2. **Check email logs** for delivery issues
3. **Update software** (PHP, database)
4. **Backup database** regularly
5. **Monitor website performance**

### Database Maintenance

```sql
-- Clean old unconfirmed registrations (older than 30 days)
DELETE FROM waitlist 
WHERE confirmed = FALSE 
AND created_at < DATE_SUB(NOW(), INTERVAL 30 DAY);

-- Clean old email logs (older than 90 days)
DELETE FROM email_logs 
WHERE created_at < DATE_SUB(NOW(), INTERVAL 90 DAY);
```

## Troubleshooting

### Common Issues

1. **Database Connection Errors**:
   - Check database credentials
   - Verify database exists
   - Check Hostinger database settings

2. **Email Not Sending**:
   - Check SMTP credentials
   - Verify email account settings
   - Check spam folders
   - Review email logs in database

3. **Form Submission Issues**:
   - Check PHP error logs
   - Verify file permissions
   - Test with browser developer tools

4. **CSS/JS Not Loading**:
   - Check file paths
   - Verify file permissions
   - Clear browser cache

### Error Logs

Check PHP error logs in Hostinger control panel:
- **Error Logs** section
- Look for PHP errors
- Check database connection issues

## Security Best Practices

1. **Keep PHP updated**
2. **Use strong database passwords**
3. **Enable SSL/HTTPS**
4. **Regular security updates**
5. **Monitor for suspicious activity**
6. **Backup regularly**
7. **Use prepared statements** (already implemented)
8. **Validate all inputs** (already implemented)

## Support

For technical support:
1. Check Hostinger documentation
2. Review error logs
3. Test in development environment
4. Contact Hostinger support if needed

## License

This website template is provided as-is for VisionLab AI. Customize as needed for your specific requirements.

---

**Note**: Remember to replace placeholder values (database credentials, email addresses, domain names) with your actual information before deployment.