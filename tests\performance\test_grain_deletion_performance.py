#!/usr/bin/env python3
"""
Performance tests for grain deletion optimizations.
Tests the performance improvements in grain deletion operations.
"""

import time
import numpy as np
import pandas as pd
import torch
from typing import List, Set
import logging
from unittest.mock import Mock, MagicMock

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MockGrainAnalysisWidget:
    """Mock widget for testing deletion performance."""
    
    def __init__(self, num_grains: int):
        """Initialize with synthetic grain data."""
        self.grain_df = self._create_synthetic_dataframe(num_grains)
        self.grain_annotations = self._create_synthetic_annotations(num_grains)
        self.grain_current_scale_factor = 1.0
        self.grain_recalculation_needed = False
        
        # Mock UI components
        self.scene = Mock()
        self.grain_items = {}
        self.results_widget = Mock()
        
    def _create_synthetic_dataframe(self, num_grains: int) -> pd.DataFrame:
        """Create synthetic grain data for testing."""
        np.random.seed(42)  # For reproducible results
        
        data = {
            'Object ID': [f"Object_{i}" for i in range(num_grains)],
            'Area (µm²)': np.random.uniform(100, 1000, num_grains),
            'Perimeter (µm)': np.random.uniform(50, 200, num_grains),
            'ECD (µm)': np.random.uniform(10, 50, num_grains),
            'Length (µm)': np.random.uniform(15, 60, num_grains),
            'Width (µm)': np.random.uniform(8, 40, num_grains),
            'Aspect Ratio': np.random.uniform(1.0, 3.0, num_grains),
            'Solidity': np.random.uniform(0.7, 1.0, num_grains),
            'Convexity': np.random.uniform(0.8, 1.0, num_grains),
            'Compactness': np.random.uniform(0.5, 1.0, num_grains)
        }
        
        df = pd.DataFrame(data)
        df.index = range(num_grains)  # Ensure index matches annotation indices
        return df
    
    def _create_synthetic_annotations(self, num_grains: int) -> List[torch.Tensor]:
        """Create synthetic annotation tensors."""
        annotations = []
        for i in range(num_grains):
            # Create random binary mask (simplified)
            mask = torch.randint(0, 2, (100, 100), dtype=torch.uint8)
            annotations.append(mask)
        return annotations
    
    def _remove_deleted_graphics_items(self, deleted_indices: Set[int]):
        """Mock graphics item removal."""
        for idx in deleted_indices:
            if idx in self.grain_items:
                del self.grain_items[idx]
    
    def _update_visualization_after_deletion_optimized(self, deleted_indices: Set[int]):
        """Mock optimized visualization update."""
        self._remove_deleted_graphics_items(deleted_indices)
        # Simulate some processing time
        time.sleep(0.001 * len(deleted_indices))
    
    def mark_state_as_modified(self):
        """Mock state modification."""
        pass
    
    def save_grain_analysis_state(self):
        """Mock state saving."""
        pass
    
    def update_status(self, message: str):
        """Mock status update."""
        pass
    
    def draw_grain_highlights(self):
        """Mock highlight drawing."""
        pass


def simulate_old_deletion_method(widget: MockGrainAnalysisWidget, indices_to_delete: Set[int]) -> float:
    """Simulate the old deletion method with full recalculation."""
    start_time = time.time()
    
    # Filter data structures (same as optimized)
    valid_indices_to_delete = indices_to_delete.intersection(set(widget.grain_df.index))
    df_keep_mask = ~widget.grain_df.index.isin(valid_indices_to_delete)
    original_df_indices = widget.grain_df.index.tolist()
    
    indices_to_keep_in_annotations = [
        i for i, df_idx in enumerate(original_df_indices) 
        if df_idx not in valid_indices_to_delete
    ]
    
    # Filter annotations
    widget.grain_annotations = [widget.grain_annotations[i] for i in indices_to_keep_in_annotations]
    widget.grain_df = widget.grain_df[df_keep_mask].reset_index(drop=True)
    
    # OLD METHOD: Full parameter recalculation (simulated)
    # This would call calculate_parameters() for ALL remaining grains
    remaining_grains = len(widget.grain_annotations)
    recalc_time = remaining_grains * 0.002  # Simulate 2ms per grain for parameter calculation
    time.sleep(recalc_time)
    
    # Full visualization regeneration (simulated)
    time.sleep(0.01)  # Simulate contour regeneration
    
    # Full graphics redraw (simulated)
    time.sleep(0.005 * remaining_grains)  # Simulate redrawing all items
    
    end_time = time.time()
    return end_time - start_time


def simulate_optimized_deletion_method(widget: MockGrainAnalysisWidget, indices_to_delete: Set[int]) -> float:
    """Simulate the optimized deletion method."""
    start_time = time.time()
    
    # Filter data structures (same as old method)
    valid_indices_to_delete = indices_to_delete.intersection(set(widget.grain_df.index))
    df_keep_mask = ~widget.grain_df.index.isin(valid_indices_to_delete)
    original_df_indices = widget.grain_df.index.tolist()
    
    indices_to_keep_in_annotations = [
        i for i, df_idx in enumerate(original_df_indices) 
        if df_idx not in valid_indices_to_delete
    ]
    
    # Filter annotations
    widget.grain_annotations = [widget.grain_annotations[i] for i in indices_to_keep_in_annotations]
    widget.grain_df = widget.grain_df[df_keep_mask].reset_index(drop=True)
    
    # OPTIMIZED: Skip parameter recalculation, set flag instead
    widget.grain_recalculation_needed = True
    
    # Optimized visualization update
    widget._update_visualization_after_deletion_optimized(valid_indices_to_delete)
    
    # Optimized UI updates
    widget.draw_grain_highlights()
    widget.results_widget.populate(widget.grain_df)
    widget.update_status(f"Deleted {len(valid_indices_to_delete)} grains. Recalculation recommended.")
    
    end_time = time.time()
    return end_time - start_time


def run_performance_benchmark():
    """Run performance benchmarks for different dataset sizes."""
    dataset_sizes = [100, 500, 1000, 2000]
    deletion_sizes = [1, 5, 10, 50]
    
    results = []
    
    for num_grains in dataset_sizes:
        for num_deletions in deletion_sizes:
            if num_deletions >= num_grains:
                continue
                
            logger.info(f"Testing {num_grains} grains, deleting {num_deletions}")
            
            # Test old method
            widget_old = MockGrainAnalysisWidget(num_grains)
            indices_to_delete = set(range(num_deletions))
            old_time = simulate_old_deletion_method(widget_old, indices_to_delete)
            
            # Test optimized method
            widget_new = MockGrainAnalysisWidget(num_grains)
            indices_to_delete = set(range(num_deletions))
            new_time = simulate_optimized_deletion_method(widget_new, indices_to_delete)
            
            speedup = old_time / new_time if new_time > 0 else float('inf')
            
            result = {
                'dataset_size': num_grains,
                'deletions': num_deletions,
                'old_time': old_time,
                'new_time': new_time,
                'speedup': speedup,
                'time_saved': old_time - new_time
            }
            results.append(result)
            
            logger.info(f"  Old: {old_time:.4f}s, New: {new_time:.4f}s, Speedup: {speedup:.2f}x")
    
    return results


def test_data_consistency():
    """Test that optimized deletion maintains data consistency."""
    logger.info("Testing data consistency...")
    
    num_grains = 100
    widget = MockGrainAnalysisWidget(num_grains)
    
    # Store original state
    original_df_len = len(widget.grain_df)
    original_annotations_len = len(widget.grain_annotations)
    
    # Delete some grains
    indices_to_delete = {5, 15, 25, 35, 45}
    simulate_optimized_deletion_method(widget, indices_to_delete)
    
    # Verify consistency
    expected_remaining = original_df_len - len(indices_to_delete)
    actual_df_len = len(widget.grain_df)
    actual_annotations_len = len(widget.grain_annotations)
    
    assert actual_df_len == expected_remaining, f"DataFrame length mismatch: {actual_df_len} != {expected_remaining}"
    assert actual_annotations_len == expected_remaining, f"Annotations length mismatch: {actual_annotations_len} != {expected_remaining}"
    assert widget.grain_recalculation_needed == True, "Recalculation flag should be set"
    
    logger.info("✓ Data consistency test passed")


if __name__ == "__main__":
    logger.info("Starting grain deletion performance tests...")
    
    # Run consistency test
    test_data_consistency()
    
    # Run performance benchmark
    results = run_performance_benchmark()
    
    # Print summary
    logger.info("\n" + "="*60)
    logger.info("PERFORMANCE BENCHMARK SUMMARY")
    logger.info("="*60)
    
    for result in results:
        logger.info(f"Dataset: {result['dataset_size']:4d} grains, "
                   f"Deleted: {result['deletions']:2d}, "
                   f"Speedup: {result['speedup']:6.2f}x, "
                   f"Time saved: {result['time_saved']*1000:6.1f}ms")
    
    # Calculate average speedup
    avg_speedup = np.mean([r['speedup'] for r in results])
    total_time_saved = sum([r['time_saved'] for r in results])
    
    logger.info(f"\nAverage speedup: {avg_speedup:.2f}x")
    logger.info(f"Total time saved in tests: {total_time_saved*1000:.1f}ms")
    logger.info("\n✓ All performance tests completed successfully!")
