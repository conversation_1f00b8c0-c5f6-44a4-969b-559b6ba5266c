# src/gui/dialogs/gemini_settings_dialog.py

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QLabel, QLineEdit, QDialogButtonBox, QMessageBox,
    QComboBox, QHBoxLayout, QGroupBox
)
from PySide6.QtCore import QSettings

class GeminiSettingsDialog(QDialog):
    """Dialog to configure Gemini API settings."""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Gemini API Settings")
        self.setMinimumWidth(400)

        self.settings = QSettings("VisionLab Ai", "VisionLab_Ai_V4")

        layout = QVBoxLayout(self)

        # API Key
        api_key_group = QGroupBox("API Key")
        api_key_layout = QVBoxLayout(api_key_group)

        api_key_layout.addWidget(QLabel("Google Gemini API Key:"))
        self.api_key_input = QLineEdit()
        self.api_key_input.setEchoMode(QLineEdit.EchoMode.Password)
        # Load saved key
        saved_key = self.settings.value("gemini/api_key", "")
        self.api_key_input.setText(saved_key)
        api_key_layout.addWidget(self.api_key_input)

        layout.addWidget(api_key_group)

        # Model Selection
        model_group = QGroupBox("Model Selection")
        model_layout = QVBoxLayout(model_group)

        model_layout.addWidget(QLabel("Select Gemini Model:"))
        self.model_selector = QComboBox()
        self.model_selector.addItems([
            "gemini-2.5-pro-preview-06-05",
            "gemini-2.5-flash-preview-05-20",
            "gemini-2.5-flash-preview-04-17",
            "gemini-2.0-flash",
            "gemini-2.0-pro",
            "gemini-1.5-flash",
            "gemini-1.5-pro"
        ])
        # Load saved model
        saved_model = self.settings.value("gemini/model", "gemini-2.5-pro-preview-06-05")
        index = self.model_selector.findText(saved_model)
        if index >= 0:
            self.model_selector.setCurrentIndex(index)
        model_layout.addWidget(self.model_selector)

        layout.addWidget(model_group)

        # Instructions
        instructions = QLabel(
            "To use the AI Assistant, you need a Google Gemini API key.\n"
            "1. Go to https://makersuite.google.com/app/apikey\n"
            "2. Create a new API key\n"
            "3. Copy and paste it here"
        )
        instructions.setWordWrap(True)
        layout.addWidget(instructions)

        # Buttons
        self.button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel)
        self.button_box.accepted.connect(self.accept)
        self.button_box.rejected.connect(self.reject)
        layout.addWidget(self.button_box)

    def accept(self):
        """Save settings when OK is clicked."""
        # Save API key
        new_key = self.api_key_input.text().strip()
        self.settings.setValue("gemini/api_key", new_key)
        self.settings.sync()  # Force settings to be written to disk immediately

        # Save model selection
        model = self.model_selector.currentText()
        self.settings.setValue("gemini/model", model)
        self.settings.sync()  # Force settings to be written to disk immediately

        # No message boxes here - they will be shown by the caller
        super().accept()

    @staticmethod
    def get_api_key():
        """Static method to get the API key from settings."""
        settings = QSettings("VisionLab Ai", "VisionLab_Ai_V4")
        settings.sync()  # Ensure we're reading the latest values
        api_key = settings.value("gemini/api_key", "")
        return api_key

    @staticmethod
    def get_model_name():
        """Static method to get the model name from settings."""
        settings = QSettings("VisionLab Ai", "VisionLab_Ai_V4")
        settings.sync()  # Ensure we're reading the latest values
        model_name = settings.value("gemini/model", "gemini-2.5-pro-preview-06-05")

        # Remove the "models/" prefix if it exists
        if model_name and model_name.startswith("models/"):
            model_name = model_name.replace("models/", "")

        return model_name
