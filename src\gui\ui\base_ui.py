# src/gui/ui/base_ui.py

import sys
from PySide6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
    QHBoxLayout, QLabel, QPushButton, QFileDialog, QMessageBox, QSlider, QFrame,
    QSizePolicy, QMenuBar, QCheckBox, QProgressBar, QGridLayout, QLineEdit,
    QComboBox, QTabWidget, QColorDialog, QSpinBox, QDoubleSpinBox, QDialog,
    QFormLayout, QGroupBox, QScrollArea, QStyleFactory)

from PySide6.QtGui import QPixmap, QImage, QAction, QPalette, QColor, QIcon
from PySide6.QtCore import Qt, QRect, QSize, QEvent

class HoverButton(QPushButton):
    """Custom QPushButton with hover icon functionality."""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.normal_icon = None
        self.hover_icon = None
        self.installEventFilter(self)
        
    def eventFilter(self, obj, event):
        if obj == self:
            if event.type() == QEvent.Enter and self.hover_icon:
                self.setIcon(self.hover_icon)
            elif event.type() == QEvent.Leave and self.normal_icon:
                self.setIcon(self.normal_icon)
        return super().eventFilter(obj, event)
        
    def set_hover_icons(self, normal_icon, hover_icon):
        self.normal_icon = normal_icon
        self.hover_icon = hover_icon
        self.setIcon(normal_icon)

class BaseUI(QMainWindow):
    """Base class for VisionLab Ai UI, containing common UI setup methods."""

    def __init__(self):
        super().__init__()
        self.setWindowTitle("VisionLab Ai")
        self.setGeometry(100, 100, 1600, 900)
        self.setMinimumSize(1200, 800)

        # Set window flags to remove the title bar
        self.setWindowFlags(self.windowFlags() | Qt.FramelessWindowHint)

        # Hide the menu bar
        self.menuBar().setVisible(False)
        self.menuBar().setMaximumHeight(0)

        # UI elements that will be needed in the main class
        self.stacked_widget = None
        self.process_btn = None
        self.analysis_btn = None
        self.settings_btn = None
        self.trainable_btn = None
        self.ai_assistant_btn = None
        self.point_counting_btn = None

        # Menu actions
        self.open_action = None
        self.save_action = None
        self.exit_action = None
        self.rand_colors_action = None
        self.pick_colors_action = None
        self.calc_percent_action = None
        self.start_seg_action = None
        self.merge_seg_action = None
        self.preprocess_action = None
        self.about_action = None

    def setup_ui(self):
        """Sets up the main UI components."""
        # Skip creating the old menu bar
        # self.create_menu()
        central_widget = QWidget(self)
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # Set the window to have no margins
        self.setContentsMargins(0, 0, 0, 0)

        # Create custom title bar
        self.create_title_bar(main_layout)

        # Ensure the window has no margin at the top
        self.setStyleSheet("""
            QMainWindow {
                margin: 0;
                padding: 0;
                border: none;
            }
        """)

        # Create container for vertical navigation layout
        self.main_container = QWidget()
        self.main_container_layout = QHBoxLayout(self.main_container)
        self.main_container_layout.setContentsMargins(0, 0, 0, 0)
        self.main_container_layout.setSpacing(0)
        main_layout.addWidget(self.main_container)

        # Create vertical navigation sidebar (will be added first)
        self.setup_vertical_navigation()

        # Create container for tab widget
        self.tab_container = QWidget()
        self.tab_container_layout = QVBoxLayout(self.tab_container)
        self.tab_container_layout.setContentsMargins(0, 0, 0, 0)
        self.tab_container_layout.setSpacing(0)
        self.main_container_layout.addWidget(self.tab_container)

        # Stacked Widget for Pages
        self.stacked_widget = QTabWidget()
        self.tab_container_layout.addWidget(self.stacked_widget)

        # Hide the tab bar since we're using the custom navigation bar with icons
        self.stacked_widget.tabBar().setVisible(False)
        self.stacked_widget.setDocumentMode(True)  # Make tabs look more modern
        self.stacked_widget.setMovable(True)  # Allow tab reordering
        self.stacked_widget.setElideMode(Qt.ElideRight)  # Handle long tab names

        # Set initial tab position based on settings
        self.setup_tab_position()

        # Create collapse/expand button for sidebar mode
        self.create_collapse_button()

        # Apply initial tab styling and setup icons
        self.update_tab_styling()

        # Setup navigation icons (this will be called again in update_tab_styling)
        self.setup_navigation_icons()

    def setup_vertical_navigation(self):
        """Creates the vertical navigation sidebar."""
        # Create vertical navigation frame
        self.vertical_nav_frame = QFrame()
        self.vertical_nav_frame.setObjectName("VerticalNavigationFrame")
        self.vertical_nav_frame.setFixedWidth(90)  # Increased width for larger icons
        self.vertical_nav_frame.setStyleSheet("""
            QFrame#VerticalNavigationFrame {
                background-color: palette(window);
                border-right: 1px solid palette(mid);
            }
        """)

        # Create vertical layout for navigation buttons
        self.vertical_nav_layout = QVBoxLayout(self.vertical_nav_frame)
        self.vertical_nav_layout.setSpacing(8)
        self.vertical_nav_layout.setContentsMargins(8, 8, 8, 8)

        # Add the vertical navigation to the main container (left side)
        self.main_container_layout.addWidget(self.vertical_nav_frame)

        # Create navigation buttons
        self.create_vertical_navigation_buttons()

    def create_vertical_navigation_buttons(self):
        """Creates the vertical navigation buttons."""

        # Navigation buttons (using custom HoverButton for navigation tabs)
        self.project_hub_btn = HoverButton()
        self.process_btn = HoverButton()
        self.trainable_btn = HoverButton()
        self.point_counting_btn = HoverButton()
        self.grain_analysis_btn = HoverButton()
        self.advanced_segmentation_btn = HoverButton()
        self.analysis_btn = HoverButton()
        self.batch_processing_btn = HoverButton()
        self.settings_btn = HoverButton()
        self.ai_assistant_btn = HoverButton()
        self.about_btn = HoverButton()

        # Add icons to navigation buttons
        self.setup_navigation_button_icons()

        button_tooltips = {
            self.project_hub_btn: "Project Hub",
            self.process_btn: "Unsupervised Segmentation",
            self.trainable_btn: "Trainable Segmentation",
            self.point_counting_btn: "Point Counting",
            self.grain_analysis_btn: "Grain Analysis",
            self.advanced_segmentation_btn: "Advanced Segmentation",
            self.analysis_btn: "Image Lab",
            self.batch_processing_btn: "Batch Processing",
            self.settings_btn: "Settings",
            self.ai_assistant_btn: "AI Assistant",
            self.about_btn: "About"
        }

        # Configure buttons for vertical layout
        for btn in [self.project_hub_btn, self.process_btn, self.trainable_btn,
                   self.point_counting_btn, self.grain_analysis_btn, self.advanced_segmentation_btn,
                   self.analysis_btn, self.batch_processing_btn, self.ai_assistant_btn, self.settings_btn, self.about_btn]:
            btn.setToolTip(button_tooltips.get(btn, ""))
            btn.setFixedSize(72, 72)  # Larger square buttons for bigger icons
            btn.setCheckable(True)
            self.vertical_nav_layout.addWidget(btn)

        # Add stretch to push buttons to the top
        self.vertical_nav_layout.addStretch()

        # Apply initial navigation button styling
        self.update_navigation_button_styling()

        # Set default active button
        self.project_hub_btn.setChecked(True)

        # Connect navigation buttons to tab switching
        self.connect_navigation_buttons()

    def setup_tab_position(self):
        """Sets up the tab position based on user settings."""
        try:
            from PySide6.QtCore import QSettings
            settings = QSettings("VisionLab Ai", "VisionLab_Ai_V4")
            tab_position = settings.value("app/tab_position", "top").lower()
            
            # Map string values to QTabWidget positions
            position_map = {
                "top": QTabWidget.North,
                "bottom": QTabWidget.South,
                "left": QTabWidget.West,
                "right": QTabWidget.East
            }
            
            position = position_map.get(tab_position, QTabWidget.North)
            self.stacked_widget.setTabPosition(position)
            
            # Adjust layout based on position
            if tab_position == "left":
                self.setup_left_sidebar_layout()
            elif tab_position == "bottom":
                self.setup_bottom_tabs_layout()
            else:
                self.setup_default_layout()
                
        except Exception:
            # Default to top position if there's an error
            self.stacked_widget.setTabPosition(QTabWidget.North)
            self.setup_default_layout()
    
    def setup_left_sidebar_layout(self):
        """Configures layout for left sidebar tabs."""
        # Set minimum width for left tabs to ensure readability
        self.stacked_widget.tabBar().setMinimumWidth(150)
        # Show collapse button for left sidebar
        if hasattr(self, 'collapse_button'):
            self.collapse_button.setVisible(True)
    
    def setup_bottom_tabs_layout(self):
        """Configures layout for bottom tabs."""
        # Hide collapse button for bottom tabs
        if hasattr(self, 'collapse_button'):
            self.collapse_button.setVisible(False)
    
    def setup_default_layout(self):
        """Configures default top tabs layout."""
        # Hide collapse button for top tabs
        if hasattr(self, 'collapse_button'):
            self.collapse_button.setVisible(False)
    
    def create_collapse_button(self):
        """Creates a collapse/expand button for sidebar mode."""
        from PySide6.QtWidgets import QPushButton
        from PySide6.QtCore import QSize
        
        self.collapse_button = QPushButton("◀")
        self.collapse_button.setFixedSize(QSize(20, 100))
        self.collapse_button.setToolTip("Collapse/Expand Sidebar")
        self.collapse_button.clicked.connect(self.toggle_sidebar_collapse)
        self.collapse_button.setVisible(False)  # Hidden by default
        
        # Add to layout
        self.tab_container_layout.addWidget(self.collapse_button)
        
        # Track collapse state
        self.sidebar_collapsed = False
    
    def toggle_sidebar_collapse(self):
        """Toggles the sidebar collapse state."""
        if self.sidebar_collapsed:
            # Expand sidebar
            self.stacked_widget.tabBar().setVisible(True)
            self.collapse_button.setText("◀")
            self.collapse_button.setToolTip("Collapse Sidebar")
            self.sidebar_collapsed = False
        else:
            # Collapse sidebar
            self.stacked_widget.tabBar().setVisible(False)
            self.collapse_button.setText("▶")
            self.collapse_button.setToolTip("Expand Sidebar")
            self.sidebar_collapsed = True
    
    def set_tab_position(self, position):
        """Public method to change tab position programmatically."""
        try:
            from PySide6.QtCore import QSettings
            settings = QSettings("VisionLab Ai", "VisionLab_Ai_V4")
            
            # Map QTabWidget positions to string values
            position_map = {
                QTabWidget.North: "top",
                QTabWidget.South: "bottom", 
                QTabWidget.West: "left",
                QTabWidget.East: "right"
            }
            
            position_str = position_map.get(position, "top")
            settings.setValue("app/tab_position", position_str)
            
            # Apply the new position
            self.setup_tab_position()
            self.update_tab_styling()
            
        except Exception as e:
            print(f"Error setting tab position: {e}")
    
    def setup_navigation_icons(self):
        """Sets up theme-aware icons for the main navigation tabs."""
        try:
            from src.gui.utils.icon_utils import get_tab_icon

            # Apply icons to existing tabs
            for i in range(self.stacked_widget.count()):
                tab_text = self.stacked_widget.tabText(i)

                # Get theme-aware icon for this tab
                icon = get_tab_icon(tab_text, size=20, palette=self.palette())

                if not icon.isNull():
                    self.stacked_widget.setTabIcon(i, icon)
                    print(f"Successfully set theme-aware icon for tab '{tab_text}'")
                else:
                    print(f"Warning: No icon found for tab '{tab_text}'")

        except Exception as e:
             print(f"Error setting up navigation icons: {e}")
    
    def setup_navigation_button_icons(self):
        """Sets up theme-aware icons for navigation buttons."""
        try:
            from src.gui.utils.icon_utils import load_theme_aware_icon, create_theme_aware_icon
            from PySide6.QtCore import QSettings
            
            # Get current theme
            settings = QSettings("VisionLab Ai", "VisionLab_Ai_V4")
            theme = settings.value("app/theme", "Dark Theme").lower()
            is_dark = "dark" in theme

            # Define the mapping between buttons and their corresponding icon files
            button_icon_mapping = {
                self.project_hub_btn: "project_hub.svg",
                self.process_btn: "segmentation.svg",
                self.trainable_btn: "trainable_segmentation.svg",
                self.point_counting_btn: "point_counting.svg",
                self.grain_analysis_btn: "grain_analysis.svg",
                self.advanced_segmentation_btn: "advanced_segmentation.svg",
                self.analysis_btn: "image_lab.svg",
                self.batch_processing_btn: "files.svg",
                self.settings_btn: "settings.svg",
                self.ai_assistant_btn: "ai_assistant.svg",
                self.about_btn: "about.svg"
            }

            # Apply icons to navigation buttons
            for button, icon_file in button_icon_mapping.items():
                # Create theme-aware icon with much larger size for navigation buttons
                icon_path = f"src/gui/icons/{icon_file}"
                normal_icon = create_theme_aware_icon(icon_path, size=48, palette=self.palette())

                # Create hover icon (white for light theme, same for dark theme)
                if not is_dark:
                    hover_icon = create_theme_aware_icon(icon_path, size=48, palette=self.palette(), force_color=QColor(255, 255, 255))
                else:
                    hover_icon = normal_icon  # Same icon for dark theme

                if not normal_icon.isNull():
                    # Set up hover behavior for custom buttons
                    if hasattr(button, 'set_hover_icons'):
                        button.set_hover_icons(normal_icon, hover_icon)
                    else:
                        button.setIcon(normal_icon)

                    button.setIconSize(QSize(48, 48))  # Much larger icon size for better visibility
                    print(f"Successfully set theme-aware icon for navigation button: {icon_file}")
                else:
                    print(f"Warning: Could not load icon: {icon_file}")

        except Exception as e:
            print(f"Error setting up navigation button icons: {e}")

    def create_theme_aware_icon(self, icon_path, size=None):
        """Creates a theme-aware icon that adapts to the current theme."""
        try:
            from src.gui.utils.icon_utils import create_theme_aware_icon

            # Set default size if not provided
            if size is None:
                size = 24

            # Use the utility function with current palette
            return create_theme_aware_icon(icon_path, size, self.palette())

        except Exception as e:
            print(f"Error creating theme-aware icon for {icon_path}: {e}")
            # Fallback to regular icon loading
            from PySide6.QtGui import QIcon
            import os
            return QIcon(icon_path) if os.path.exists(icon_path) else QIcon()

    def update_tab_icon(self, tab_index, tab_name=None):
        """Updates the theme-aware icon for a specific tab."""
        try:
            from src.gui.utils.icon_utils import get_tab_icon

            if tab_name is None:
                tab_name = self.stacked_widget.tabText(tab_index)

            # Get theme-aware icon for this tab
            icon = get_tab_icon(tab_name, size=20, palette=self.palette())

            if not icon.isNull():
                self.stacked_widget.setTabIcon(tab_index, icon)
                return True

        except Exception as e:
            print(f"Error updating tab icon: {e}")
        return False

    def refresh_all_icons(self):
        """Refreshes all icons to match the current theme."""
        try:
            # Refresh navigation button icons
            self.setup_navigation_button_icons()

            # Refresh tab icons
            for i in range(self.stacked_widget.count()):
                self.update_tab_icon(i)

            print("Successfully refreshed all icons for current theme")

        except Exception as e:
            print(f"Error refreshing icons: {e}")

    def update_theme(self):
        """Updates the theme and refreshes all UI elements including icons."""
        try:
            # Update tab styling (which includes icon refresh)
            self.update_tab_styling()

            # Update navigation button styling
            self.update_navigation_button_styling()

            print("Successfully updated theme and refreshed all UI elements")

        except Exception as e:
            print(f"Error updating theme: {e}")

    def update_navigation_button_styling(self):
        """Updates the styling of navigation buttons for the current theme."""
        try:
            from PySide6.QtCore import QSettings

            # Get theme colors
            try:
                settings = QSettings("VisionLab Ai", "VisionLab_Ai_V4")
                theme = settings.value("app/theme", "Dark Theme").lower()
                is_dark = "dark" in theme
            except Exception:
                is_dark = True

            # Apply updated styling to navigation buttons
            for btn in [self.project_hub_btn, self.process_btn, self.trainable_btn,
                       self.point_counting_btn, self.grain_analysis_btn,
                       self.advanced_segmentation_btn, self.analysis_btn,
                       self.batch_processing_btn, self.ai_assistant_btn, self.settings_btn, self.about_btn]:
                if btn:
                    # Get theme-aware colors
                    if is_dark:
                        nav_btn_bg = QColor(self.palette().color(QPalette.Window)).name()
                        nav_btn_text = QColor(self.palette().color(QPalette.WindowText)).name()
                        nav_btn_hover_bg = QColor(self.palette().color(QPalette.AlternateBase)).name()
                        nav_btn_hover_text = QColor(self.palette().color(QPalette.BrightText)).name()
                        nav_btn_pressed_bg = QColor(self.palette().color(QPalette.Button)).darker(120).name()
                        nav_btn_checked_bg = QColor(self.palette().color(QPalette.Highlight)).name()
                        nav_btn_checked_text = QColor(self.palette().color(QPalette.HighlightedText)).name()
                        nav_btn_checked_border = QColor(self.palette().color(QPalette.Highlight)).name()
                    else:
                        nav_btn_bg = QColor(self.palette().color(QPalette.Window)).name()
                        nav_btn_text = QColor(self.palette().color(QPalette.WindowText)).name()
                        nav_btn_hover_bg = "#007acc"  # Use a darker blue background for better contrast
                        nav_btn_hover_text = "#ffffff"  # White text/icons on hover for light theme
                        nav_btn_pressed_bg = QColor(self.palette().color(QPalette.Button)).darker(110).name()
                        nav_btn_checked_bg = QColor(self.palette().color(QPalette.Highlight)).name()
                        nav_btn_checked_text = QColor(self.palette().color(QPalette.HighlightedText)).name()
                        nav_btn_checked_border = QColor(self.palette().color(QPalette.Highlight)).name()

                    # Apply modern vertical navigation styling with larger icon space
                    btn.setStyleSheet(f"""
                        QPushButton {{
                            background-color: transparent;
                            color: {nav_btn_text};
                            border: none;
                            padding: 4px;
                            font-size: 10px;
                            font-weight: 500;
                            margin: 2px;
                            border-radius: 8px;
                            text-align: center;
                        }}
                        QPushButton:hover {{
                            background-color: {nav_btn_hover_bg};
                            color: {nav_btn_hover_text};
                            border-radius: 8px;
                        }}
                        QPushButton:pressed {{
                            background-color: {nav_btn_pressed_bg};
                        }}
                        QPushButton:checked {{
                            background-color: {nav_btn_checked_bg};
                            color: {nav_btn_checked_text};
                            font-weight: bold;
                            border-left: 4px solid {nav_btn_checked_border};
                            border-radius: 0px 8px 8px 0px;
                        }}
                    """)

        except Exception as e:
            print(f"Error updating navigation button styling: {e}")

    def update_tab_styling(self):
        """Updates the tab styling based on the current theme."""
        # Get the current theme from settings
        try:
            from PySide6.QtCore import QSettings
            settings = QSettings("VisionLab Ai", "VisionLab_Ai_V4")
            theme = settings.value("app/theme", "Dark Theme").lower()
            is_dark = "dark" in theme
            
            # Get font size from settings
            font_size_name = settings.value("app/font_size", "normal")
            try:
                from src.gui.styles.theme_config import FONT_SIZES
                font_size = FONT_SIZES.get(font_size_name, 10)
            except ImportError:
                # Default font sizes if import fails
                font_sizes = {"small": 8, "normal": 10, "medium": 12, "large": 14, "extra-large": 16}
                font_size = font_sizes.get(font_size_name, 10)
        except Exception:
            # Default to dark theme if there's an error
            is_dark = True
            font_size = 10

        # Define colors based on theme
        if is_dark:
            # Dark theme colors (using palette for more dynamic theming)
            # bg_color = "#1e1e1e" # Old hardcoded
            bg_color = QColor(self.palette().color(QPalette.Window)).name()
            border_color = QColor(self.palette().color(QPalette.Mid)).name()
            text_color = QColor(self.palette().color(QPalette.WindowText)).name()
            hover_bg_color = QColor(self.palette().color(QPalette.AlternateBase)).name() # Example, adjust as needed
            hover_text_color = QColor(self.palette().color(QPalette.HighlightedText)).name()
            selected_bg_color = QColor(self.palette().color(QPalette.Highlight)).name()
            selected_text_color = QColor(self.palette().color(QPalette.HighlightedText)).name()
            selected_border_color = QColor(self.palette().color(QPalette.Highlight)).name()
        else:
            # Light theme colors
            bg_color = "#f0f0f0"
            border_color = "#d0d0d0"
            text_color = "#333333"
            hover_bg_color = "#e0e0e0"
            hover_text_color = "#000000"
            selected_bg_color = "#007acc"  # Updated to the requested blue color
            selected_text_color = "white"
            selected_border_color = "#007acc"  # Updated to the requested blue color

        # Get current tab position for position-specific styling
        current_position = self.stacked_widget.tabPosition()
        
        # Define padding for tab styling
        padding = 6  # Default padding value
        control_height = 28  # Default control height
        
        # Position-specific styling adjustments
        if current_position == QTabWidget.West:  # Left sidebar
            tab_padding = f"{padding}px {padding//2}px"
            tab_min_width = "120px"
            tab_min_height = f"{control_height}px"
            pane_margin = "left: 0px"
        elif current_position == QTabWidget.South:  # Bottom tabs
            tab_padding = f"{padding//2}px {padding*2}px"
            tab_min_width = "80px"
            tab_min_height = f"{control_height-4}px"
            pane_margin = "bottom: 0px"
        else:  # Top tabs (default)
            tab_padding = f"{padding}px {padding*2}px"
            tab_min_width = "80px"
            tab_min_height = f"{control_height-6}px"
            pane_margin = "top: 0px"

        # Apply theme-aware styling to the tab bar
        self.stacked_widget.setStyleSheet(f"""
            QTabWidget {{
                padding: 0px;
                margin: 0px;
                background-color: {bg_color};
            }}

            QTabWidget::pane {{
                border: 1px solid {border_color};
                {pane_margin};
                background-color: {bg_color};
                border-radius: 6px;
            }}

            QTabBar {{
                background-color: {bg_color};
                border: none;
            }}

            QTabBar::tab {{
                background-color: {bg_color};
                color: {text_color};
                border: 1px solid {border_color};
                padding: {tab_padding};
                margin: 1px;
                min-width: {tab_min_width};
                min-height: {tab_min_height};
                border-radius: 6px;
                font-size: {font_size}pt;
                font-weight: 500;
            }}

            QTabBar::tab:hover {{
                background-color: {hover_bg_color};
                color: {hover_text_color};
                border-color: {hover_bg_color};
                border-radius: 6px;
            }}

            QTabBar::tab:selected {{
                background-color: {selected_bg_color};
                color: {selected_text_color};
                border-color: {selected_border_color};
                font-weight: bold;
                border-radius: 6px;
            }}

            QTabBar::tab:!selected {{
                margin-top: 2px;
            }}
        """)

        # Refresh all icons to match the new theme
        self.refresh_all_icons()



    def create_title_bar(self, main_layout):
        """Creates a custom title bar for the frameless window."""
        # Create title bar frame
        title_bar = QFrame()
        title_bar.setFixedHeight(30)
        # Apply theme-aware styling to the title bar
        # This will be updated by the theme change handler if one exists
        # For now, let's set a default that can be overridden
        title_bar.setObjectName("CustomTitleBar")
        title_bar.setStyleSheet("""
            QFrame#CustomTitleBar {
                background-color: palette(window);
                border: none;
            }
        """)
        title_layout = QHBoxLayout(title_bar)
        title_layout.setContentsMargins(5, 0, 5, 0)
        title_layout.setSpacing(0)

        # Add window title
        title_label = QLabel("VisionLab Ai")
        title_label.setStyleSheet("color: #cccccc; font-weight: bold;")
        title_layout.addWidget(title_label)

        # Add spacer
        title_layout.addStretch()

        # Add window control buttons
        min_button = QPushButton("🗕")
        min_button.setFixedSize(30, 30)
        min_button.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                color: #cccccc;
                border: none;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #333333;
            }
            QPushButton:pressed {
                background-color: #444444;
            }
        """)
        min_button.clicked.connect(self.showMinimized)

        max_button = QPushButton("🗖")
        max_button.setFixedSize(30, 30)
        max_button.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                color: #cccccc;
                border: none;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #333333;
            }
            QPushButton:pressed {
                background-color: #444444;
            }
        """)
        max_button.clicked.connect(self.toggle_maximize)

        close_button = QPushButton("✕")
        close_button.setFixedSize(30, 30)
        close_button.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                color: #cccccc;
                border: none;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #e81123;
                color: white;
            }
            QPushButton:pressed {
                background-color: #f1707a;
            }
        """)
        close_button.clicked.connect(self.close)

        title_layout.addWidget(min_button)
        title_layout.addWidget(max_button)
        title_layout.addWidget(close_button)

        # Add title bar to main layout
        main_layout.addWidget(title_bar)

        # Store reference to title bar for mouse events
        self.title_bar = title_bar

        # Install event filter for mouse events
        title_bar.mousePressEvent = self.title_bar_mouse_press
        title_bar.mouseMoveEvent = self.title_bar_mouse_move
        title_bar.mouseDoubleClickEvent = self.title_bar_double_click

    def title_bar_mouse_press(self, event):
        """Handle mouse press events on the title bar."""
        if event.button() == Qt.LeftButton:
            self.drag_position = event.globalPosition().toPoint() - self.frameGeometry().topLeft()
            event.accept()

    def title_bar_mouse_move(self, event):
        """Handle mouse move events on the title bar."""
        if hasattr(self, 'drag_position') and event.buttons() == Qt.LeftButton:
            self.move(event.globalPosition().toPoint() - self.drag_position)
            event.accept()

    def title_bar_double_click(self, event):
        """Handle double click events on the title bar."""
        self.toggle_maximize()
        event.accept()

    def toggle_maximize(self):
        """Toggle between maximized and normal window state."""
        if self.isMaximized():
            self.showNormal()
        else:
            self.showMaximized()

    def toggle_navigation_bar(self):
        """Toggles the visibility of the navigation bar."""
        if hasattr(self, 'nav_bar'):
            self.nav_bar.setVisible(not self.nav_bar.isVisible())
    
    def connect_navigation_buttons(self):
        """Connects navigation buttons to tab switching functionality."""
        # Define button to tab mapping
        button_tab_mapping = {
            self.project_hub_btn: "Project Hub",
            self.process_btn: "Unsupervised Segmentation",
            self.trainable_btn: "Trainable Segmentation",
            self.point_counting_btn: "Point Counting",
            self.grain_analysis_btn: "Grain Analysis",
            self.advanced_segmentation_btn: "Advanced Segmentation",
            self.analysis_btn: "Image Lab",
            self.batch_processing_btn: "Batch Processing",
            self.settings_btn: "Settings",
            self.ai_assistant_btn: "AI Assistant",
            self.about_btn: "About"
        }
        
        # Connect each button to switch to its corresponding tab
        for button, tab_name in button_tab_mapping.items():
            button.clicked.connect(lambda checked, name=tab_name: self.switch_to_tab_by_name(name))
    
    def switch_to_tab_by_name(self, tab_name):
        """Switches to a tab by its name and updates navigation button states."""
        # Find the tab index by name
        for i in range(self.stacked_widget.count()):
            if self.stacked_widget.tabText(i) == tab_name:
                self.stacked_widget.setCurrentIndex(i)
                self.update_navigation_button_states(tab_name)
                break
    
    def update_navigation_button_states(self, active_tab_name):
        """Updates the checked state of navigation buttons based on the active tab."""
        button_tab_mapping = {
            "Project Hub": self.project_hub_btn,
            "Unsupervised Segmentation": self.process_btn,
            "Trainable Segmentation": self.trainable_btn,
            "Point Counting": self.point_counting_btn,
            "Grain Analysis": self.grain_analysis_btn,
            "Advanced Segmentation": self.advanced_segmentation_btn,
            "Image Lab": self.analysis_btn,
            "Batch Processing": self.batch_processing_btn,
            "Settings": self.settings_btn,
            "AI Assistant": self.ai_assistant_btn,
            "About": self.about_btn
        }
        
        # Update button states
        for tab_name, button in button_tab_mapping.items():
            if button:
                button.setChecked(tab_name == active_tab_name)