#!/usr/bin/env python3
"""
Test runner for grain deletion optimization tests.

Usage:
    python tests/run_optimization_tests.py
"""

import sys
import os

# Add project root to path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

def main():
    """Run the optimization tests."""
    print("Starting Grain Deletion Optimization Tests...")
    print(f"Project root: {project_root}")
    
    try:
        # Import and run the performance tests
        from tests.performance.test_grain_deletion_optimization import run_performance_tests
        
        success = run_performance_tests()
        
        if success:
            print("\n🎉 All optimization tests passed!")
            print("\nOptimization Summary:")
            print("✓ Data consistency maintained after deletions")
            print("✓ Performance improvements verified for large datasets")
            print("✓ Batch processing optimizations working")
            print("✓ Performance monitoring functional")
            print("✓ Memory efficiency within acceptable limits")
        else:
            print("\n❌ Some tests failed. Please review the output above.")
            
        return success
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Make sure all dependencies are installed and the project structure is correct.")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
