# Welcome to the **Unsupervised Segmentation** page of VisionLab AI V4! 

This tutorial will guide you through each section of the interface, explaining how to use the tools and settings available for unsupervised image segmentation.

## Table of Contents

[Overview](#overview)  
[Image Gallery](#image-gallery)  
[Segmentation Parameters](#segmentation-parameters)  
[Viewing Results](#viewing-results)  
[Segments and Colors](#segments-and-colors)  
[Merge Action](#merge-action)  
[Color Palette](#color-palette)  
[Post-processing](#post-processing)  
[Export & Save Options](#export--save-options)  

---

## Overview

The Unsupervised Segmentation page in VisionLab AI V4 uses a **hybrid machine learning approach** to segment your images without requiring any manual labels. This method combines traditional clustering (such as KMeans, PCA+KMeans, or Felzenszwalb superpixels) with a lightweight Convolutional Neural Network (CNN) that is trained on-the-fly for each image.

### How it works:

1. **Initial Segmentation**: The image is first segmented into superpixels using a classic unsupervised method (KMeans, PCA+KMeans, or Felzenszwalb). This produces an over-segmentation that acts as a pseudo-label map.

2. **CNN Refinement**: A small CNN is then trained for a few epochs using these superpixels as training targets. The CNN learns to refine and smooth the segmentation, producing a more coherent and visually accurate result.

This hybrid strategy leverages the strengths of both clustering and deep learning, providing robust and adaptive segmentation for a wide range of image types.

---

## Image Gallery

**Manage the set of images to be segmented.**

- **Add Images**: Use the add button to include new images in your gallery
- **Remove Images**: Select and remove unwanted images from the gallery
- **Preview**: Click on any image to preview it before segmentation
- **Clear All**: Use the **Clear All** button to remove all images at once
- **Batch Processing**: Load multiple images for efficient batch segmentation

---

## Segmentation Parameters

The Segmentation Parameters section lets you control both the initial clustering and the CNN refinement stages of the hybrid unsupervised segmentation algorithm. Options are grouped into Basic and Advanced settings for flexibility and clarity.

**Basic Parameters:**
- **Method:** Choose the algorithm for initial over-segmentation (superpixels). Options: KMeans, Felzenszwalb, or PCA+KMeans. This determines the pseudo-labels used to guide CNN training.
- **Training Epochs:** Number of epochs to train the CNN refinement model. Higher values may yield better results but increase processing time.
- **Model Dimension 1 & 2:** Set the width (number of channels) for the CNN’s internal layers. Larger values increase model capacity and computation.
- **Min/Max Label Number:** Define the range for the number of segments (clusters) created in the initial over-segmentation.
- **Target Width/Height:** Resize the input image for segmentation. Higher values preserve more detail but require more memory and computation.
- **Learning Rate:** Initial learning rate for CNN training. Influences how quickly the model updates its weights.

**Advanced Options:** (expandable section)
- **Optimizer:** Select the optimization algorithm for CNN training. Choices: SGD, Adam, AdamW, RMSprop. AdamW is recommended for most cases due to its decoupled weight decay and robust convergence.
- **Adaptive Learning Rate:** If enabled, the learning rate is automatically adjusted during training for improved convergence. If disabled, the learning rate remains fixed at your specified value.
- **Momentum:** (SGD only) Sets the momentum factor, which helps accelerate training and smooth out updates. Not used with Adam, AdamW, or RMSprop.
- **Weight Decay:** Applies L2 regularization to the CNN weights, helping prevent overfitting. Especially relevant for AdamW optimizer.
- **Enable Gradient Clipping:** When enabled, restricts the maximum gradient norm during backpropagation to prevent instability. You can set the gradient clip value to control the threshold.

*How these affect segmentation:*
- **Basic parameters** influence both the initial pseudo-label generation (clustering) and the capacity/speed of the CNN refinement.
- **Advanced options** control the details of CNN training. For most users, defaults are robust, but advanced users can fine-tune optimizer behavior, learning rate adaptation, regularization, and stability mechanisms for challenging datasets or specific segmentation needs.

If you’re unsure, leave advanced options at their defaults. They are designed to provide high-quality results across a wide range of images.

---

## Viewing Results
- **Original Image:** Shows the raw input image.
- **Segmented Image:** Displays the result of segmentation, with each segment colored distinctly.
- **Segment Grid View:** Shows individual segments or overlays for detailed inspection.

---

## Segments & Colors
- **Purpose:** Review and manage detected segments.
- **Features:**
  - List of segments with color swatches and percentage coverage.
  - Select segments to merge or highlight.

---

## Merge Action
- **Purpose:** Combine multiple segments into one.
- **How to Use:**
  - Select segments from the list.
  - Click `Merge Selected Segments` to combine them.

---

## Color Palette
- **Purpose:** Customize the appearance of segmented regions.
- **Features:**
  - Choose from predefined palettes (e.g., `tab20`).
  - Click `Apply Color Palette` to update the display.

---

## Post-processing
- **Purpose:** Refine and customize segmentation results.
- **Features:**
  - `Pick Colors`: Manually assign colors to segments.
  - `Save Custom Palette`: Save your current color choices.
  - `Manage Palettes`: Edit or switch between saved palettes.
  - `Select Segments to Display`: Show/hide specific segments.
  - `Show Full Segmentation`: View the complete segmentation overlay.

---

## Export & Save Options
- **Save Segmented Image:** Export the current segmented result as an image file.
- **Export to COCO Format:** Save segmentation results in the COCO annotation format for machine learning tasks.
- **Export Segments as Annotations:** Generate annotation files for further analysis or training.

---

This tutorial covers all major sections of the Unsupervised Segmentation page. For questions or further help, refer to the in-app help or contact support.
