# ai_assistant_page.py
# UI page for the AI Assistant interface

import os
import logging
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QComboBox, QLabel, QTextEdit,
    QPushButton, QGraphicsView, QGraphicsScene, QGraphicsPixmapItem,
    QTableWidget, QTableWidgetItem, QHeaderView, QSplitter, QGraphicsRectItem,
    QGraphicsSimpleTextItem, QSizePolicy, QMessageBox, QApplication
)
from PySide6.QtGui import QPixmap, QPen, QColor, QBrush, QFont, QIcon, QPainter
from PySide6.QtCore import Qt, Signal, Slot, QDateTime
from src.gemini.prompts import DEFAULT_PROMPTS
from src.gemini.gemini_worker import GeminiWorker
from src.ui.widgets.settings_dialog import SettingsDialog # For easy key access

logger = logging.getLogger(__name__)

# Define some colors for bounding boxes
BOX_COLORS = [
    QColor("red"), QColor("lime"), QColor("blue"), QColor("yellow"),
    QColor("cyan"), QColor("magenta"), QColor("orange"), QColor("purple")
]

class AIAssistantPage(QWidget):
    """UI Page for the AI Assistant feature."""

    # Signal to request status bar updates from main window
    status_message_requested = Signal(str, int) # message, timeout_ms

    def __init__(self, project, parent=None):
        super().__init__(parent)
        self.project = project
        self.current_image_id = None
        self.current_pixmap = None
        self.api_key = SettingsDialog.get_api_key() # Get initial key

        # Gemini Worker Thread
        self.gemini_worker = GeminiWorker(self.api_key)
        self.gemini_worker.result_ready.connect(self._handle_text_result)
        self.gemini_worker.structured_data_ready.connect(self._handle_structured_data)
        self.gemini_worker.bounding_boxes_ready.connect(self._handle_bounding_boxes)
        self.gemini_worker.error_occurred.connect(self._handle_analysis_error)
        self.gemini_worker.status_update.connect(self._update_status)
        self.gemini_worker.finished.connect(self._on_analysis_finished)

        # No loading indicator needed - we'll use the cursor instead

        self._init_ui()
        self._connect_signals()
        self.refresh_image_list() # Populate initially

    def _init_ui(self):
        main_layout = QVBoxLayout(self)

        # --- Top Control Bar ---
        control_bar_layout = QHBoxLayout()
        control_bar_layout.setSpacing(10)
        
        # Image selector with icon
        image_label = QLabel()
        image_label.setPixmap(QIcon(":/icons/image_icon.png").pixmap(16, 16))
        control_bar_layout.addWidget(image_label)
        self.image_selector = QComboBox()
        self.image_selector.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        self.image_selector.setMinimumWidth(200)
        control_bar_layout.addWidget(self.image_selector)

        # Prompt template selector with icon
        prompt_label = QLabel()
        prompt_label.setPixmap(QIcon(":/icons/template_icon.png").pixmap(16, 16))
        control_bar_layout.addWidget(prompt_label)
        self.prompt_selector = QComboBox()
        self.prompt_selector.addItems(DEFAULT_PROMPTS.keys())
        self.prompt_selector.setMinimumWidth(200)
        control_bar_layout.addWidget(self.prompt_selector)

        # Action buttons with improved styling
        self.analyze_button = QPushButton(QIcon(":/icons/analyze_icon.png"), " Analyze")
        self.analyze_button.setToolTip("Start analysis with the selected image and prompt")
        self.analyze_button.setStyleSheet("padding: 5px 10px;")
        control_bar_layout.addWidget(self.analyze_button)

        self.cancel_button = QPushButton(QIcon(":/icons/cancel_icon.png"), " Cancel")
        self.cancel_button.setToolTip("Cancel ongoing analysis")
        self.cancel_button.setEnabled(False)
        self.cancel_button.setStyleSheet("padding: 5px 10px;")
        control_bar_layout.addWidget(self.cancel_button)

        # Add loading indicator
        self.loading_indicator = QLabel()
        self.loading_indicator.setPixmap(QIcon(":/icons/loading_icon.png").pixmap(16, 16))
        self.loading_indicator.setVisible(False)
        control_bar_layout.addWidget(self.loading_indicator)

        main_layout.addLayout(control_bar_layout)

        # --- Main Content Area (Splitter) ---
        self.splitter = QSplitter(Qt.Orientation.Horizontal)

        # Left Side: Image Viewer + Prompt Editor
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        left_layout.setContentsMargins(0,0,0,0)

        # Image viewer toolbar with zoom controls
        image_toolbar = QHBoxLayout()
        image_toolbar.setContentsMargins(0, 0, 0, 5)

        # Add a label for the zoom controls
        zoom_label = QLabel("Zoom:")
        image_toolbar.addWidget(zoom_label)

        # Zoom in button
        self.zoom_in_button = QPushButton("+")
        self.zoom_in_button.setToolTip("Zoom In (+ or =)")
        self.zoom_in_button.setFixedSize(30, 30)
        self.zoom_in_button.clicked.connect(self._zoom_in)
        self.zoom_in_button.setEnabled(False)  # Initially disabled until image is loaded
        image_toolbar.addWidget(self.zoom_in_button)

        # Zoom out button
        self.zoom_out_button = QPushButton("-")
        self.zoom_out_button.setToolTip("Zoom Out (-)")
        self.zoom_out_button.setFixedSize(30, 30)
        self.zoom_out_button.clicked.connect(self._zoom_out)
        self.zoom_out_button.setEnabled(False)  # Initially disabled until image is loaded
        image_toolbar.addWidget(self.zoom_out_button)

        # Fit to view button
        self.fit_view_button = QPushButton("⤢")
        self.fit_view_button.setToolTip("Fit to View (0)")
        self.fit_view_button.setFixedSize(30, 30)
        self.fit_view_button.clicked.connect(self._fit_to_view)
        self.fit_view_button.setEnabled(False)  # Initially disabled until image is loaded
        image_toolbar.addWidget(self.fit_view_button)

        # Add spacer to push buttons to the left
        image_toolbar.addStretch()

        # Add toolbar to layout
        left_layout.addLayout(image_toolbar)

        self.image_viewer = QGraphicsView()
        self.image_scene = QGraphicsScene(self)
        self.image_viewer.setScene(self.image_scene)
        self.image_viewer.setRenderHint(QPainter.RenderHint.Antialiasing)
        self.image_viewer.setDragMode(QGraphicsView.DragMode.ScrollHandDrag) # Allow panning
        self.image_viewer.setTransformationAnchor(QGraphicsView.ViewportAnchor.AnchorUnderMouse)
        self.image_viewer.setResizeAnchor(QGraphicsView.ViewportAnchor.AnchorViewCenter)
        left_layout.addWidget(self.image_viewer, 1) # Give more stretch factor

        left_layout.addWidget(QLabel("Prompt:"))
        self.prompt_input = QTextEdit()
        self.prompt_input.setPlaceholderText("Select a template or enter your custom prompt here.")
        self.prompt_input.setFixedHeight(150) # Adjust height as needed
        left_layout.addWidget(self.prompt_input)

        # Right Side: Results Display
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)
        right_layout.setContentsMargins(0,0,0,0)
        right_layout.setSpacing(5)

        # Results tabs with improved styling
        self.results_tabs = QTabWidget()
        
        # Text results tab
        self.text_results_display = QTextEdit()
        self.text_results_display.setReadOnly(True)
        self.text_results_display.setPlaceholderText("Analysis results will appear here...")
        self.text_results_display.setStyleSheet("font-family: monospace;")
        self.results_tabs.addTab(self.text_results_display, "Text Results")

        # Structured data tab
        table_container = QWidget()
        table_layout = QVBoxLayout(table_container)
        table_layout.setContentsMargins(0,0,0,0)
        
        self.structured_data_table = QTableWidget()
        self.structured_data_table.setColumnCount(3) # Label, Shape, Description
        self.structured_data_table.setHorizontalHeaderLabels(["Label", "Shape", "Description"])
        self.structured_data_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeMode.Stretch)
        self.structured_data_table.verticalHeader().setVisible(False)
        self.structured_data_table.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)
        self.structured_data_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.structured_data_table.setStyleSheet("alternate-background-color: #f5f5f5;")
        
        table_layout.addWidget(self.structured_data_table)
        self.results_tabs.addTab(table_container, "Structured Data")
        
        right_layout.addWidget(self.results_tabs)


        self.splitter.addWidget(left_panel)
        self.splitter.addWidget(right_panel)
        self.splitter.setSizes([600, 400]) # Initial size ratio for left/right panels

        main_layout.addWidget(self.splitter, 1) # Give splitter stretch factor

        # Status Label (optional, can also use main window status bar)
        self.status_label = QLabel("Ready.")
        main_layout.addWidget(self.status_label)

    def _connect_signals(self):
        self.image_selector.currentIndexChanged.connect(self._on_image_selected)
        self.prompt_selector.currentIndexChanged.connect(self._on_prompt_template_selected)
        self.analyze_button.clicked.connect(self._start_analysis)
        self.cancel_button.clicked.connect(self._cancel_analysis)
        self.structured_data_table.itemSelectionChanged.connect(self._highlight_bbox_from_table)

    def refresh_image_list(self):
        """Populates the image selector combobox."""
        current_selection = self.image_selector.currentText()
        self.image_selector.blockSignals(True) # Avoid triggering selection change yet
        self.image_selector.clear()
        ids = self.project.get_image_ids()
        if not ids:
             self.image_selector.addItem("No images in project")
             self.image_selector.setEnabled(False)
             self.analyze_button.setEnabled(False)
        else:
            self.image_selector.addItems(ids)
            self.image_selector.setEnabled(True)
            # Try to restore previous selection
            index = self.image_selector.findText(current_selection)
            if index != -1:
                self.image_selector.setCurrentIndex(index)
            else:
                self.image_selector.setCurrentIndex(0) # Select first image if previous not found
            self._on_image_selected(self.image_selector.currentIndex()) # Manually trigger update for the initial/restored item

        self.image_selector.blockSignals(False)


    @Slot(int)
    def _on_image_selected(self, index):
        """Handles image selection change."""
        if index < 0:
             self.current_image_id = None
             self._clear_displays()
             return

        self.current_image_id = self.image_selector.currentText()
        logger.info(f"Image selected: {self.current_image_id}")
        self._clear_displays() # Clear previous results/image
        self._load_image_display()
        self._load_saved_state()
        self._load_saved_analysis() # Try loading previous results

    def _load_image_display(self):
        """Loads and displays the selected image."""
        self.image_scene.clear() # Clear previous items
        self.current_pixmap = None
        if not self.current_image_id: return

        image_path = self.project.get_image_path(self.current_image_id)
        if image_path and os.path.exists(image_path):
            self.current_pixmap = QPixmap(image_path)
            if not self.current_pixmap.isNull():
                pixmap_item = QGraphicsPixmapItem(self.current_pixmap)
                self.image_scene.addItem(pixmap_item)
                self.image_scene.setSceneRect(pixmap_item.boundingRect()) # Fit scene to image
                self.image_viewer.fitInView(pixmap_item, Qt.AspectRatioMode.KeepAspectRatio) # Zoom to fit
                self.analyze_button.setEnabled(True)
                self._update_status(f"Loaded image: {os.path.basename(image_path)}")

                # Make sure zoom buttons are enabled
                self.zoom_in_button.setEnabled(True)
                self.zoom_out_button.setEnabled(True)
                self.fit_view_button.setEnabled(True)
            else:
                logger.error(f"Failed to load image: {image_path}")
                self.image_scene.addItem(QGraphicsSimpleTextItem(f"Error loading:\n{os.path.basename(image_path)}"))
                self.analyze_button.setEnabled(False)
                self._update_status(f"Error loading image: {os.path.basename(image_path)}")
        else:
            logger.warning(f"Image path not found for ID: {self.current_image_id}")
            self.image_scene.addItem(QGraphicsSimpleTextItem(f"Image not found:\n{self.current_image_id}"))
            self.analyze_button.setEnabled(False)
            self._update_status(f"Image not found: {self.current_image_id}")

    def _load_saved_state(self):
        """Loads saved UI state (e.g., last prompt)."""
        if not self.current_image_id: return
        state = self.project.load_ai_assistant_state(self.current_image_id)
        if state:
            prompt_text = state.get("last_prompt", "")
            template_name = state.get("last_template", list(DEFAULT_PROMPTS.keys())[0]) # Default to first

            self.prompt_input.setText(prompt_text)
            index = self.prompt_selector.findText(template_name)
            if index != -1:
                self.prompt_selector.setCurrentIndex(index)
            logger.info(f"Loaded saved state for {self.current_image_id}")

    def _save_current_state(self):
        """Saves the current UI state."""
        if not self.current_image_id: return
        state = {
            "last_prompt": self.prompt_input.toPlainText(),
            "last_template": self.prompt_selector.currentText()
        }
        self.project.save_ai_assistant_state(self.current_image_id, state)


    def _load_saved_analysis(self):
        """Loads and displays previously saved analysis results."""
        if not self.current_image_id: return
        results = self.project.load_ai_analysis_results(self.current_image_id)
        if results:
            logger.info(f"Loading saved analysis for {self.current_image_id}")
            self._handle_text_result(results.get("text_result", "No saved text result."))
            structured = results.get("structured_data", [])
            self._handle_structured_data(structured) # Populates table
            self._handle_bounding_boxes(self._extract_bboxes(structured)) # Draws boxes
            self._update_status(f"Loaded saved analysis for {self.current_image_id}")


    @Slot(int)
    def _on_prompt_template_selected(self, _):
        """Updates the prompt input when a template is selected."""
        template_name = self.prompt_selector.currentText()
        prompt_text = DEFAULT_PROMPTS.get(template_name, "")
        is_custom = (template_name == "Custom Prompt")

        self.prompt_input.setReadOnly(not is_custom)
        if not is_custom:
            self.prompt_input.setText(prompt_text)
        else:
            # Don't clear custom prompt if user switches back to it
            if not self.prompt_input.toPlainText() or self.prompt_input.toPlainText() in DEFAULT_PROMPTS.values():
                 self.prompt_input.setPlaceholderText("Enter your custom prompt here...")
                 # self.prompt_input.clear() # Optionally clear it

    def _clear_displays(self):
        """Clears image, results, and table."""
        # Keep image displayed until new one selected
        # self.image_scene.clear()
        # self.current_pixmap = None
        self.text_results_display.clear()
        self.structured_data_table.setRowCount(0)
        # Clear previous bounding boxes but keep image
        items_to_remove = [item for item in self.image_scene.items() if not isinstance(item, QGraphicsPixmapItem)]
        for item in items_to_remove:
            self.image_scene.removeItem(item)

        # If no image is loaded, disable zoom buttons
        if not self.current_pixmap:
            self.zoom_in_button.setEnabled(False)
            self.zoom_out_button.setEnabled(False)
            self.fit_view_button.setEnabled(False)


    def _start_analysis(self):
        """Starts the Gemini analysis process."""
        if self.gemini_worker.isRunning():
            QMessageBox.warning(self, "Analysis Running", "An analysis is already in progress.")
            return

        self.api_key = SettingsDialog.get_api_key() # Re-check key before starting
        if not self.api_key:
             QMessageBox.critical(self, "API Key Missing",
                                 "Cannot start analysis. Please set your Google Gemini API Key in Settings.")
             # Optionally open settings dialog: SettingsDialog(self).exec()
             return

        if not self.current_image_id or not self.current_pixmap:
            QMessageBox.warning(self, "No Image", "Please select a valid image first.")
            return

        image_path = self.project.get_image_path(self.current_image_id)
        prompt = self.prompt_input.toPlainText().strip()
        template_name = self.prompt_selector.currentText()

        if not prompt:
            QMessageBox.warning(self, "No Prompt", "Please select a template or enter a custom prompt.")
            return

        # Determine if JSON output is expected
        request_json = "JSON" in template_name

        logger.info(f"Starting analysis for: {self.current_image_id}")
        logger.info(f"Prompt template: {template_name}")
        logger.info(f"Request JSON: {request_json}")

        self._clear_displays() # Clear previous results before starting new analysis
        self._save_current_state() # Save prompt used

        self.gemini_worker.set_api_key(self.api_key) # Update worker key if changed
        self.gemini_worker.set_task(image_path, prompt, request_json)

        # Update UI state
        self.analyze_button.setEnabled(False)
        self.cancel_button.setEnabled(True)

        # Set waiting cursor
        QApplication.setOverrideCursor(Qt.CursorShape.WaitCursor)

        self.gemini_worker.start() # Start the thread

    def _cancel_analysis(self):
        """Requests cancellation of the ongoing analysis."""
        if self.gemini_worker.isRunning():
            self.gemini_worker.cancel()

            # Restore normal cursor
            QApplication.restoreOverrideCursor()

            self._update_status("Attempting to cancel analysis...")
            # Button state will be reset in _on_analysis_finished

    @Slot(str)
    def _handle_text_result(self, text):
        """Displays the text result from Gemini."""
        self.text_results_display.setText(text)

    @Slot(list)
    def _handle_structured_data(self, data):
        """Populates the table with structured data."""
        self.structured_data_table.setRowCount(0) # Clear previous rows
        if not isinstance(data, list):
            logger.warning(f"Received non-list structured data: {type(data)}")
            return

        self.structured_data_table.setRowCount(len(data))
        for row, item in enumerate(data):
            if not isinstance(item, dict): continue # Skip invalid items

            label = str(item.get("label", "N/A"))
            attributes = item.get("attributes", {})
            shape = str(attributes.get("shape", "-"))
            description = str(attributes.get("description", "-"))

            self.structured_data_table.setItem(row, 0, QTableWidgetItem(label))
            self.structured_data_table.setItem(row, 1, QTableWidgetItem(shape))
            self.structured_data_table.setItem(row, 2, QTableWidgetItem(description))

            # Store the bounding box data with the row for later use (highlighting)
            if "box_2d" in item:
                 self.structured_data_table.item(row, 0).setData(Qt.ItemDataRole.UserRole, item["box_2d"])


        self.structured_data_table.resizeRowsToContents()


    def _extract_bboxes(self, structured_data):
         """Helper to extract only bounding box items."""
         if not isinstance(structured_data, list): return []
         return [item for item in structured_data if isinstance(item, dict) and "box_2d" in item and "label" in item]


    @Slot(list)
    def _handle_bounding_boxes(self, boxes_data):
        """Draws bounding boxes on the image."""
        if not self.current_pixmap: return

        # Clear previous boxes but keep the image
        items_to_remove = [item for item in self.image_scene.items() if not isinstance(item, QGraphicsPixmapItem)]
        for item in items_to_remove:
            self.image_scene.removeItem(item)

        img_w = self.current_pixmap.width()
        img_h = self.current_pixmap.height()

        if img_w == 0 or img_h == 0: return # Avoid division by zero

        unique_labels = sorted(list(set(item.get("label", "unknown") for item in boxes_data)))
        label_colors = {label: BOX_COLORS[i % len(BOX_COLORS)] for i, label in enumerate(unique_labels)}

        font = QFont("Arial", 8) # Smaller font for labels

        for i, item in enumerate(boxes_data):
            box = item.get("box_2d")
            label = item.get("label", f"Box {i+1}")
            if isinstance(box, list) and len(box) == 4:
                try:
                    # Convert normalized coords [ymin, xmin, ymax, xmax] to image coords
                    ymin, xmin, ymax, xmax = map(float, box)
                    x = xmin * img_w
                    y = ymin * img_h
                    w = (xmax - xmin) * img_w
                    h = (ymax - ymin) * img_h

                    if w <= 0 or h <= 0: continue # Skip invalid boxes

                    color = label_colors.get(label, QColor("gray"))
                    pen = QPen(color, 2) # Slightly thicker pen
                    rect_item = QGraphicsRectItem(x, y, w, h)
                    rect_item.setPen(pen)
                    # Store data in the item for potential interaction later
                    rect_item.setData(Qt.ItemDataRole.UserRole, {"label": label, "box_2d": box})
                    self.image_scene.addItem(rect_item)

                    # Add label text near the box corner
                    text_item = QGraphicsSimpleTextItem(label)
                    text_item.setFont(font)
                    text_item.setBrush(QBrush(color.darker(150))) # Slightly darker text
                    text_item.setPos(x, y - 12 if y > 12 else y + h) # Position above or below
                    text_item.setZValue(1) # Ensure text is above rect
                     # Simple background for visibility
                    # text_item.setBrush(QBrush(QColor(255, 255, 255, 180))) # Semi-transparent white bg
                    self.image_scene.addItem(text_item)


                except (ValueError, TypeError) as e:
                    logger.warning(f"Skipping invalid bounding box data: {box} - {e}")


    @Slot()
    def _highlight_bbox_from_table(self):
        """Highlights the corresponding bounding box when a table row is selected."""
        selected_items = self.structured_data_table.selectedItems()
        if not selected_items: return # No selection

        selected_row = selected_items[0].row()
        # Retrieve the stored bounding box data
        box_data_item = self.structured_data_table.item(selected_row, 0) # Assumes stored in first col
        if not box_data_item: return

        box_2d = box_data_item.data(Qt.ItemDataRole.UserRole)
        if not box_2d: return # No box data associated with this row

        # Find the corresponding QGraphicsRectItem
        target_rect = None
        for item in self.image_scene.items():
            if isinstance(item, QGraphicsRectItem):
                item_data = item.data(Qt.ItemDataRole.UserRole)
                if isinstance(item_data, dict) and item_data.get("box_2d") == box_2d:
                    target_rect = item
                    break # Found it

        # Reset previous highlights
        for item in self.image_scene.items():
             if isinstance(item, QGraphicsRectItem):
                 pen = item.pen()
                 pen.setWidth(2) # Reset to default width
                 pen.setStyle(Qt.PenStyle.SolidLine)
                 item.setPen(pen)
                 item.setZValue(0) # Reset Z-order

        # Highlight the selected one
        if target_rect:
            pen = target_rect.pen()
            pen.setWidth(4) # Make it thicker
            pen.setStyle(Qt.PenStyle.DashLine) # Make it dashed
            target_rect.setPen(pen)
            target_rect.setZValue(1) # Bring to front
            # Ensure the highlighted box is visible
            self.image_viewer.ensureVisible(target_rect, 10, 10) # Add some margin

    @Slot(str)
    def _handle_analysis_error(self, error_message):
        """Displays an error message from the worker."""
        logger.error(f"AI Analysis Error: {error_message}")

        # Restore normal cursor before showing error message
        QApplication.restoreOverrideCursor()

        QMessageBox.critical(self, "AI Analysis Error", error_message)
        self._update_status(f"Error: {error_message[:100]}...") # Show truncated error in status

    @Slot(str)
    def _update_status(self, message):
        """Updates the status label and potentially the main window status bar."""
        self.status_label.setText(message)
        # Emit signal for main window status bar (optional)
        self.status_message_requested.emit(message, 3000) # Show for 3 seconds

    @Slot()
    def _on_analysis_finished(self):
        """Called when the worker thread finishes, successful or not."""
        self.analyze_button.setEnabled(True)
        self.cancel_button.setEnabled(False)

        # Restore normal cursor
        QApplication.restoreOverrideCursor()

        self._update_status("Analysis finished.") # Reset status

        # Save results if successful (check if data was received)
        if self.current_image_id and (self.text_results_display.toPlainText() or self.structured_data_table.rowCount() > 0):
             results_to_save = {
                 "text_result": self.text_results_display.toPlainText(),
                 "structured_data": [],
                 "timestamp": QDateTime.currentDateTime().toString(Qt.DateFormat.ISODate) # Add timestamp
             }
             # Reconstruct structured data from table for saving
             for row in range(self.structured_data_table.rowCount()):
                  label_item = self.structured_data_table.item(row, 0)
                  shape_item = self.structured_data_table.item(row, 1)
                  desc_item = self.structured_data_table.item(row, 2)
                  item_data = {
                      "label": label_item.text() if label_item else "N/A",
                      "attributes": {
                           "shape": shape_item.text() if shape_item else "-",
                           "description": desc_item.text() if desc_item else "-"
                           }
                      }
                  # Add box if present
                  if label_item and label_item.data(Qt.ItemDataRole.UserRole):
                      item_data["box_2d"] = label_item.data(Qt.ItemDataRole.UserRole)

                  results_to_save["structured_data"].append(item_data)

             self.project.save_ai_analysis_results(self.current_image_id, results_to_save)


    # --- Public methods / Event Handlers ---
    def update_api_key(self):
         """Called when API key might have changed in settings."""
         self.api_key = SettingsDialog.get_api_key()
         # No need to update worker immediately, it checks before running.
         logger.info("AI Assistant page noted API key update.")

    # --- Zoom Control Methods ---
    def _zoom_in(self):
        """Zoom in on the image by a fixed factor."""
        if not self.current_pixmap or not self.zoom_in_button.isEnabled():
            return
        self.image_viewer.scale(1.25, 1.25)

    def _zoom_out(self):
        """Zoom out on the image by a fixed factor."""
        if not self.current_pixmap or not self.zoom_out_button.isEnabled():
            return
        self.image_viewer.scale(0.8, 0.8)

    def _fit_to_view(self):
        """Fit the image to the view."""
        if not self.current_pixmap or not self.fit_view_button.isEnabled():
            return

        # Reset the view transformation
        self.image_viewer.resetTransform()

        # Find the pixmap item
        for item in self.image_scene.items():
            if isinstance(item, QGraphicsPixmapItem):
                self.image_viewer.fitInView(item, Qt.AspectRatioMode.KeepAspectRatio)
                break

    def keyPressEvent(self, event):
        """Handle keyboard shortcuts for zoom."""
        if event.key() == Qt.Key.Key_Plus or event.key() == Qt.Key.Key_Equal:
            self._zoom_in()
            event.accept()
        elif event.key() == Qt.Key.Key_Minus:
            self._zoom_out()
            event.accept()
        elif event.key() == Qt.Key.Key_0:
            self._fit_to_view()
            event.accept()
        else:
            super().keyPressEvent(event)

    def wheelEvent(self, event):
        """Handle mouse wheel zooming on the image viewer."""
        if self.image_viewer.underMouse():
            factor = 1.15 if event.angleDelta().y() > 0 else 1 / 1.15
            self.image_viewer.scale(factor, factor)
            event.accept()
        else:
            super().wheelEvent(event)

    def closeEvent(self, event):
         """Ensure worker thread is stopped cleanly on close."""
         if self.gemini_worker.isRunning():
              logger.info("Stopping Gemini worker thread...")
              self.gemini_worker.cancel()
              self.gemini_worker.quit()
              if not self.gemini_worker.wait(3000): # Wait 3 secs
                  logger.warning("Gemini worker did not stop gracefully, terminating.")
                  self.gemini_worker.terminate()
         super().closeEvent(event)
