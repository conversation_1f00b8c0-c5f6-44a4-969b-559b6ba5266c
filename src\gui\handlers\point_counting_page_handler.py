# src/gui/handlers/point_counting_page_handler.py

import os
import random
import csv
import numpy as np
import cv2
from datetime import datetime
from PySide6.QtCore import Qt, QTimer, QPoint, QDateTime, QObject
from PySide6.QtGui import QColor, QPixmap, QImage, QShortcut, QKeySequence, QIcon, QAction, QPainter, QPen
import logging # Add logging import
from PySide6.QtWidgets import QMessageBox, QInputDialog, QColorDialog, QFileDialog, QApplication, QProgressDialog, QProgressBar, QMenu, QFrame, QLabel

# Import feature extraction and classification modules
from src.utils.point_feature_extractor import PointFeatureExtractor
from src.utils.point_classifier import PointClassifier
from src.gui.dialogs.semi_auto_settings_dialog import SemiAutoSettingsDialog

from src.utils.session_state import SessionState # Assuming SessionState handles project context

logger = logging.getLogger(__name__) # Add logger initialization

class PointCountingPageHandler(QObject):
    """Handler for the redesigned Point Counting page functionality."""

    # Generic default class names for new images
    # These are only used when creating a new state for an image that hasn't been analyzed yet
    # Existing images with saved data will keep their class names and colors
    DEFAULT_CLASSES = [
        ("Class 1", QColor(255, 255, 0)),   # Yellow
        ("Class 2", QColor(255, 150, 150)), # Pinkish
        ("Class 3", QColor(150, 150, 150)), # Gray
        ("Class 4", QColor(0, 100, 255)),    # Blue
        ("Class 5", QColor(100, 200, 100)), # Greenish
    ]
    POINT_RADIUS = 5
    HIGHLIGHT_RADIUS = 8
    UNCLASSIFIED_INDEX = -1 # Use -1 for unclassified points

    # Constants for point classification method
    MANUAL_CLASSIFICATION = 0
    AUTO_CLASSIFICATION = 1

    # Constants for point visualization
    CURRENT_POINT_ALPHA = 0.5  # Transparency for the currently selected point
    BORDER_THICKNESS = 2     # Thickness of point border
    AUTO_INDICATOR_RADIUS = 7  # Radius of the auto-classification indicator circle

    def __init__(self, ui, project=None):
        super().__init__()  # Initialize the QObject parent class
        self.ui = ui
        self.session_state = SessionState() # Gets project context automatically if set globally
        self._project = None # Initialize the project reference

        # State per image (keyed by image_path)
        # Each entry will store: { 'points': [], 'classes': [], 'current_class_index': 0, ... }
        self.image_states = {}
        self.current_image_path = None
        self.current_image_data = None # Store the raw cv2 image data

        # Shared UI State
        self.counting_method = "grid"
        self.grid_size = 10
        self.random_points_count = 100
        self.random_seed = 42
        self.grid_offset_x = 0
        self.grid_offset_y = 0
        self.show_grid_lines = True
        self.auto_zoom_enabled = True  # Enable auto-zoom by default
        self.point_zoom_level = 2.0    # Default zoom level for points

        # Load grid color from settings
        try:
            from src.utils.settings_manager import settings_manager
            if settings_manager is not None:
                r = int(settings_manager.get_value("point_counting/grid_color_r", 255))
                g = int(settings_manager.get_value("point_counting/grid_color_g", 255))
                b = int(settings_manager.get_value("point_counting/grid_color_b", 255))
                a = int(settings_manager.get_value("point_counting/grid_color_a", 180))
                self.grid_color = QColor(r, g, b, a)
            else:
                self.grid_color = QColor(255, 255, 255, 180) # White with some transparency default
        except Exception as e:
            logger.error(f"Error loading grid color from settings: {e}")
            self.grid_color = QColor(255, 255, 255, 180) # White with some transparency default

        self.current_point_index = self.UNCLASSIFIED_INDEX # Index within the current image's points list

        # Classifier for export/import functionality
        self.trained_classifier = None  # Stores the last trained classifier
        self.loaded_classifier = None   # Stores an imported pre-trained classifier

        # Shared color dialog
        self.color_dialog = QColorDialog(self.ui.point_counting_page) # Parent to the page widget
        self.color_dialog.setOption(QColorDialog.ShowAlphaChannel, True)

        # Debounce timer for slider updates
        self.grid_opacity_timer = QTimer(self.ui.point_counting_page)
        self.grid_opacity_timer.setSingleShot(True)
        self.grid_opacity_timer.setInterval(150) # 150ms delay
        self.grid_opacity_timer.timeout.connect(self._update_display_after_opacity_change)

        self._setup_ui_defaults()
        self._connect_signals()
        self.classification_shortcuts = [] # Store classification shortcuts
        self._setup_keyboard_shortcuts()
        self._update_controls_for_method() # Set initial visibility

        # Set the project after initializing other attributes
        self.project = project # This will call the setter

    @property
    def project(self):
        return self._project

    @project.setter
    def project(self, value):
        """Update the project reference and reload state if needed."""
        old_project = self._project
        self._project = value

        # If we have a current image and the project changed, reload state
        if hasattr(self, 'current_image_path') and self.current_image_path and value and value != old_project:
            logger.info(f"Project reference updated, reloading state for {os.path.basename(self.current_image_path)}")
            self._load_state_from_project(self.current_image_path)

    def _setup_ui_defaults(self):
        """Initialize UI elements with default values."""
        # Grid Color Button
        self._update_color_button_style(self.ui.grid_color_button, self.grid_color)
        # Sliders/Spinners
        self.ui.grid_size_spinner.setValue(self.grid_size)
        self.ui.random_points_spinner.setValue(self.random_points_count)
        self.ui.random_seed_spinner.setValue(self.random_seed)
        self.ui.grid_offset_x_spinner.setValue(self.grid_offset_x)
        self.ui.grid_offset_y_spinner.setValue(self.grid_offset_y)

        # Set initial class selector frame style based on theme
        is_dark_theme = self._is_dark_theme()
        bg_color = "#2d2d2d" if is_dark_theme else "#f5f5f5"
        border_color = "#555555" if is_dark_theme else "#888888"
        text_color = "#e0e0e0" if is_dark_theme else "#333333"

        self.ui.class_selector_frame.setStyleSheet(f"""
            border: 1px solid {border_color};
            border-radius: 3px;
            background-color: {bg_color};
            color: {text_color};
        """)
        # Set cursor using Qt's native method instead of CSS
        self.ui.class_selector_frame.setCursor(Qt.PointingHandCursor)
        # Set tooltip to indicate scrolling functionality
        self.ui.class_selector_frame.setToolTip("Use mouse wheel to scroll through classes")

        # Set initial class display label style (removed hardcoded font styles)
        self.ui.class_display_label.setStyleSheet(f"""
            padding-left: 5px;
            color: {text_color};
        """)

        # Set initial class name label style (removed hardcoded font styles)
        self.ui.current_class_name_label.setStyleSheet(f"""
            padding: 8px;
            border-radius: 4px;
            background-color: {bg_color};
            color: {text_color};
            border: 2px solid {border_color};
        """)

        # Install event filter for mouse wheel class switching
        self.ui.class_selector_frame.installEventFilter(self)
        self.ui.class_display_label.installEventFilter(self)
        self.ui.show_class_dropdown_button.installEventFilter(self)

        # Load grid opacity from settings
        try:
            from src.utils.settings_manager import settings_manager
            if settings_manager is not None:
                opacity = int(settings_manager.get_value("point_counting/grid_opacity", 70))
                self.ui.grid_opacity_slider.setValue(opacity)
            else:
                self.ui.grid_opacity_slider.setValue(self.grid_color.alpha() * 100 // 255) # Set from alpha
        except Exception as e:
            logger.error(f"Error loading grid opacity from settings: {e}")
            self.ui.grid_opacity_slider.setValue(self.grid_color.alpha() * 100 // 255) # Set from alpha

        self.ui.show_grid_lines_checkbox.setChecked(self.show_grid_lines)
        self.ui.auto_zoom_checkbox.setChecked(self.auto_zoom_enabled)
        # Radio buttons
        self.ui.grid_counting_radio.setChecked(self.counting_method == "grid")
        self.ui.random_counting_radio.setChecked(self.counting_method == "random")
        self.ui.custom_counting_radio.setChecked(self.counting_method == "custom")
        # Disable controls initially
        self._set_controls_enabled(False)

    def _connect_signals(self):
        """Connect signals from UI elements to handler methods, ensuring no duplicates."""
        logger.debug("Connecting Point Counting signals...")

        # Check if we've already connected signals to avoid duplicates
        if hasattr(self, '_signals_connected') and self._signals_connected:
            return

        # Helper to disconnect safely
        def safe_disconnect(signal, slot):
            try:
                # Check if the signal is connected before attempting to disconnect
                signal.disconnect(slot)
            except (RuntimeError, TypeError):
                # RuntimeError if not connected, TypeError if signal doesn't exist
                # Silently ignore disconnection errors
                pass

        # --- Disconnect existing signals first ---
        safe_disconnect(self.ui.grid_counting_radio.toggled, self.on_counting_method_changed)
        safe_disconnect(self.ui.random_counting_radio.toggled, self.on_counting_method_changed)
        safe_disconnect(self.ui.custom_counting_radio.toggled, self.on_counting_method_changed)
        safe_disconnect(self.ui.grid_size_spinner.valueChanged, self.on_param_changed)
        safe_disconnect(self.ui.random_points_spinner.valueChanged, self.on_param_changed)
        safe_disconnect(self.ui.random_seed_spinner.valueChanged, self.on_param_changed)
        safe_disconnect(self.ui.grid_offset_x_spinner.valueChanged, self.on_param_changed)
        safe_disconnect(self.ui.grid_offset_y_spinner.valueChanged, self.on_param_changed)
        safe_disconnect(self.ui.show_grid_lines_checkbox.toggled, self.on_show_grid_lines_toggled)
        safe_disconnect(self.ui.grid_color_button.clicked, self.on_grid_color_clicked)
        safe_disconnect(self.ui.grid_opacity_slider.valueChanged, self.grid_opacity_timer.start)
        safe_disconnect(self.ui.add_class_button.clicked, self.on_add_class)
        safe_disconnect(self.ui.remove_class_button.clicked, self.on_remove_class)
        safe_disconnect(self.ui.rename_class_button.clicked, self.on_rename_class)
        safe_disconnect(self.ui.class_selector.currentIndexChanged, self.on_class_selected)

        # Disconnect the dropdown button if it exists
        if hasattr(self.ui, 'show_class_dropdown_button'):
            safe_disconnect(self.ui.show_class_dropdown_button.clicked, self.on_show_class_dropdown)

        safe_disconnect(self.ui.change_class_color_button.clicked, self.on_change_class_color)
        safe_disconnect(self.ui.show_class_selector_button.toggled, self.on_toggle_quick_selector)
        safe_disconnect(self.ui.vertical_class_selector.class_selected, self.on_floating_class_selected)
        safe_disconnect(self.ui.next_point_button.clicked, self.on_next_point)
        safe_disconnect(self.ui.prev_point_button.clicked, self.on_prev_point)
        safe_disconnect(self.ui.generate_points_button.clicked, self.on_generate_points)
        safe_disconnect(self.ui.clear_points_button.clicked, self.on_clear_points)
        safe_disconnect(self.ui.export_points_button.clicked, self.on_export_points)
        safe_disconnect(self.ui.export_classifier_button.clicked, self.on_export_classifier)
        safe_disconnect(self.ui.import_classifier_button.clicked, self.on_import_classifier)
        safe_disconnect(self.ui.use_pretrained_button.clicked, self.on_use_pretrained_classifier)
        safe_disconnect(self.ui.point_counting_gallery.image_clicked, self.on_gallery_image_clicked)
        safe_disconnect(self.ui.point_counting_image_view.mouse_pressed, self.on_image_view_mouse_press)

        # --- Reconnect signals ---
        # Method Selection
        self.ui.grid_counting_radio.toggled.connect(self.on_counting_method_changed)
        self.ui.random_counting_radio.toggled.connect(self.on_counting_method_changed)
        self.ui.custom_counting_radio.toggled.connect(self.on_counting_method_changed)

        # Parameters
        self.ui.grid_size_spinner.valueChanged.connect(self.on_param_changed)
        self.ui.random_points_spinner.valueChanged.connect(self.on_param_changed)
        self.ui.random_seed_spinner.valueChanged.connect(self.on_param_changed)
        self.ui.grid_offset_x_spinner.valueChanged.connect(self.on_param_changed)
        self.ui.grid_offset_y_spinner.valueChanged.connect(self.on_param_changed)
        self.ui.show_grid_lines_checkbox.toggled.connect(self.on_show_grid_lines_toggled)
        self.ui.grid_color_button.clicked.connect(self.on_grid_color_clicked)
        # Use timer for opacity slider to avoid excessive updates
        self.ui.grid_opacity_slider.valueChanged.connect(self.grid_opacity_timer.start)

        # Classes
        self.ui.add_class_button.clicked.connect(self.on_add_class)
        self.ui.remove_class_button.clicked.connect(self.on_remove_class)
        self.ui.rename_class_button.clicked.connect(self.on_rename_class) # Connect rename
        self.ui.class_selector.currentIndexChanged.connect(self.on_class_selected)

        # Connect the dropdown button to show the class selector dropdown
        if hasattr(self.ui, 'show_class_dropdown_button'):
            self.ui.show_class_dropdown_button.clicked.connect(self.on_show_class_dropdown)

        self.ui.change_class_color_button.clicked.connect(self.on_change_class_color)
        self.ui.show_class_selector_button.toggled.connect(self.on_toggle_quick_selector) # Use toggled

        # Vertical class selector
        self.ui.vertical_class_selector.class_selected.connect(self.on_floating_class_selected)

        # Point Navigation
        self.ui.next_point_button.clicked.connect(self.on_next_point)
        self.ui.prev_point_button.clicked.connect(self.on_prev_point)
        self.ui.auto_zoom_checkbox.toggled.connect(self.on_auto_zoom_toggled)

        # Actions
        self.ui.generate_points_button.clicked.connect(self.on_generate_points)
        self.ui.clear_points_button.clicked.connect(self.on_clear_points)
        # self.ui.save_results_button.clicked.connect(self.on_save_results) # Not implemented yet
        self.ui.export_points_button.clicked.connect(self.on_export_points)
        self.ui.semi_auto_classify_button.clicked.connect(self.on_semi_auto_classify)
        self.ui.undo_auto_classify_button.clicked.connect(self.on_undo_auto_classify)
        self.ui.export_classifier_button.clicked.connect(self.on_export_classifier)
        self.ui.import_classifier_button.clicked.connect(self.on_import_classifier)
        self.ui.use_pretrained_button.clicked.connect(self.on_use_pretrained_classifier)

        # Gallery
        self.ui.point_counting_gallery.image_clicked.connect(self.on_gallery_image_clicked)
        self.ui.point_counting_gallery.remove_clicked.connect(self.on_point_counting_gallery_remove_clicked)
        
        # Connect clear point counting gallery button
        if hasattr(self.ui, 'clear_point_counting_gallery_button'):
            self.ui.clear_point_counting_gallery_button.clicked.connect(self.clear_point_counting_gallery)
        # Note: point_counting_requested signal might be redundant if click loads image

        # Image View Interaction
        # Connect to the actual signal defined in QPixmapView
        self.ui.point_counting_image_view.mouse_pressed.connect(self.on_image_view_mouse_press)
        # Application Quit (Connect in app.py if needed, or rely on project saving)
        # app = QApplication.instance()
        # if app: app.aboutToQuit.connect(self.save_current_state)

        # Mark signals as connected to prevent duplicate connections
        self._signals_connected = True

    def _setup_keyboard_shortcuts(self):
        """Setup keyboard shortcuts using QShortcut, ensuring no duplicates."""
        # Check if shortcuts are already set up to avoid duplicates
        if hasattr(self, '_shortcuts_connected') and self._shortcuts_connected:
            return

        parent_widget = self.ui.point_counting_page # Attach shortcuts to the page

        # --- Clear existing classification shortcuts first ---
        if hasattr(self, 'classification_shortcuts') and self.classification_shortcuts:
            for shortcut, callback in self.classification_shortcuts:
                try:
                    shortcut.activated.disconnect(callback)
                except (RuntimeError, TypeError):
                    pass
                shortcut.setEnabled(False) # Disable it
            self.classification_shortcuts.clear()
        else:
            self.classification_shortcuts = [] # Ensure the list exists

        # --- Setup Navigation Shortcuts (Assuming these are less likely to cause issues, but could be cleared too if needed) ---
        # Consider clearing/managing these if they also cause problems
        QShortcut(QKeySequence(Qt.Key_Space), parent_widget, self.on_next_point)
        QShortcut(QKeySequence(Qt.Key_Right), parent_widget, self.on_next_point)
        QShortcut(QKeySequence(Qt.Key_Backspace), parent_widget, self.on_prev_point)
        QShortcut(QKeySequence(Qt.Key_Left), parent_widget, self.on_prev_point)

        # --- Setup Classification Shortcuts (Keys 1-9) ---
        for i in range(1, 10):
            shortcut = QShortcut(QKeySequence(f"{i}"), parent_widget)
            # Use lambda with default argument to capture the correct index
            callback = lambda index=i-1: self.on_classify_by_shortcut(index)
            try:
                shortcut.activated.connect(callback)
                self.classification_shortcuts.append((shortcut, callback)) # Store shortcut and its callback
            except Exception:
                pass

    # --- State Management ---

    def _get_current_state(self, create_if_missing=False):
        """Gets the state dictionary for the current image."""
        if not self.current_image_path:
            return None

        state = self.image_states.get(self.current_image_path)
        if state is None and create_if_missing:
            state = self._create_default_state()
            self.image_states[self.current_image_path] = state
        elif state is None:
            pass  # No state found and not creating

        return state

    def _create_default_state(self):
        """Creates a default state dictionary for a new image."""
        try:
            # Deep copy default classes to avoid shared references
            default_classes_copy = [(name, QColor(color)) for name, color in self.DEFAULT_CLASSES]

            # Check if this is a new project with no existing state
            try:
                is_new_project = self._is_new_project()

                pass  # Silently handle project type determination
            except Exception:
                # If there's an error determining if it's a new project, continue silently
                pass

            return {
                'points': [],               # List of (x, y, class_index, classification_method) tuples
                                            # classification_method: 0=manual, 1=auto
                'classes': default_classes_copy, # List of (name, QColor) tuples
                'current_class_index': 0,
                # Add other image-specific settings if needed in the future
            }
        except Exception as e:
            # If there's an error creating the default state, log it and return a minimal state
            logger.error(f"Error creating default state: {e}")
            # Create a minimal state with just one class
            minimal_class = [("Class 1", QColor(255, 255, 0))]
            return {
                'points': [],
                'classes': minimal_class,
                'current_class_index': 0,
            }

    def _is_new_project(self):
        """Check if this is a new project with no existing point counting state."""
        try:
            # Check if we have a project object
            if hasattr(self, 'project') and self.project:
                # Check if the project has the get_image_ids method
                if hasattr(self.project, 'get_image_ids'):
                    # Check if any images have point counting state
                    for image_id in self.project.get_image_ids():
                        if hasattr(self.project, 'has_point_counting_state') and self.project.has_point_counting_state(image_id):
                            return False
                    return True
                # If the project doesn't have get_image_ids, check if it has images attribute
                elif hasattr(self.project, 'images') and isinstance(self.project.images, dict):
                    # Check if any images have point counting state
                    for image_id in self.project.images.keys():
                        if hasattr(self.project, 'has_point_counting_state') and self.project.has_point_counting_state(image_id):
                            return False
                    return True
                else:
                    # Can't determine from project, fall back to session state
                    logger.warning("Project object doesn't have get_image_ids method or images attribute")

            # If no project object or can't determine from project, check session state
            if self.session_state and self.session_state.project_dir:
                # Check if any state files exist in the point_counting directory
                page_state_dir = self.session_state.get_state_subdir("point_counting")
                if page_state_dir and page_state_dir.exists():
                    # Check if any state files exist
                    state_files = list(page_state_dir.glob("state_*.json"))
                    return len(state_files) == 0
                return True

            # Default to True if we can't determine
            return True
        except Exception as e:
            # Log the error and default to False (use existing class names) to be safe
            logger.error(f"Error in _is_new_project: {e}")
            return False

    def _load_state_from_project(self, image_path):
        """Loads state for a specific image path from the project session."""
        # First try to load from the project object
        if hasattr(self, 'project') and self.project:
            image_id = os.path.basename(image_path)
            if self.project.has_point_counting_state(image_id):
                loaded_data = self.project.load_point_counting_state(image_id)
                if loaded_data:
                    # Ensure points have 4 elements (handle older formats if necessary)
                    if 'points' in loaded_data:
                        loaded_data['points'] = [
                            (p[0], p[1],
                             p[2] if len(p) > 2 else self.UNCLASSIFIED_INDEX,
                             p[3] if len(p) > 3 else self.MANUAL_CLASSIFICATION)
                            for p in loaded_data['points']
                        ]
                    else:
                        loaded_data['points'] = [] # Ensure points list exists

                    # Set default for missing keys
                    loaded_data.setdefault('current_class_index', 0)

                    # Store in memory
                    self.image_states[image_path] = loaded_data
                    logger.info(f"Loaded point counting state from project for {image_id}")
                    return True

        # Fall back to session state if project loading failed
        if not self.session_state.project_dir: return False
        # Assume session_state can load arbitrary data associated with a key/project
        loaded_data = self.session_state.load_state(f"point_counting_{os.path.basename(image_path)}")
        if loaded_data:
            # Deserialize classes
            if 'classes' in loaded_data:
                loaded_data['classes'] = [(name, QColor(color_str)) for name, color_str in loaded_data['classes']]
            else: # Handle missing classes - use default
                 loaded_data['classes'] = [(name, QColor(color)) for name, color in self.DEFAULT_CLASSES]

            # Ensure points have 4 elements (handle older formats if necessary)
            if 'points' in loaded_data:
                loaded_data['points'] = [
                    (p[0], p[1],
                     p[2] if len(p) > 2 else self.UNCLASSIFIED_INDEX,
                     p[3] if len(p) > 3 else self.MANUAL_CLASSIFICATION)
                    for p in loaded_data['points']
                ]
            else:
                loaded_data['points'] = [] # Ensure points list exists

            # Set default for missing keys
            loaded_data.setdefault('current_class_index', 0)

            self.image_states[image_path] = loaded_data
            logger.info(f"Loaded point counting state from session for {os.path.basename(image_path)}")
            return True
        return False

    def save_current_state_to_project(self):
        """Saves the state for the current image to the project session."""
        state = self._get_current_state(create_if_missing=True)
        if not state or not self.current_image_path:
            return

        # Store the current point index within the image's state
        state['current_point_index'] = self.current_point_index

        # First try to save to the project object
        if hasattr(self, 'project') and self.project:
            image_id = os.path.basename(self.current_image_path)

            # Add additional metadata to the state
            state['counting_method'] = self.counting_method
            state['grid_size'] = self.ui.grid_size_spinbox.value() if hasattr(self.ui, 'grid_size_spinbox') else 10
            state['random_count'] = self.ui.random_points_spinner.value() if hasattr(self.ui, 'random_points_spinner') else 100
            state['timestamp'] = datetime.now().isoformat() if 'datetime' in globals() else str(QDateTime.currentDateTime().toString())

            # Save to project
            logger.info(f"Saving point counting state to project for image_id: {image_id}")
            self.project.save_point_counting_state(image_id, state)
            logger.info(f"Point counting state saved to project for {image_id}")
            return

        # Fall back to session state if project saving failed
        if not self.session_state.project_dir:
            return

        # Serialize state for session state
        serializable_state = state.copy()
        serializable_state['classes'] = [(name, color.name(QColor.HexArgb)) for name, color in state['classes']]

        key = f"point_counting_{os.path.basename(self.current_image_path)}"
        if self.session_state.save_state(key, serializable_state):
            print(f"Saved point counting state to session for {os.path.basename(self.current_image_path)}")
        else:
            print(f"Failed to save point counting state to session for {os.path.basename(self.current_image_path)}")

    # --- Image Loading & Switching ---

    def load_images_from_project_hub(self, image_paths, image_infos):
        """Loads images from Project Hub into the gallery and sets up the first image.
        If the gallery already has images, new images will be appended.
        """
        print(f"Point Counting: Loading {len(image_paths)} images.")
        if not image_paths:
            self._set_controls_enabled(False)
            self.ui.status_label.setText("No images loaded.")
            return

        # Check if we already have images in the gallery
        existing_images = len(self.ui.point_counting_gallery.images) > 0

        # If no existing images, clear everything
        if not existing_images:
            self.ui.point_counting_gallery.clear_images()
            self.image_states = {}
            self.current_image_path = None
            self.current_image_data = None
            self.current_point_index = self.UNCLASSIFIED_INDEX
            self.ui.point_counting_image_view.clear()
            self.ui.summary_label.clear()
            self.ui.point_counting_percentages_widget.update_percentages({}, {})

        # Load images and populate gallery
        first_new_image_path = None
        loaded_count = 0

        # Check for duplicate images
        existing_paths = set(self.ui.point_counting_gallery.file_paths)

        for image_path, image_info in zip(image_paths, image_infos):
            # Skip if this image is already in the gallery
            if image_path in existing_paths:
                print(f"Skipping duplicate image: {image_path}")
                continue

            try:
                image_data = cv2.imread(image_path)
                if image_data is None:
                    print(f"Warning: Failed to load image {image_path}")
                    continue
                image_data = cv2.cvtColor(image_data, cv2.COLOR_BGR2RGB)

                # Add to gallery
                self.ui.point_counting_gallery.add_image(image_data, image_info.filename, image_path)
                loaded_count += 1

                # Attempt to load state or create default
                if not self._load_state_from_project(image_path):
                    self.image_states[image_path] = self._create_default_state()

                if first_new_image_path is None:
                    first_new_image_path = image_path

            except Exception as e:
                print(f"Error loading image {image_path} for point counting: {e}")
                QMessageBox.warning(self.ui.point_counting_page, "Load Error", f"Could not load image:\n{os.path.basename(image_path)}\nError: {e}")

        # Determine which image to activate
        if existing_images and self.current_image_path:
            # Keep the current image active if we already had images
            self._set_controls_enabled(True)
            print(f"Keeping current image: {self.current_image_path}")
            # Just update the UI to reflect any changes
            self._update_ui_for_current_state()
        elif first_new_image_path:
            # Activate the first new image if we didn't have a current image
            gallery_index = self.ui.point_counting_gallery.file_paths.index(first_new_image_path)
            self.ui.point_counting_gallery.select_image(gallery_index) # Visually select in gallery
            self._switch_to_image(first_new_image_path)
            self._set_controls_enabled(True)
            print(f"Switched to new image: {first_new_image_path}")
        elif len(self.ui.point_counting_gallery.file_paths) > 0:
            # If no new images were added but we have existing ones, make sure one is active
            first_image_path = self.ui.point_counting_gallery.file_paths[0]
            gallery_index = 0
            self.ui.point_counting_gallery.select_image(gallery_index)
            self._switch_to_image(first_image_path)
            self._set_controls_enabled(True)
            print(f"Switched to existing image: {first_image_path}")
        else:
            # No images at all
            self.ui.status_label.setText("Failed to load any valid images.")
            self._set_controls_enabled(False)

        # Show a message about how many images were added
        if loaded_count > 0:
            QMessageBox.information(self.ui.point_counting_page, "Images Added",
                                  f"Added {loaded_count} new images to the gallery.")

    def on_gallery_image_clicked(self, index):
        """Handle click on an image in the gallery."""
        if 0 <= index < len(self.ui.point_counting_gallery.file_paths):
            new_image_path = self.ui.point_counting_gallery.file_paths[index]
            if new_image_path != self.current_image_path:
                self._switch_to_image(new_image_path)

    def on_point_counting_gallery_remove_clicked(self, index):
        """Handler for when a thumbnail X button is clicked in point counting gallery."""
        if not hasattr(self, 'ui') or not hasattr(self.ui, 'point_counting_gallery') or not (0 <= index < len(self.ui.point_counting_gallery.file_paths)):
            return
        
        image_path = self.ui.point_counting_gallery.file_paths[index]
        filename = os.path.basename(image_path)
        
        # Confirm removal
        reply = QMessageBox.question(
            self.ui.point_counting_page, "Confirm Removal",
            f"Remove this image from the point counting gallery?\n{filename}",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            # Check if this is the currently active image
            was_current = (image_path == self.current_image_path)
            
            # Manually remove from gallery UI without triggering the signal again
            if 0 <= index < len(self.ui.point_counting_gallery.images):
                # Store the current selection state
                was_selected = (self.ui.point_counting_gallery.selected_index == index)
                
                # Remove the thumbnail widget
                if index < len(self.ui.point_counting_gallery.thumbnails):
                    thumbnail = self.ui.point_counting_gallery.thumbnails.pop(index)
                    thumbnail.deleteLater()
                
                # Remove the image data
                self.ui.point_counting_gallery.images.pop(index)
                self.ui.point_counting_gallery.filenames.pop(index)
                self.ui.point_counting_gallery.file_paths.pop(index)
                
                # Remove from image states
                if image_path in self.image_states:
                    del self.image_states[image_path]
                
                # Handle current image removal
                if was_current:
                    if len(self.ui.point_counting_gallery.file_paths) > 0:
                        # Switch to another image
                        new_index = min(index, len(self.ui.point_counting_gallery.file_paths) - 1)
                        new_image_path = self.ui.point_counting_gallery.file_paths[new_index]
                        self.ui.point_counting_gallery.selected_index = new_index
                        self._switch_to_image(new_image_path)
                    else:
                        # No images left
                        self.current_image_path = None
                        self.current_image_data = None
                        self.current_point_index = self.UNCLASSIFIED_INDEX
                        self.ui.point_counting_image_view.clear()
                        self._set_controls_enabled(False)
                else:
                    # Update the selected index
                    if was_selected:
                        if len(self.ui.point_counting_gallery.images) > 0:
                            new_index = min(index, len(self.ui.point_counting_gallery.images) - 1)
                            self.ui.point_counting_gallery.selected_index = new_index
                        else:
                            self.ui.point_counting_gallery.selected_index = -1
                    elif self.ui.point_counting_gallery.selected_index > index:
                        self.ui.point_counting_gallery.selected_index -= 1
                
                # Update all thumbnail indices
                for i, thumbnail in enumerate(self.ui.point_counting_gallery.thumbnails):
                    thumbnail.index = i
                
                # Update thumbnail styling
                for i, thumbnail in enumerate(self.ui.point_counting_gallery.thumbnails):
                    thumbnail.set_selected(i == self.ui.point_counting_gallery.selected_index)
                
                # Update the count label
                self.ui.point_counting_gallery.update_count_label()
            
            logger.info(f"Successfully removed image from point counting gallery: {filename}")

    def _switch_to_image(self, new_image_path):
        """Switches the active image, saving old state and loading new state."""
        if self.current_image_path:
            self.save_current_state_to_project() # Save state of the image we are leaving

        self.current_image_path = new_image_path
        self.current_image_data = self.ui.point_counting_gallery.get_image_data(new_image_path) # Get raw data

        if self.current_image_data is None:
            print(f"Error: Could not retrieve image data for {new_image_path} from gallery.")
            QMessageBox.critical(self.ui.point_counting_page, "Error", "Failed to load image data from gallery.")
            self.current_image_path = None
            self._set_controls_enabled(False)
            return

        print(f"Switched to image: {os.path.basename(new_image_path)}")

        # Get state for the new image (should exist from load_images or project load)
        state = self._get_current_state(create_if_missing=True) # Ensure state exists

        # Restore current point index for this image
        self.current_point_index = state.get('current_point_index', self.UNCLASSIFIED_INDEX)
        # Validate current_point_index
        if not (self.UNCLASSIFIED_INDEX <= self.current_point_index < len(state['points'])):
             self.current_point_index = self.UNCLASSIFIED_INDEX if not state['points'] else 0


        self._update_ui_for_current_state()
        self._set_controls_enabled(True)

    # --- UI Update & Control Logic ---

    def _set_controls_enabled(self, enabled):
        """Enable/disable relevant controls based on whether an image is loaded."""
        # Panels
        self.ui.point_counting_controls_panel.setEnabled(enabled)
        self.ui.right_panel.setEnabled(enabled) # Enable/disable right panel
        # Specific buttons that depend on points existing
        points_exist = enabled and bool(self._get_current_state().get('points'))
        self.ui.clear_points_button.setEnabled(points_exist)
        self.ui.export_points_button.setEnabled(points_exist)
        self.ui.prev_point_button.setEnabled(points_exist)
        self.ui.next_point_button.setEnabled(points_exist)
        # Save results button (if implemented)
        # self.ui.save_results_button.setEnabled(points_exist)

    def reset_state(self):
        """Completely resets the handler's state when switching projects.
        This should clear all data and UI elements to prevent conflicts.
        """
        logger.info("Resetting Point Counting page state")

        # Clear all state variables
        self.image_states = {}
        self.current_image_path = None
        self.current_image_data = None
        self.current_point_index = self.UNCLASSIFIED_INDEX

        # Reset UI elements
        if hasattr(self.ui, 'point_counting_gallery'):
            self.ui.point_counting_gallery.clear_images()

        if hasattr(self.ui, 'point_counting_image_view'):
            self.ui.point_counting_image_view.clear()

        if hasattr(self.ui, 'class_selector'):
            self.ui.class_selector.clear()

        if hasattr(self.ui, 'vertical_class_selector'):
            self.ui.vertical_class_selector.update_classes([])

        if hasattr(self.ui, 'summary_label'):
            self.ui.summary_label.setText("No image loaded.")

        if hasattr(self.ui, 'point_counting_percentages_widget'):
            self.ui.point_counting_percentages_widget.update_percentages({}, {})

        if hasattr(self.ui, 'status_label'):
            self.ui.status_label.setText("Load an image.")

        # Reset control buttons
        if hasattr(self.ui, 'prev_point_button'):
            self.ui.prev_point_button.setEnabled(False)
        if hasattr(self.ui, 'next_point_button'):
            self.ui.next_point_button.setEnabled(False)
        if hasattr(self.ui, 'clear_points_button'):
            self.ui.clear_points_button.setEnabled(False)
        if hasattr(self.ui, 'export_points_button'):
            self.ui.export_points_button.setEnabled(False)

        # Reset counting method to default
        self.counting_method = "grid"
        if hasattr(self.ui, 'grid_counting_radio'):
            self.ui.grid_counting_radio.setChecked(True)

        # Reset parameter values
        self.grid_size = 10
        self.random_points_count = 100
        self.random_seed = 42
        self.grid_offset_x = 0
        self.grid_offset_y = 0

        if hasattr(self.ui, 'grid_size_spinner'):
            self.ui.grid_size_spinner.setValue(self.grid_size)
        if hasattr(self.ui, 'random_points_spinner'):
            self.ui.random_points_spinner.setValue(self.random_points_count)
        if hasattr(self.ui, 'random_seed_spinner'):
            self.ui.random_seed_spinner.setValue(self.random_seed)
        if hasattr(self.ui, 'grid_offset_x_spinner'):
            self.ui.grid_offset_x_spinner.setValue(self.grid_offset_x)
        if hasattr(self.ui, 'grid_offset_y_spinner'):
            self.ui.grid_offset_y_spinner.setValue(self.grid_offset_y)

        logger.info("Point Counting page state reset complete")

    def _update_ui_for_current_state(self):
        """Updates all relevant UI elements based on the current image's state."""
        state = self._get_current_state()
        if not state or self.current_image_data is None:
            self.ui.point_counting_image_view.clear()
            self.ui.class_selector.clear()
            self.ui.summary_label.setText("No image loaded or state error.")
            self.ui.point_counting_percentages_widget.update_percentages({}, {})
            self.ui.status_label.setText("Load an image.")
            return

        # Update class selector and indicators
        self._update_class_selector()
        self._update_class_color_indicator()
        self._update_class_display_label()  # Update the class display label
        self.ui.vertical_class_selector.update_classes(state['classes'])

        # Update image display with points and grid
        self._update_display()

        # Update summary and percentages
        self._update_summary()
        self._update_class_percentages()

        # Update status bar
        self._update_status_label()

        # Update Point Nav Button states
        points = state.get('points', [])
        points_exist = bool(points)
        self.ui.prev_point_button.setEnabled(points_exist)
        self.ui.next_point_button.setEnabled(points_exist)
        self.ui.clear_points_button.setEnabled(points_exist)
        self.ui.export_points_button.setEnabled(points_exist)


    def _update_controls_for_method(self):
        """Show/hide parameter groups based on selected method."""
        self.ui.grid_params_group.setVisible(self.counting_method == "grid")
        self.ui.random_params_group.setVisible(self.counting_method == "random")

        # Enable/disable Generate button based on method
        is_manual = self.counting_method == "custom"
        self.ui.generate_points_button.setEnabled(not is_manual)  # Disable in manual mode

        # Update button text
        if is_manual:
            self.ui.generate_points_button.setText("Manual Click Mode Active")
            self.ui.generate_points_button.setStyleSheet("background-color: #2c3e50; color: #e0e6f0; font-style: italic; padding: 6px;")
        else:
            self.ui.generate_points_button.setText("Generate Points")
            self.ui.generate_points_button.setStyleSheet("background-color: #388e3c; color: #e0e6f0; font-weight: bold; padding: 6px;")

        # Print debug message
        print(f"Updated controls for method: {self.counting_method}, Generate button enabled: {not is_manual}")

    def _update_color_button_style(self, button, color):
        """Updates the background color and style of a color button."""
        button.setStyleSheet(
            f"background-color: {color.name(QColor.HexRgb)}; "
            f"border: 1px solid #888; border-radius: 4px;"
        )
        # Add contrasting text if needed, or make button smaller if just color
        # button.setText(color.name(QColor.HexRgb)) # Optional: show hex code

    def _update_class_selector(self):
        """Update the class selector combo box based on current state."""
        state = self._get_current_state()
        if not state: return

        self.ui.class_selector.blockSignals(True)
        self.ui.class_selector.clear()
        for _, (class_name, color) in enumerate(state['classes']):  # Using _ for unused index
            pixmap = QPixmap(16, 16)
            pixmap.fill(color)
            self.ui.class_selector.addItem(QIcon(pixmap), class_name) # Add icon
        current_class_idx = state.get('current_class_index', 0)
        if 0 <= current_class_idx < len(state['classes']):
            self.ui.class_selector.setCurrentIndex(current_class_idx)
        self.ui.class_selector.blockSignals(False)

    def _update_class_color_indicator(self):
        """Update the small color square next to the change color button."""
        state = self._get_current_state()
        if not state: return
        current_class_idx = state.get('current_class_index', -1)
        if 0 <= current_class_idx < len(state['classes']):
            color = state['classes'][current_class_idx][1]
            self.ui.class_color_indicator.setStyleSheet(f"background-color: {color.name()}; border: 1px solid #555;")
        else:
            self.ui.class_color_indicator.setStyleSheet("background-color: transparent; border: none;")

    def _update_class_display_label(self):
        """Update the class display label with the current class name and color."""
        state = self._get_current_state()
        if not state: return

        current_class_idx = state.get('current_class_index', -1)
        if 0 <= current_class_idx < len(state['classes']):
            class_name, color = state['classes'][current_class_idx]

            # Create a pixmap with a border for better visibility
            pixmap = QPixmap(20, 20)
            pixmap.fill(Qt.transparent)  # Start with transparent background using Qt from imports

            # Create a painter to draw on the pixmap
            painter = QPainter(pixmap)
            painter.setRenderHint(QPainter.Antialiasing)

            # Draw filled circle with border
            painter.setPen(QPen(Qt.black, 1))
            painter.setBrush(color)
            painter.drawEllipse(2, 2, 16, 16)
            painter.end()

            # Set the text with the class name
            self.ui.class_display_label.setText(f" {class_name} (scroll to change)")

            # Set the label style based on theme
            is_dark_theme = self._is_dark_theme()
            text_color = "#e0e0e0" if is_dark_theme else "#333333"
            self.ui.class_display_label.setStyleSheet(f"""
                padding-left: 5px;
                font-weight: bold;
                color: {text_color};
            """)

            # Set the icon
            self.ui.class_display_label.setPixmap(pixmap)

            # Update the frame style to show the selected class color as a border
            is_dark_theme = self._is_dark_theme()
            bg_color = "#2d2d2d" if is_dark_theme else "#f5f5f5"
            text_color = "#e0e0e0" if is_dark_theme else "#333333"

            self.ui.class_selector_frame.setStyleSheet(f"""
                border: 1px solid {color.name()};
                border-radius: 3px;
                background-color: {bg_color};
                color: {text_color};
            """)
            # Set cursor using Qt's native method instead of CSS
            self.ui.class_selector_frame.setCursor(Qt.PointingHandCursor)
            # Set tooltip to indicate scrolling functionality
            self.ui.class_selector_frame.setToolTip("Use mouse wheel to scroll through classes")

            # Update the prominent class name display
            # Use theme-appropriate background with class color accent
            is_dark_theme = self._is_dark_theme()
            bg_color = "#2d2d2d" if is_dark_theme else "#f5f5f5"
            text_color = "#e0e0e0" if is_dark_theme else "#333333"

            # Add a subtle hint of the class color to the background
            bg_with_class_hint = f"linear-gradient(to right, {bg_color}, {bg_color} 85%, {color.name()} 100%)"

            self.ui.current_class_name_label.setText(f"{class_name}")
            self.ui.current_class_name_label.setStyleSheet(f"""
                font-size: 14px;
                font-weight: bold;
                padding: 8px;
                border-radius: 4px;
                background-color: {bg_color};
                background: {bg_with_class_hint};
                color: {text_color};
                border: 2px solid {color.name()};
            """)
        else:
            # No class selected or invalid index
            self.ui.class_display_label.setText(" Select a class (scroll to change)")

            # Set the label style based on theme
            is_dark_theme = self._is_dark_theme()
            text_color = "#e0e0e0" if is_dark_theme else "#333333"
            self.ui.class_display_label.setStyleSheet(f"""
                padding-left: 5px;
                font-weight: bold;
                color: {text_color};
            """)

            # Use theme-appropriate colors
            is_dark_theme = self._is_dark_theme()
            bg_color = "#2d2d2d" if is_dark_theme else "#f5f5f5"
            border_color = "#555555" if is_dark_theme else "#888888"
            text_color = "#e0e0e0" if is_dark_theme else "#333333"

            self.ui.class_selector_frame.setStyleSheet(f"""
                border: 1px solid {border_color};
                border-radius: 3px;
                background-color: {bg_color};
                color: {text_color};
            """)
            # Set cursor using Qt's native method instead of CSS
            self.ui.class_selector_frame.setCursor(Qt.PointingHandCursor)
            # Set tooltip to indicate scrolling functionality
            self.ui.class_selector_frame.setToolTip("Use mouse wheel to scroll through classes")

            # Update the prominent class name display for no selection
            self.ui.current_class_name_label.setText("None")
            self.ui.current_class_name_label.setStyleSheet(f"""
                font-size: 14px;
                font-weight: bold;
                padding: 8px;
                border-radius: 4px;
                background-color: {bg_color};
                color: {text_color};
                border: 2px solid {border_color};
            """)

    def _update_display(self):
        """Update the image view with points and grid."""
        print("_update_display called")
        if self.current_image_data is None:
            print("No image data available for display")
            self.ui.point_counting_image_view.clear()
            return

        state = self._get_current_state()
        if not state:
            print("No state available for display")
            return

        display_image = self.current_image_data.copy()
        height, width = display_image.shape[:2]
        points = state.get('points', [])
        classes = state.get('classes', [])
        print(f"Updating display with {len(points)} points, image size: {width}x{height}")

        # Draw Grid Lines
        if self.show_grid_lines and self.counting_method == "grid" and points:
            rows = self.grid_size
            cols = self.grid_size
            if rows > 0 and cols > 0:
                cell_height = height / rows
                cell_width = width / cols
                # Get RGB values from QColor - our image is already in RGB format
                r, g, b = self.grid_color.red(), self.grid_color.green(), self.grid_color.blue()
                grid_color_rgb = (r, g, b)
                alpha = self.grid_color.alpha() / 255.0

                overlay = np.zeros_like(display_image, dtype=np.uint8)
                # Draw horizontal lines
                for r in range(1, rows):
                    y = int(r * cell_height)
                    cv2.line(overlay, (0, y), (width, y), grid_color_rgb, 1, cv2.LINE_AA)
                # Draw vertical lines
                for c in range(1, cols):
                    x = int(c * cell_width)
                    cv2.line(overlay, (x, 0), (x, height), grid_color_rgb, 1, cv2.LINE_AA)

                # Blend grid overlay
                cv2.addWeighted(overlay, alpha, display_image, 1.0 - alpha, 0, display_image)


        # Draw Points
        # Point visualization approach:
        # 1. All classified points are filled with their class color (fully opaque)
        # 2. Only the currently selected point has transparency to show the underlying image
        # 3. Auto-classified points have cyan borders and an 'A' indicator
        # 4. Manually classified points have borders in their class color
        # 5. The current point has a yellow highlight circle around it
        # 6. Unclassified points are unfilled circles with gray borders

        # Define colors in RGB format for OpenCV (image is already in RGB format)
        unclassified_color_rgb = (200, 200, 200) # Light gray
        invalid_color_rgb = (255, 0, 0) # Red (RGB: Red=255, Green=0, Blue=0)
        highlight_color_rgb = (255, 255, 0) # Yellow (RGB: Red=255, Green=255, Blue=0)
        border_color_rgb = (0, 0, 0) # Black
        auto_border_color_rgb = (0, 255, 255) # Cyan border for auto-classified points

        for i, point in enumerate(points):
            # Handle both 3-element and 4-element point tuples
            if len(point) >= 4:
                x, y, class_idx, classification_method = point
            else:
                x, y, class_idx = point[:3]
                classification_method = self.MANUAL_CLASSIFICATION

            int_x, int_y = int(round(x)), int(round(y))
            point_color_rgb = unclassified_color_rgb

            if class_idx == self.UNCLASSIFIED_INDEX:
                point_color_rgb = unclassified_color_rgb
            elif 0 <= class_idx < len(classes):
                qcolor = classes[class_idx][1]
                # Get RGB values directly from QColor
                r, g, b = qcolor.red(), qcolor.green(), qcolor.blue()
                # Use RGB values directly (image is already in RGB format)
                point_color_rgb = (r, g, b)
            else: # Invalid index
                point_color_rgb = invalid_color_rgb

            is_current = (i == self.current_point_index)
            radius = self.HIGHLIGHT_RADIUS if is_current else self.POINT_RADIUS

            # Draw highlight circle first if current
            if is_current:
                 # Draw a bright yellow highlight circle around the current point
                 cv2.circle(display_image, (int_x, int_y), radius + 3, highlight_color_rgb, self.BORDER_THICKNESS, cv2.LINE_AA)

            # Draw classified or unclassified points
            if class_idx != self.UNCLASSIFIED_INDEX and 0 <= class_idx < len(classes):
                # For classified points

                if is_current:
                    # Current point - semi-transparent to see the underlying image
                    # Create a separate overlay for the filled circle to control transparency
                    circle_overlay = np.zeros_like(display_image, dtype=np.uint8)

                    # Fill the circle with the class color
                    cv2.circle(circle_overlay, (int_x, int_y), radius, point_color_rgb, -1, cv2.LINE_AA)

                    # Add the filled circle with transparency
                    cv2.addWeighted(circle_overlay, self.CURRENT_POINT_ALPHA, display_image, 1.0, 0, display_image)
                else:
                    # Non-current points - fully opaque
                    # Draw filled circle directly with the class color
                    cv2.circle(display_image, (int_x, int_y), radius, point_color_rgb, -1, cv2.LINE_AA)

                # Draw the border - different style for auto vs manual classification
                if classification_method == self.AUTO_CLASSIFICATION:
                    # Cyan border for auto-classified points
                    cv2.circle(display_image, (int_x, int_y), radius, auto_border_color_rgb, self.BORDER_THICKNESS, cv2.LINE_AA)

                    # Add a small 'A' indicator near the point
                    indicator_x = int_x + radius + 2
                    indicator_y = int_y - radius - 2

                    # Create a small background circle for better visibility
                    cv2.circle(display_image, (indicator_x + 4, indicator_y - 4), self.AUTO_INDICATOR_RADIUS, (255, 255, 255), -1, cv2.LINE_AA)
                    cv2.circle(display_image, (indicator_x + 4, indicator_y - 4), self.AUTO_INDICATOR_RADIUS, auto_border_color_rgb, 1, cv2.LINE_AA)

                    # Draw the 'A' indicator
                    cv2.putText(display_image, "A", (indicator_x, indicator_y),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 0, 0), 1, cv2.LINE_AA)
                else:
                    # Solid border in class color for manually classified points
                    cv2.circle(display_image, (int_x, int_y), radius, point_color_rgb, self.BORDER_THICKNESS, cv2.LINE_AA)
            else:
                # Unclassified points - just draw an unfilled circle with gray border
                cv2.circle(display_image, (int_x, int_y), radius, unclassified_color_rgb, self.BORDER_THICKNESS, cv2.LINE_AA)

            # Optional: Draw point index number slightly offset
            # text_pos = (int_x + radius + 2, int_y - radius - 2)
            # cv2.putText(display_image, str(i + 1), text_pos, cv2.FONT_HERSHEY_SIMPLEX, 0.4, border_color_rgb, 2, cv2.LINE_AA)
            # cv2.putText(display_image, str(i + 1), text_pos, cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1, cv2.LINE_AA)

        # Convert to QPixmap and display
        # The image is already in RGB format (converted when loaded)
        # so we can create the QImage directly
        h, w = display_image.shape[:2]
        bytes_per_line = 3 * w
        q_image = QImage(display_image.data, w, h, bytes_per_line, QImage.Format_RGB888)
        pixmap = QPixmap.fromImage(q_image)
        self.ui.point_counting_image_view.setPixmap(pixmap)

    def _update_summary(self):
        """Update the summary statistics display."""
        state = self._get_current_state()
        if not state:
            self.ui.summary_label.setText("No data.")
            return

        points = state.get('points', [])
        classes = state.get('classes', [])
        total_points = len(points)

        if total_points == 0:
            self.ui.summary_label.setText("No points generated yet.")
            return

        # Count points by classification status and method
        unclassified = 0
        classified = 0
        invalid = 0
        auto_classified = 0
        manual_classified = 0

        class_counts = {i: 0 for i in range(len(classes))}

        for point in points:
            # Handle both 3-element and 4-element point tuples
            if len(point) >= 4:
                _, _, c_idx, classification_method = point
            else:
                _, _, c_idx = point[:3]
                classification_method = self.MANUAL_CLASSIFICATION

            if c_idx == self.UNCLASSIFIED_INDEX:
                unclassified += 1
            elif 0 <= c_idx < len(classes):
                classified += 1
                class_counts[c_idx] += 1

                # Count by classification method
                if classification_method == self.AUTO_CLASSIFICATION:
                    auto_classified += 1
                else:
                    manual_classified += 1
            else:
                invalid += 1

        # Verify counts
        if classified + unclassified + invalid != total_points:
            print(f"Warning: Count mismatch - total: {total_points}, classified: {classified}, unclassified: {unclassified}, invalid: {invalid}")

        summary_text = f"<b>Total Points:</b> {total_points}<br>"
        summary_text += f"<b>Classified:</b> {classified} ({classified / total_points * 100:.1f}%)<br>"

        # Add auto/manual classification breakdown if any auto-classified points exist
        if auto_classified > 0 and classified > 0:
            summary_text += f"&nbsp;&nbsp;<b>Manual:</b> {manual_classified} ({manual_classified / classified * 100:.1f}%)<br>"
            summary_text += f"&nbsp;&nbsp;<b>Auto:</b> {auto_classified} ({auto_classified / classified * 100:.1f}%)<br>"

        summary_text += f"<b>Unclassified:</b> {unclassified} ({unclassified / total_points * 100:.1f}%)<br>"
        if invalid > 0:
             summary_text += f"<font color='red'><b>Invalid Class IDs:</b> {invalid}</font><br>"
        summary_text += "<hr>"
        summary_text += "<b>Class Counts:</b><br>"

        if classified > 0:
            # Sort classes by count descending
            sorted_class_indices = sorted(class_counts.keys(), key=lambda i: class_counts[i], reverse=True)
            for i in sorted_class_indices:
                 count = class_counts[i]
                 if count > 0: # Only show classes with points
                    name = classes[i][0]
                    color = classes[i][1]
                    percentage = (count / classified) * 100
                    # Use HTML span for color
                    summary_text += f"<span style='color:{color.name()};'>■</span> {name}: {count} ({percentage:.1f}%)<br>"
        else:
            summary_text += "<i>No points classified.</i>"

        self.ui.summary_label.setHtml(summary_text) # Use setHtml for rich text

    def _update_class_percentages(self):
        """Update the class percentages pie chart widget."""
        state = self._get_current_state()
        if not state:
            self.ui.point_counting_percentages_widget.update_percentages({}, {})
            return

        points = state.get('points', [])
        classes = state.get('classes', [])
        class_counts = {}
        total_classified = 0

        for point in points:
            # Handle both 3-element and 4-element point tuples
            if len(point) >= 4:
                _, _, class_id, _ = point
            else:
                _, _, class_id = point[:3]

            if 0 <= class_id < len(classes): # Valid classified point
                class_name = classes[class_id][0]
                class_counts[class_name] = class_counts.get(class_name, 0) + 1
                total_classified += 1

        percentages = {}
        colors = {}
        if total_classified > 0:
            for class_id, (class_name, qcolor) in enumerate(classes):
                 count = class_counts.get(class_name, 0)
                 if count > 0: # Only include classes with points
                    percentages[class_name] = (count / total_classified) * 100
                    colors[class_name] = qcolor # Pass QColor directly if widget supports it

        self.ui.point_counting_percentages_widget.update_percentages(percentages, colors)

    def _update_status_label(self):
        """Updates the status label with context-specific information."""
        state = self._get_current_state()
        if not self.current_image_path or not state:
            self.ui.status_label.setText("Load an image from the Project Hub.")
            return

        points = state.get('points', [])
        classes = state.get('classes', [])
        status_text = f"Image: <b>{os.path.basename(self.current_image_path)}</b>"

        if self.current_point_index != self.UNCLASSIFIED_INDEX and 0 <= self.current_point_index < len(points):
            point_num = self.current_point_index + 1
            total_points = len(points)

            # Handle both 3-element and 4-element point tuples
            point = points[self.current_point_index]
            if len(point) >= 4:
                _, _, class_id, classification_method = point
            else:
                _, _, class_id = point[:3]
                classification_method = self.MANUAL_CLASSIFICATION

            status_text += f" | Point {point_num}/{total_points}"

            # Add classification method indicator
            if class_id != self.UNCLASSIFIED_INDEX:
                method_text = " (Auto)" if classification_method == self.AUTO_CLASSIFICATION else ""
                status_text += method_text

            if class_id == self.UNCLASSIFIED_INDEX:
                status_text += ": <span style='color: gray;'>Unclassified</span>"
            elif 0 <= class_id < len(classes):
                class_name = classes[class_id][0]
                class_color = classes[class_id][1]
                status_text += f": <span style='color: {class_color.name()};'><b>{class_name}</b></span>"
            else:
                status_text += f": <span style='color: red;'>Invalid Class ID</span>"
        elif points:
             status_text += f" | {len(points)} points"
        else:
             status_text += " | No points"

        self.ui.status_label.setText(status_text)

    def _update_keyboard_shortcuts(self):
        """Updates the classification keyboard shortcuts (1-9) based on current classes."""
        # Disconnect and clear existing classification shortcuts
        for shortcut, callback in self.classification_shortcuts:
            try:
                shortcut.activated.disconnect(callback)
            except (TypeError, RuntimeError):  # Ignore errors when disconnecting
                # Ignore if already disconnected or object deleted
                pass
            shortcut.setParent(None) # Remove from parent
            shortcut.deleteLater() # Schedule for deletion
        self.classification_shortcuts.clear()

        state = self._get_current_state()
        if not state: return

        parent_widget = self.ui.point_counting_page
        num_classes = len(state['classes'])

        # Re-create shortcuts for keys 1 through min(9, num_classes)
        for i in range(1, min(10, num_classes + 1)):
            shortcut = QShortcut(QKeySequence(f"{i}"), parent_widget)
            # Use lambda with default argument to capture the correct index
            callback = lambda index=i-1: self.on_classify_by_shortcut(index)
            shortcut.activated.connect(callback)
            self.classification_shortcuts.append((shortcut, callback))

        # Mark shortcuts as connected to prevent duplicate setup
        self._shortcuts_connected = True
        logger.debug("Finished setting up keyboard shortcuts.")

    # --- Event Handlers ---

    def on_counting_method_changed(self):
        """Handle change in counting method selection."""
        if self.ui.grid_counting_radio.isChecked():
            self.counting_method = "grid"
        elif self.ui.random_counting_radio.isChecked():
            self.counting_method = "random"
        elif self.ui.custom_counting_radio.isChecked():
            self.counting_method = "custom"
        self._update_controls_for_method()

        # Print debug message to confirm method change
        print(f"Counting method changed to: {self.counting_method}")

    def on_param_changed(self):
        """Handle changes in parameter spinners."""
        self.grid_size = self.ui.grid_size_spinner.value()
        self.random_points_count = self.ui.random_points_spinner.value()
        self.random_seed = self.ui.random_seed_spinner.value()
        self.grid_offset_x = self.ui.grid_offset_x_spinner.value()
        self.grid_offset_y = self.ui.grid_offset_y_spinner.value()
        # Optionally regenerate points automatically if in grid mode and params change
        # if self.counting_method == "grid" and self.sender() in [self.ui.grid_size_spinner, self.ui.grid_offset_x_spinner, self.ui.grid_offset_y_spinner]:
        #    if self._get_current_state() and self._get_current_state().get('points'):
        #       self.on_generate_points()

    def _update_display_after_opacity_change(self):
        """Called by the timer after the opacity slider stops moving."""
        opacity = self.ui.grid_opacity_slider.value()
        alpha_val = int(opacity * 2.55) # Convert 0-100 to 0-255
        self.grid_color.setAlpha(alpha_val)
        self._update_display() # Update view with new opacity

        # Save to settings
        try:
            from src.utils.settings_manager import settings_manager
            if settings_manager is not None:
                settings_manager.set_value("point_counting/grid_color_a", alpha_val)
                settings_manager.set_value("point_counting/grid_opacity", opacity)
        except Exception as e:
            logger.error(f"Error saving grid opacity to settings: {e}")

    def handle_app_shutdown(self):
        """Save state when application is shutting down."""
        print("Point Counting: Handling application shutdown")
        if self.current_image_path:
            self.save_current_state_to_project()
        print("Point Counting: State saved during shutdown")

    def eventFilter(self, obj, event):
        """Event filter to handle mouse wheel events for class switching.

        Args:
            obj: The object that triggered the event
            event: The event that was triggered

        Returns:
            bool: True if the event was handled, False otherwise
        """
        from PySide6.QtCore import QEvent

        # Check if the event is a wheel event on any of the class selector components
        if (obj in [self.ui.class_selector_frame, self.ui.class_display_label, self.ui.show_class_dropdown_button]
            and event.type() == QEvent.Wheel):
            # Get the current state
            state = self._get_current_state()
            if not state:
                return False

            # Get the number of classes
            num_classes = len(state['classes'])
            if num_classes <= 1:
                return False

            # Get the current class index
            current_idx = state.get('current_class_index', 0)

            # Determine the direction of the scroll
            delta = event.angleDelta().y()

            # Calculate the new index
            if delta > 0:  # Scroll up
                new_idx = (current_idx - 1) % num_classes
            else:  # Scroll down
                new_idx = (current_idx + 1) % num_classes

            # Update the class selector
            self.ui.class_selector.setCurrentIndex(new_idx)

            # Get the selected class name and color
            class_name = state['classes'][new_idx][0]
            class_color = state['classes'][new_idx][1]

            # Update the prominent class name display
            # Use theme-appropriate background with class color accent
            is_dark_theme = self._is_dark_theme()
            bg_color = "#2d2d2d" if is_dark_theme else "#f5f5f5"
            text_color = "#e0e0e0" if is_dark_theme else "#333333"

            # Add a subtle hint of the class color to the background
            bg_with_class_hint = f"linear-gradient(to right, {bg_color}, {bg_color} 85%, {class_color.name()} 100%)"

            self.ui.current_class_name_label.setText(f"{class_name}")
            self.ui.current_class_name_label.setStyleSheet(f"""
                font-size: 14px;
                font-weight: bold;
                padding: 8px;
                border-radius: 4px;
                background-color: {bg_color};
                background: {bg_with_class_hint};
                color: {text_color};
                border: 2px solid {class_color.name()};
            """)

            # Show a tooltip with the selected class name
            from PySide6.QtWidgets import QToolTip
            # Use globalPosition().toPoint() instead of deprecated globalPos()
            QToolTip.showText(event.globalPosition().toPoint(), f"Selected: {class_name}", self.ui.class_selector_frame)

            # Event was handled
            return True

        # Pass the event to the parent class
        return super(PointCountingPageHandler, self).eventFilter(obj, event)

    def _get_contrasting_text(self, color):
        """Get a contrasting text color (black or white) based on the background color.

        Args:
            color (QColor): The background color

        Returns:
            str: The contrasting text color as a hex string
        """
        # Calculate luminance using the formula: 0.299*R + 0.587*G + 0.114*B
        luminance = (0.299 * color.red() + 0.587 * color.green() + 0.114 * color.blue()) / 255

        # Use white text for dark backgrounds, black text for light backgrounds
        return "#ffffff" if luminance < 0.5 else "#000000"

    def _get_contrasting_background(self, color):
        """Get a contrasting background color based on the input color.

        Args:
            color (QColor): The input color

        Returns:
            str: The contrasting background color as a hex string with alpha
        """
        # For very light colors, use a darker version with transparency
        if color.lightness() > 200:
            # Create a darker version with transparency
            darker_color = QColor(color)
            darker_color.setAlpha(40)  # Low alpha for transparency
            return f"rgba({darker_color.red()}, {darker_color.green()}, {darker_color.blue()}, 0.15)"
        else:
            # For darker colors, use a lighter version with transparency
            lighter_color = QColor(color)
            lighter_color.setAlpha(40)  # Low alpha for transparency
            return f"rgba({lighter_color.red()}, {lighter_color.green()}, {lighter_color.blue()}, 0.15)"

    def _is_dark_theme(self):
        """Detect if the application is using a dark theme.

        Returns:
            bool: True if using a dark theme, False otherwise
        """
        try:
            # Try to get the current application instance
            app = QApplication.instance()
            if not app:
                return False

            # Get the current theme settings
            settings = app.property("settings")
            if settings:
                theme_base = settings.value("app/theme", "Dark Theme").lower()
                return "dark" in theme_base

            # Check background color of the main window as fallback
            bg_color = self.ui.point_counting_page.palette().color(self.ui.point_counting_page.backgroundRole())
            return bg_color.lightness() < 128  # If lightness is low, it's likely a dark theme

        except Exception as e:
            logger.error(f"Error detecting theme: {e}")
            return False  # Default to light theme on error

    def apply_theme(self, theme_name="dark", style_params=None):
        """Apply the current theme to the point counting page components.

        Args:
            theme_name (str): The name of the theme to apply
            style_params (dict, optional): Custom style parameters to use
        """
        try:
            # Try to use the new theme system
            from src.gui.styles.theme_config import apply_theme, get_palette
            from PySide6.QtWidgets import QApplication, QPushButton, QComboBox, QSlider, QSpinBox, QCheckBox

            # Get the current application instance
            app = QApplication.instance()
            if not app:
                logger.warning("No QApplication instance found when applying theme to point counting page")
                return

            # Get the current theme settings
            settings = app.property("settings")
            if settings:
                theme_base = settings.value("app/theme", "Dark Theme").lower().replace(" theme", "")
                color_scheme = settings.value("app/color_scheme", "Default").lower()
                font_family = settings.value("app/font_family", "Segoe UI")
                font_size_name = settings.value("app/font_size", "normal")
                theme_name = f"{color_scheme}-{theme_base}"
            else:
                # Use the provided theme name as fallback
                theme_name = theme_name.lower()
                font_family = "Segoe UI"
                font_size_name = "normal"

            # Get the font size from the font size name
            from src.gui.styles.theme_config import FONT_SIZES
            font_size = FONT_SIZES.get(font_size_name, 10)

            # Get the palette for the current theme
            palette = get_palette(theme_name)

            # Apply theme to all group boxes and frames to fix background colors
            from PySide6.QtWidgets import QGroupBox, QFrame
            for group_box in self.ui.point_counting_page.findChildren(QGroupBox):
                apply_theme(group_box, theme_name, font_family, font_size, style_params)
                # Remove any custom background color
                current_style = group_box.styleSheet()
                if "background-color" in current_style:
                    new_style = ""
                    for line in current_style.split(";"):
                        if "background-color" not in line:
                            new_style += line + ";"
                    group_box.setStyleSheet(new_style)

            # Apply theme to all frames
            for frame in self.ui.point_counting_page.findChildren(QFrame):
                apply_theme(frame, theme_name, font_family, font_size, style_params)
                # Remove any custom background color
                current_style = frame.styleSheet()
                if "background-color" in current_style:
                    new_style = ""
                    for line in current_style.split(";"):
                        if "background-color" not in line:
                            new_style += line + ";"
                    frame.setStyleSheet(new_style)

            # Apply theme to all buttons in the point counting page
            for button in self.ui.point_counting_page.findChildren(QPushButton):
                apply_theme(button, theme_name, font_family, font_size, style_params)
                # Reset any custom button styles
                if "Generate Points" in button.text() or "Clear Points" in button.text():
                    apply_theme(button, theme_name, font_family, font_size, style_params)  # Apply theme again to override custom styles

            # Apply theme to all comboboxes
            for combo in self.ui.point_counting_page.findChildren(QComboBox):
                apply_theme(combo, theme_name, font_family, font_size, style_params)

            # Apply theme to all sliders
            for slider in self.ui.point_counting_page.findChildren(QSlider):
                apply_theme(slider, theme_name, font_family, font_size, style_params)

            # Apply theme to all spinboxes
            for spinbox in self.ui.point_counting_page.findChildren(QSpinBox):
                apply_theme(spinbox, theme_name, font_family, font_size, style_params)

            # Apply theme to all checkboxes
            for checkbox in self.ui.point_counting_page.findChildren(QCheckBox):
                apply_theme(checkbox, theme_name, font_family, font_size, style_params)

            # Apply theme to the vertical class selector
            if hasattr(self.ui, 'vertical_class_selector'):
                apply_theme(self.ui.vertical_class_selector, theme_name, font_family, font_size, style_params)

            logger.info(f"Applied theme {theme_name} to point counting page components")

        except ImportError:
            logger.warning("Could not import theme_config module, using fallback theme")
            # Fallback to basic theme application
            if theme_name == "dark":
                # Apply dark theme styles
                pass
            else:
                # Apply light theme styles
                pass
        except Exception as e:
            logger.error(f"Error applying theme to point counting page: {e}")

    def on_grid_color_clicked(self):
        """Handle click on grid color button."""
        self.color_dialog.setCurrentColor(self.grid_color)
        if self.color_dialog.exec():
            new_color = self.color_dialog.selectedColor()
            if new_color.isValid() and new_color != self.grid_color:
                self.grid_color = new_color
                # Update slider to match alpha
                opacity = new_color.alpha() * 100 // 255
                self.ui.grid_opacity_slider.setValue(opacity)
                self._update_color_button_style(self.ui.grid_color_button, self.grid_color)
                self._update_display() # Update view with new color

                # Save to settings
                try:
                    from src.utils.settings_manager import settings_manager
                    if settings_manager is not None:
                        settings_manager.set_value("point_counting/grid_color_r", new_color.red())
                        settings_manager.set_value("point_counting/grid_color_g", new_color.green())
                        settings_manager.set_value("point_counting/grid_color_b", new_color.blue())
                        settings_manager.set_value("point_counting/grid_color_a", new_color.alpha())
                        settings_manager.set_value("point_counting/grid_opacity", opacity)
                except Exception as e:
                    logger.error(f"Error saving grid color to settings: {e}")

    def on_show_grid_lines_toggled(self, checked):
        """Handle toggle of show grid lines checkbox."""
        self.show_grid_lines = checked
        self._update_display()

    def on_auto_zoom_toggled(self, checked):
        """Handle toggling of auto-zoom feature."""
        self.auto_zoom_enabled = checked
        print(f"Auto-zoom {'enabled' if checked else 'disabled'}")

        # If auto-zoom was just enabled and we have a current point, center on it (preserve zoom level)
        if checked and self.current_point_index != self.UNCLASSIFIED_INDEX:
            state = self._get_current_state()
            if state and state.get('points') and 0 <= self.current_point_index < len(state['points']):
                point = state['points'][self.current_point_index]
                px = int(point[0])
                py = int(point[1])
                self.ui.point_counting_image_view.zoomToPoint(px, py)

    def on_add_class(self):
        """Add a new class."""
        state = self._get_current_state()
        if not state: return

        class_name, ok = QInputDialog.getText(self.ui.point_counting_page, "Add Class", "Enter class name:")
        if not (ok and class_name.strip()): return
        class_name = class_name.strip()

        if any(name == class_name for name, _ in state['classes']):
            QMessageBox.warning(self.ui.point_counting_page, "Duplicate Class", f"Class '{class_name}' already exists.")
            return

        # Suggest a color (implement generate_distinct_color if needed)
        suggested_color = QColor(random.randint(50, 200), random.randint(50, 200), random.randint(50, 200))
        self.color_dialog.setCurrentColor(suggested_color)
        if self.color_dialog.exec():
            color = self.color_dialog.selectedColor()
            if color.isValid():
                state['classes'].append((class_name, color))
                state['current_class_index'] = len(state['classes']) - 1 # Select new class
                self._update_ui_for_current_state()
                self._update_keyboard_shortcuts() # Update shortcuts for new class list
                self._update_status_label() # Refresh status

    def on_remove_class(self):
        """Remove the selected class."""
        state = self._get_current_state()
        if not state: return
        classes = state['classes']
        current_idx = state['current_class_index']

        if len(classes) <= 1:
            QMessageBox.warning(self.ui.point_counting_page, "Cannot Remove", "At least one class must remain.")
            return
        if not (0 <= current_idx < len(classes)): return

        class_name, _ = classes[current_idx]
        reply = QMessageBox.question(self.ui.point_counting_page, "Confirm Removal",
                                     f"Remove class '{class_name}'?\nPoints assigned to this class will become unclassified.",
                                     QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
        if reply == QMessageBox.Yes:
            del classes[current_idx]
            # Update points: set removed class index to unclassified, shift higher indices down
            new_points = []
            for point in state['points']:
                # Handle both 3-element and 4-element point tuples
                if len(point) >= 4:
                    x, y, c_idx, classification_method = point
                else:
                    x, y, c_idx = point[:3]
                    classification_method = self.MANUAL_CLASSIFICATION

                if c_idx == current_idx:
                    new_points.append((x, y, self.UNCLASSIFIED_INDEX, classification_method))
                elif c_idx > current_idx:
                    new_points.append((x, y, c_idx - 1, classification_method))
                else:
                    new_points.append((x, y, c_idx, classification_method))
            state['points'] = new_points
            # Adjust current class index if necessary
            state['current_class_index'] = max(0, current_idx - 1) if classes else 0
            self._update_ui_for_current_state()
            self._update_keyboard_shortcuts() # Update shortcuts after removal

    def on_rename_class(self):
        """Rename the selected class."""
        state = self._get_current_state()
        if not state: return
        classes = state['classes']
        current_idx = state['current_class_index']

        if not (0 <= current_idx < len(classes)): return

        old_name, color = classes[current_idx]
        new_name, ok = QInputDialog.getText(self.ui.point_counting_page, "Rename Class", "Enter new name:", text=old_name)

        if ok and new_name.strip() and new_name.strip() != old_name:
            new_name = new_name.strip()
            # Check for duplicates
            if any(name == new_name for i, (name, _) in enumerate(classes) if i != current_idx):
                 QMessageBox.warning(self.ui.point_counting_page, "Duplicate Name", f"Class '{new_name}' already exists.")
                 return
            # Update class name
            classes[current_idx] = (new_name, color)
            self._update_class_selector() # Update dropdown text
            self._update_summary() # Update summary text
            self.ui.vertical_class_selector.update_classes(classes) # Update floating selector

    def on_change_class_color(self):
        """Change the color of the selected class."""
        state = self._get_current_state()
        if not state: return
        classes = state['classes']
        current_idx = state['current_class_index']

        if not (0 <= current_idx < len(classes)): return

        name, old_color = classes[current_idx]
        self.color_dialog.setCurrentColor(old_color)
        if self.color_dialog.exec():
            new_color = self.color_dialog.selectedColor()
            if new_color.isValid() and new_color != old_color:
                classes[current_idx] = (name, new_color)
                self._update_ui_for_current_state() # Update display, selector, indicator

    def on_show_class_dropdown(self):
        """Show the class selector dropdown when the dropdown button is clicked."""
        logger.debug("Show class dropdown button clicked")

        # Get the current state
        state = self._get_current_state()
        if not state: return

        # Create a custom menu

        menu = QMenu(self.ui.point_counting_page)

        # Detect if we're using a dark theme
        is_dark_theme = self._is_dark_theme()

        if is_dark_theme:
            # Dark theme styling
            menu.setStyleSheet("""
                QMenu {
                    background-color: #2d2d2d;
                    border: 1px solid #555555;
                    border-radius: 4px;
                    padding: 5px;
                    min-width: 180px;
                    color: #e0e0e0;
                }
                QMenu::item {
                    padding: 8px 30px 8px 30px;
                    border-radius: 3px;
                    margin: 2px 0px;
                }
                QMenu::item:selected {
                    background-color: #0784b5;
                    color: white;
                }
                QMenu::icon {
                    padding-left: 10px;
                }
            """)
        else:
            # Light theme styling
            menu.setStyleSheet("""
                QMenu {
                    background-color: #f5f5f5;
                    border: 1px solid #888888;
                    border-radius: 4px;
                    padding: 5px;
                    min-width: 180px;
                }
                QMenu::item {
                    padding: 8px 30px 8px 30px;
                    border-radius: 3px;
                    margin: 2px 0px;
                }
                QMenu::item:selected {
                    background-color: #0784b5;
                    color: white;
                }
                QMenu::icon {
                    padding-left: 10px;
                }
            """)

        # Add class items to the menu
        for idx, (class_name, color) in enumerate(state['classes']):
            # Create a pixmap with a border for better visibility
            pixmap = QPixmap(20, 20)
            pixmap.fill(Qt.transparent)  # Start with transparent background using Qt from imports

            # Create a painter to draw on the pixmap
            painter = QPainter(pixmap)
            painter.setRenderHint(QPainter.Antialiasing)

            # Draw filled circle with border
            painter.setPen(QPen(Qt.black, 1))
            painter.setBrush(color)
            painter.drawEllipse(2, 2, 16, 16)
            painter.end()

            # Create action with the icon
            action = QAction(QIcon(pixmap), class_name, menu)

            # Use a lambda with default argument to capture the correct index
            action.triggered.connect(lambda checked=False, i=idx: self._select_class_from_menu(i))
            menu.addAction(action)

        # Show the menu below the button
        button_pos = self.ui.show_class_dropdown_button.mapToGlobal(self.ui.show_class_dropdown_button.rect().bottomLeft())
        menu.exec(button_pos)

    def _select_class_from_menu(self, index):
        """Handle selection from the custom class dropdown menu.

        Args:
            index: The index of the selected class
        """
        logger.debug(f"Class selected from menu: {index}")

        # Update the class selector combobox (this will trigger on_class_selected)
        self.ui.class_selector.setCurrentIndex(index)

    def _hide_class_selector_after_selection(self):
        """Hide the class selector after a selection is made."""
        # Disconnect the signal to prevent multiple calls
        try:
            self.ui.class_selector.activated.disconnect(self._hide_class_selector_after_selection)
        except:
            pass

        # Reset the combobox width to 0 to hide it
        self.ui.class_selector.setFixedWidth(0)

    def on_class_selected(self, index):
        """Handle selection change in the class combo box."""
        state = self._get_current_state()
        if not state: return
        if 0 <= index < len(state['classes']):
             if state['current_class_index'] != index:
                state['current_class_index'] = index
                self._update_class_color_indicator()
                self._update_status_label()
                self._update_class_display_label()  # Update the class display label
                # Optional: automatically classify current point when class changes?
                # self.classify_current_point(index)

    def on_toggle_quick_selector(self, checked):
        """Show/hide the vertical floating class selector."""
        if checked:
            # Position near top-right of image view, inside container
            container_geo = self.ui.point_counting_image_container.geometry()
            selector_hint = self.ui.vertical_class_selector.sizeHint()
            x = container_geo.width() - selector_hint.width() - 15
            y = 15
            self.ui.vertical_class_selector.move(max(10, x), y) # Ensure it's not off-screen left
            self.ui.vertical_class_selector.show()
            self.ui.vertical_class_selector.raise_() # Bring to front
        else:
            self.ui.vertical_class_selector.hide()

    def on_floating_class_selected(self, class_index):
         """Handle class selection from the floating vertical selector."""
         state = self._get_current_state()
         if not state: return
         if 0 <= class_index < len(state['classes']):
            self.ui.class_selector.setCurrentIndex(class_index) # Sync combobox
            # Automatically classify the current point AND move to the next one
            self.classify_current_point(class_index, move_to_next=True)

    def on_generate_points(self):
        """Generate points based on the selected method and parameters."""
        state = self._get_current_state()
        if self.current_image_data is None or not state:
            QMessageBox.warning(self.ui.point_counting_page, "Error", "No image loaded.")
            return

        if self.counting_method == "custom": # Should not be called in custom mode
            return

        # Confirm overwrite if points exist
        if state['points']:
            reply = QMessageBox.question(self.ui.point_counting_page, "Confirm",
                                         "This will clear existing points. Generate new points?",
                                         QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
            if reply == QMessageBox.No:
                return

        state['points'] = [] # Clear existing points for this image
        self.current_point_index = self.UNCLASSIFIED_INDEX
        height, width = self.current_image_data.shape[:2]
        new_points = []

        try:
            if self.counting_method == "grid":
                if self.grid_size <= 0: raise ValueError("Grid size must be positive.")
                rows, cols = self.grid_size, self.grid_size
                cell_h, cell_w = height / rows, width / cols
                off_x = (self.grid_offset_x / 100.0) * cell_w
                off_y = (self.grid_offset_y / 100.0) * cell_h

                for r in range(rows):
                    for c in range(cols):
                        x = (c + 0.5) * cell_w + off_x
                        y = (r + 0.5) * cell_h + off_y
                        # Clamp points to be within image bounds
                        x = max(0.0, min(float(width - 1), x))
                        y = max(0.0, min(float(height - 1), y))
                        new_points.append((x, y, self.UNCLASSIFIED_INDEX, self.MANUAL_CLASSIFICATION))

            elif self.counting_method == "random":
                if self.random_points_count <= 0: raise ValueError("Number of points must be positive.")
                rng = random.Random(self.random_seed) # Use seeded generator
                for _ in range(self.random_points_count):
                    x = rng.uniform(0, width - 1)
                    y = rng.uniform(0, height - 1)
                    new_points.append((x, y, self.UNCLASSIFIED_INDEX, self.MANUAL_CLASSIFICATION))

            state['points'] = new_points
            if new_points:
                self.current_point_index = 0 # Select first point

            self._update_ui_for_current_state()
            print(f"Generated {len(new_points)} points using {self.counting_method} method.")

        except Exception as e:
            QMessageBox.critical(self.ui.point_counting_page, "Generation Error", f"Error generating points: {e}")
            state['points'] = [] # Ensure points list is empty on error
            self.current_point_index = self.UNCLASSIFIED_INDEX
            self._update_ui_for_current_state()


    def on_clear_points(self):
        """Clear all points for the current image."""
        state = self._get_current_state()
        if not state or not state['points']: return

        reply = QMessageBox.question(self.ui.point_counting_page, "Confirm Clear",
                                     "Are you sure you want to clear all points for this image?",
                                     QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
        if reply == QMessageBox.Yes:
            state['points'] = []
            self.current_point_index = self.UNCLASSIFIED_INDEX
            self._update_ui_for_current_state()
            print("Points cleared.")

    def on_undo_auto_classify(self):
        """Revert all auto-classified points back to unclassified."""
        state = self._get_current_state()
        if not state or not state['points']:
            QMessageBox.warning(self.ui.point_counting_page, "No Points", "No points to revert.")
            return

        # Get points
        points = state['points']

        # Count auto-classified points
        auto_classified_count = 0

        # Revert auto-classified points to unclassified
        for i, point in enumerate(points):
            # Check if this is an auto-classified point
            if len(point) >= 4 and point[3] == self.AUTO_CLASSIFICATION:
                # Get coordinates
                x, y = point[:2]
                # Set to unclassified but preserve coordinates
                points[i] = (x, y, self.UNCLASSIFIED_INDEX, self.MANUAL_CLASSIFICATION)
                auto_classified_count += 1

        if auto_classified_count > 0:
            # Update UI
            self._update_ui_for_current_state()

            # Show results
            QMessageBox.information(self.ui.point_counting_page, "Undo Complete",
                                  f"Successfully reverted {auto_classified_count} auto-classified points back to unclassified.")
        else:
            QMessageBox.information(self.ui.point_counting_page, "No Auto-Classified Points",
                                  "There are no auto-classified points to revert.")

    def on_semi_auto_classify(self):
        """Perform semi-automatic classification of points based on manually classified examples."""
        state = self._get_current_state()
        if not state or not state['points']:
            QMessageBox.warning(self.ui.point_counting_page, "No Points", "No points to classify.")
            return

        # Get points and classes
        points = state['points']
        classes = state['classes']

        # Count manually classified points
        manually_classified = []
        unclassified = []

        for i, point in enumerate(points):
            if len(point) >= 4:
                x, y, class_idx, classification_method = point
            else:
                x, y, class_idx = point[:3]
                classification_method = self.MANUAL_CLASSIFICATION

            if class_idx != self.UNCLASSIFIED_INDEX:
                manually_classified.append((i, point))
            else:
                unclassified.append((i, point))

        # Check if we have enough manually classified points
        if len(manually_classified) < 3:
            QMessageBox.warning(self.ui.point_counting_page, "Not Enough Training Data",
                               "Please manually classify at least 3 points before using semi-auto classification.")
            return

        if len(unclassified) == 0:
            QMessageBox.information(self.ui.point_counting_page, "All Points Classified",
                                  "All points are already classified.")
            return

        # Show settings dialog
        settings_dialog = SemiAutoSettingsDialog(self.ui.point_counting_page)
        if not settings_dialog.exec():
            return  # User canceled

        settings = settings_dialog.get_settings()

        # Extract features and train classifier
        try:
            self._perform_semi_auto_classification(settings)
        except Exception as e:
            QMessageBox.critical(self.ui.point_counting_page, "Classification Error",
                               f"An error occurred during classification: {str(e)}")

    def _perform_semi_auto_classification(self, settings):
        """Perform the actual semi-automatic classification using the specified settings.

        Args:
            settings: Dictionary of settings from the settings dialog
        """
        state = self._get_current_state()
        if not state or not state['points'] or self.current_image_data is None:
            return

        # Create progress dialog
        progress = QProgressDialog("Performing semi-automatic classification...", "Cancel", 0, 100, self.ui.point_counting_page)
        progress.setWindowTitle("Semi-Auto Classification")
        progress.setWindowModality(Qt.WindowModal)
        progress.setValue(0)
        # Increase the size of the progress bar
        progress_bar = progress.findChild(QProgressBar)
        if progress_bar:
            progress_bar.setMinimumHeight(20)  # Increased height for better visibility
            progress_bar.setStyleSheet("QProgressBar { min-height: 20px; font-weight: bold; }")
        progress.setMinimumWidth(400)  # Make the dialog wider
        progress.show()

        try:
            # Get points and classes
            points = state['points']
            classes = state['classes']

            # Separate manually classified and unclassified points
            manually_classified_points = []
            manually_classified_labels = []
            unclassified_indices = []

            for i, point in enumerate(points):
                if len(point) >= 4:
                    x, y, class_idx, classification_method = point
                else:
                    x, y, class_idx = point[:3]
                    classification_method = self.MANUAL_CLASSIFICATION

                if class_idx != self.UNCLASSIFIED_INDEX:
                    # Only include x, y, class_idx for feature extraction
                    manually_classified_points.append((x, y))
                    manually_classified_labels.append(class_idx)
                else:
                    unclassified_indices.append(i)

            progress.setValue(10)
            if progress.wasCanceled():
                return

            # Create feature extractor
            feature_settings = settings['feature_extraction']
            extractor = PointFeatureExtractor(
                patch_size=feature_settings['patch_size'],
                use_color=feature_settings['use_color'],
                use_texture=feature_settings['use_texture']
            )

            # Extract features for manually classified points
            training_features = extractor.extract_features(self.current_image_data, manually_classified_points)

            progress.setValue(30)
            if progress.wasCanceled():
                return

            # Create and train classifier
            classifier_settings = settings['classifier']
            classifier_kwargs = {}

            # Add classifier-specific settings
            if classifier_settings['type'] == 'knn':
                classifier_kwargs['n_neighbors'] = classifier_settings.get('n_neighbors', 5)
                classifier_kwargs['weights'] = classifier_settings.get('weights', 'distance')
            elif classifier_settings['type'] == 'svm':
                classifier_kwargs['C'] = classifier_settings.get('C', 1.0)
                classifier_kwargs['kernel'] = classifier_settings.get('kernel', 'rbf')
                classifier_kwargs['gamma'] = classifier_settings.get('gamma', 'scale')
            elif classifier_settings['type'] == 'rf':
                classifier_kwargs['n_estimators'] = classifier_settings.get('n_estimators', 100)
                classifier_kwargs['max_depth'] = classifier_settings.get('max_depth', None)

            classifier = PointClassifier(classifier_type=classifier_settings['type'], **classifier_kwargs)

            # Train classifier
            success = classifier.train(training_features, np.array(manually_classified_labels))
            if not success:
                QMessageBox.warning(self.ui.point_counting_page, "Training Failed",
                                  "Failed to train classifier. Please try different settings or classify more points manually.")
                return
            
            # Store the trained classifier for export
            self.trained_classifier = classifier
            self.ui.export_classifier_button.setEnabled(True)

            progress.setValue(50)
            if progress.wasCanceled():
                return

            # Extract features for unclassified points
            unclassified_points = []
            for i in unclassified_indices:
                point = points[i]
                if len(point) >= 4:
                    x, y, _, _ = point
                else:
                    x, y, _ = point[:3]
                unclassified_points.append((x, y))

            unclassified_features = extractor.extract_features(self.current_image_data, unclassified_points)

            progress.setValue(70)
            if progress.wasCanceled():
                return

            # Predict classes for unclassified points
            if classifier_settings.get('min_confidence', 0) > 0:
                # Use probability prediction if confidence threshold is set
                probabilities = classifier.predict_proba(unclassified_features)
                predictions = []
                for probs in probabilities:
                    max_prob = np.max(probs)
                    if max_prob >= classifier_settings['min_confidence']:
                        predictions.append(np.argmax(probs))
                    else:
                        predictions.append(self.UNCLASSIFIED_INDEX)  # Keep as unclassified if below threshold
            else:
                # Use simple prediction otherwise
                predictions = classifier.predict(unclassified_features)

            progress.setValue(90)
            if progress.wasCanceled():
                return

            # Update points with predicted classes
            classified_count = 0
            for i, prediction in zip(unclassified_indices, predictions):
                if prediction != self.UNCLASSIFIED_INDEX:
                    point = points[i]
                    if len(point) >= 4:
                        x, y, _, _ = point
                    else:
                        x, y = point[:2]

                    points[i] = (x, y, prediction, self.AUTO_CLASSIFICATION)
                    classified_count += 1

            progress.setValue(100)

            # Update UI
            self._update_ui_for_current_state()

            # Show results
            result_msg = f"Successfully classified {classified_count} out of {len(unclassified_indices)} points."
            if classified_count > 0:
                result_msg += "\n\nYou can now export this trained classifier for reuse on other images."
            QMessageBox.information(self.ui.point_counting_page, "Classification Complete", result_msg)

        finally:
            progress.close()

    def on_export_points(self):
        """Export point data for the current image to a CSV file."""
        state = self._get_current_state()
        if not state or not state['points']:
            QMessageBox.warning(self.ui.point_counting_page, "No Points", "No points to export.")
            return

        points = state['points']
        classes = state['classes']
        base_filename = os.path.splitext(os.path.basename(self.current_image_path))[0]
        default_path = os.path.join(os.path.dirname(self.current_image_path), f"{base_filename}_points.csv")

        file_path, _ = QFileDialog.getSaveFileName(self.ui.point_counting_page, "Export Points to CSV",
                                                   default_path, "CSV Files (*.csv)")
        if not file_path: return

        try:
            with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.writer(csvfile)
                writer.writerow(["PointID", "X", "Y", "ClassID", "ClassName", "ClassificationMethod"])
                for i, point in enumerate(points):
                    # Handle both 3-element and 4-element point tuples
                    if len(point) >= 4:
                        x, y, class_idx, classification_method = point
                    else:
                        x, y, class_idx = point[:3]
                        classification_method = self.MANUAL_CLASSIFICATION

                    class_name = "Unclassified"
                    if 0 <= class_idx < len(classes):
                        class_name = classes[class_idx][0]
                    elif class_idx != self.UNCLASSIFIED_INDEX:
                        class_name = "INVALID_CLASS_ID"

                    method_name = "Auto" if classification_method == self.AUTO_CLASSIFICATION else "Manual"
                    writer.writerow([i + 1, f"{x:.2f}", f"{y:.2f}", class_idx, class_name, method_name])
            QMessageBox.information(self.ui.point_counting_page, "Export Successful", f"Exported {len(points)} points to\n{file_path}")
        except Exception as e:
            QMessageBox.critical(self.ui.point_counting_page, "Export Error", f"Error exporting data: {e}")

    def on_export_classifier(self):
        """Export the trained classifier to a file."""
        if not self.trained_classifier or not self.trained_classifier.is_trained:
            QMessageBox.warning(self.ui.point_counting_page, "No Trained Classifier", 
                              "No trained classifier available to export. Please run semi-automatic classification first.")
            return

        # Get default filename based on current image
        base_filename = "point_classifier"
        if self.current_image_path:
            base_filename = os.path.splitext(os.path.basename(self.current_image_path))[0] + "_classifier"
        
        default_path = os.path.join(os.path.dirname(self.current_image_path) if self.current_image_path else os.getcwd(), 
                                   f"{base_filename}.pkl")

        file_path, _ = QFileDialog.getSaveFileName(
            self.ui.point_counting_page, 
            "Export Trained Classifier",
            default_path, 
            "Pickle Files (*.pkl);;All Files (*)"
        )
        
        if not file_path:
            return

        try:
            success = self.trained_classifier.save_classifier(file_path)
            if success:
                QMessageBox.information(self.ui.point_counting_page, "Export Successful", 
                                       f"Classifier exported successfully to:\n{file_path}")
            else:
                QMessageBox.critical(self.ui.point_counting_page, "Export Failed", 
                                    "Failed to export classifier. Please try again.")
        except Exception as e:
            QMessageBox.critical(self.ui.point_counting_page, "Export Error", f"Error exporting classifier: {e}")

    def on_import_classifier(self):
        """Import a pre-trained classifier from a file."""
        file_path, _ = QFileDialog.getOpenFileName(
            self.ui.point_counting_page,
            "Import Pre-trained Classifier",
            os.getcwd(),
            "Pickle Files (*.pkl);;All Files (*)"
        )
        
        if not file_path:
            return

        try:
            # Load the classifier
            loaded_classifier = PointClassifier.create_from_file(file_path)
            if loaded_classifier and loaded_classifier.is_trained:
                self.loaded_classifier = loaded_classifier
                self.ui.use_pretrained_button.setEnabled(True)
                
                # Show classifier info
                classifier_info = f"Classifier Type: {loaded_classifier.classifier_type.upper()}\n"
                classifier_info += f"Status: Trained and ready to use"
                
                QMessageBox.information(self.ui.point_counting_page, "Import Successful", 
                                       f"Pre-trained classifier imported successfully!\n\n{classifier_info}\n\n"
                                       f"You can now use 'Use Pre-trained Classifier' to apply it to unclassified points.")
            else:
                QMessageBox.critical(self.ui.point_counting_page, "Import Failed", 
                                    "Failed to load classifier or classifier is not trained.")
        except Exception as e:
            QMessageBox.critical(self.ui.point_counting_page, "Import Error", f"Error importing classifier: {e}")

    def on_use_pretrained_classifier(self):
        """Apply the loaded pre-trained classifier to classify unclassified points."""
        if not self.loaded_classifier or not self.loaded_classifier.is_trained:
            QMessageBox.warning(self.ui.point_counting_page, "No Pre-trained Classifier", 
                              "No pre-trained classifier loaded. Please import a classifier first.")
            return

        state = self._get_current_state()
        if not state or not state['points']:
            QMessageBox.warning(self.ui.point_counting_page, "No Points", "No points to classify.")
            return

        if self.current_image_data is None:
            QMessageBox.warning(self.ui.point_counting_page, "No Image", "No image loaded.")
            return

        # Get points and find unclassified ones
        points = state['points']
        unclassified_indices = []
        unclassified_points = []

        for i, point in enumerate(points):
            if len(point) >= 4:
                x, y, class_idx, _ = point
            else:
                x, y, class_idx = point[:3]

            if class_idx == self.UNCLASSIFIED_INDEX:
                unclassified_indices.append(i)
                unclassified_points.append((x, y))

        if not unclassified_points:
            QMessageBox.information(self.ui.point_counting_page, "No Unclassified Points", 
                                   "All points are already classified.")
            return

        # Create progress dialog
        progress = QProgressDialog("Applying pre-trained classifier...", "Cancel", 0, 100, self.ui.point_counting_page)
        progress.setWindowTitle("Pre-trained Classification")
        progress.setWindowModality(Qt.WindowModal)
        progress.setValue(0)
        progress.show()

        try:
            # Use default feature extraction settings (could be made configurable)
            extractor = PointFeatureExtractor(patch_size=16, use_color=True, use_texture=True)
            
            progress.setValue(30)
            if progress.wasCanceled():
                return

            # Extract features for unclassified points
            unclassified_features = extractor.extract_features(self.current_image_data, unclassified_points)
            
            progress.setValue(60)
            if progress.wasCanceled():
                return

            # Predict classes
            predictions = self.loaded_classifier.predict(unclassified_features)
            
            progress.setValue(80)
            if progress.wasCanceled():
                return

            # Update points with predicted classes
            classified_count = 0
            for i, prediction in zip(unclassified_indices, predictions):
                if prediction != self.UNCLASSIFIED_INDEX:
                    point = points[i]
                    if len(point) >= 4:
                        x, y, _, _ = point
                    else:
                        x, y = point[:2]

                    points[i] = (x, y, prediction, self.AUTO_CLASSIFICATION)
                    classified_count += 1

            progress.setValue(100)

            # Update UI
            self._update_ui_for_current_state()

            # Show results
            QMessageBox.information(self.ui.point_counting_page, "Classification Complete", 
                                   f"Pre-trained classifier successfully classified {classified_count} out of {len(unclassified_indices)} points.")

        except Exception as e:
            QMessageBox.critical(self.ui.point_counting_page, "Classification Error", f"Error applying pre-trained classifier: {e}")
        finally:
            progress.close()

    # --- Point Navigation and Classification ---

    def on_next_point(self):
        """Navigate to the next point, prioritizing unclassified."""
        state = self._get_current_state()
        if not state or not state['points']: return
        points = state['points']
        total_points = len(points)
        if total_points == 0: return

        current_idx = self.current_point_index
        next_idx = (current_idx + 1) % total_points if current_idx != self.UNCLASSIFIED_INDEX else 0

        # Try finding next unclassified from current position
        search_idx = next_idx
        while True:
             point = points[search_idx]
             class_idx = point[2] if len(point) >= 3 else self.UNCLASSIFIED_INDEX

             if class_idx == self.UNCLASSIFIED_INDEX:
                 self.current_point_index = search_idx
                 self._update_display()
                 self._update_status_label()

                 # Get point coordinates
                 px = int(point[0])
                 py = int(point[1])
                 # Center on the point (preserve current zoom level)
                 self.ui.point_counting_image_view.zoomToPoint(px, py)
                 return

             search_idx = (search_idx + 1) % total_points
             if search_idx == next_idx: # Cycled through all points
                 break

        # If no unclassified found, just move to the next sequential point
        self.current_point_index = next_idx
        self._update_display()
        self._update_status_label()

        # Get point coordinates
        point = points[next_idx]
        px = int(point[0])
        py = int(point[1])
        # Center on the point (preserve current zoom level)
        self.ui.point_counting_image_view.zoomToPoint(px, py)


    def on_prev_point(self):
        """Navigate to the previous point."""
        state = self._get_current_state()
        if not state or not state['points']: return
        points = state['points']
        total_points = len(points)
        if total_points == 0: return

        current_idx = self.current_point_index
        if current_idx == self.UNCLASSIFIED_INDEX:
            prev_idx = total_points - 1 # Wrap around from start
        else:
            prev_idx = (current_idx - 1 + total_points) % total_points

        self.current_point_index = prev_idx
        self._update_display()
        self._update_status_label()

        # Get point coordinates
        point = points[prev_idx]
        px = int(point[0])
        py = int(point[1])
        # Center on the point (preserve current zoom level)
        self.ui.point_counting_image_view.zoomToPoint(px, py)

    def on_classify_by_shortcut(self, class_index):
        """Classify the current point using keyboard shortcut (1-9 keys)."""
        state = self._get_current_state()
        if not state: return
        if 0 <= class_index < len(state['classes']):
            self.classify_current_point(class_index, move_to_next=True) # Classify and move next

    def on_image_view_mouse_press(self, scaled_pos: QPoint, event_buttons=Qt.LeftButton):
        """Handle mouse press on the image view."""
        print(f"Mouse press detected at {scaled_pos.x()}, {scaled_pos.y()}, method: {self.counting_method}")

        if self.current_image_data is None:
            print("No image data available")
            return

        # Default to left button if not specified (for backward compatibility)
        if event_buttons != Qt.LeftButton:
            print(f"Ignoring non-left button click: {event_buttons}")
            return

        state = self._get_current_state(create_if_missing=True)
        if not state:
            print("Failed to get or create state")
            return

        # Use the already scaled position from QPixmapView
        x, y = float(scaled_pos.x()), float(scaled_pos.y())
        height, width = self.current_image_data.shape[:2]
        print(f"Image dimensions: {width}x{height}, Click position: {x}, {y}")

        # Check if within image bounds
        if not (0 <= x < width and 0 <= y < height):
            print(f"Click outside image bounds: {x}, {y}")
            return

        points = state['points']
        classes = state['classes']
        current_class_idx = state['current_class_index']
        print(f"Current class index: {current_class_idx}, Total classes: {len(classes)}, Total points: {len(points)}")

        # Check if clicking near an existing point
        click_radius_sq = (self.HIGHLIGHT_RADIUS * 1.5) ** 2 # Slightly larger click radius
        found_point_idx = -1
        min_dist_sq = float('inf')

        for i, point in enumerate(points):
            # Handle both 3-element and 4-element point tuples
            if len(point) >= 4:
                px, py, _, _ = point
            else:
                px, py = point[:2]

            dist_sq = (x - px)**2 + (y - py)**2
            if dist_sq < click_radius_sq and dist_sq < min_dist_sq:
                min_dist_sq = dist_sq
                found_point_idx = i

        if found_point_idx != -1:
            # Clicked on an existing point - classify it
            self.current_point_index = found_point_idx
            self.classify_current_point(current_class_idx, move_to_next=False) # Classify, don't move
        elif self.counting_method == "custom":
            # Clicked on empty space in Manual mode - add a new point
            print(f"Manual click mode: Adding point at {x:.1f}, {y:.1f}")
            if 0 <= current_class_idx < len(classes):
                points.append((x, y, current_class_idx, self.MANUAL_CLASSIFICATION))
                self.current_point_index = len(points) - 1 # Select the new point
                self._update_ui_for_current_state() # Update display, summary, etc.
                print(f"Added point {len(points)} at ({x:.1f}, {y:.1f}) as {classes[current_class_idx][0]}")
                # Force update display to make sure the point is visible
                self._update_display()
            else:
                 print(f"Invalid class index: {current_class_idx}")
                 QMessageBox.warning(self.ui.point_counting_page, "Warning", "Cannot add point: Invalid class selected.")

    def classify_current_point(self, class_index, move_to_next=False, classification_method=None):
        """Assigns the given class_index to the current point.

        Args:
            class_index: The index of the class to assign
            move_to_next: Whether to move to the next point after classification
            classification_method: The method used for classification (MANUAL_CLASSIFICATION or AUTO_CLASSIFICATION)
        """
        state = self._get_current_state()
        if not state: return
        points = state.get('points', [])
        classes = state.get('classes', [])
        current_idx = self.current_point_index

        if not (0 <= current_idx < len(points)):
            # No valid point selected, maybe select the first unclassified?
            # Find first unclassified index
            first_unclassified = next((i for i, p in enumerate(points) if p[2] == self.UNCLASSIFIED_INDEX), -1)
            if first_unclassified != -1:
                 self.current_point_index = first_unclassified
                 current_idx = first_unclassified
            else:
                 print("No point selected or all points classified.")
                 return # Or maybe show a message

        if 0 <= class_index < len(classes) and 0 <= current_idx < len(points):
            # Use the specified classification method or default to manual
            method = classification_method if classification_method is not None else self.MANUAL_CLASSIFICATION

            # Get existing point data
            point = points[current_idx]
            if len(point) >= 4:
                x, y, _, _ = point
            else:
                x, y = point[:2]

            # Update the point's class index and classification method
            points[current_idx] = (x, y, class_index, method)
            print(f"Classified point {current_idx + 1} as '{classes[class_index][0]}' using {'auto' if method == self.AUTO_CLASSIFICATION else 'manual'} classification")

            # Update UI immediately
            self._update_display() # Redraw points
            self._update_summary()
            self._update_class_percentages()
            self._update_status_label()

            if move_to_next:
                # Use QTimer to move to the next point slightly later,
                # allowing the user to see the classification change.
                QTimer.singleShot(100, self.on_next_point)
        else:
            print(f"Invalid class index ({class_index}) or point index ({current_idx}) for classification.")

    def clear_point_counting_gallery(self):
        """Clears all images from the point counting gallery."""
        if hasattr(self.ui, 'point_counting_gallery'):
            self.ui.point_counting_gallery.clear_images()
            logger.info("Point counting gallery cleared successfully")
        
        # Clear the point counting image view as well
        if hasattr(self.ui, 'point_counting_image_view'):
            self.ui.point_counting_image_view.clear()
            logger.info("Point counting image view cleared")


    def apply_theme(self, theme_name, style_params=None):
        """Apply theme to the Point Counting page.
        
        Args:
            theme_name: Name of the theme to apply
            style_params: Optional style parameters from theme config
        """
        from ..styles.theme_config import get_stylesheet_for_scheme, FONT_SIZES
        from ..utils.settings_manager import SettingsManager
        
        # Get current font settings
        settings = SettingsManager()
        font_family = settings.get_setting('app/font_family', 'Segoe UI')
        font_size_key = settings.get_setting('app/font_size', 'normal')
        font_size = FONT_SIZES.get(font_size_key, FONT_SIZES['normal'])
        
        # Apply global stylesheet to the point counting page
        if hasattr(self.ui, 'point_counting_page'):
            stylesheet = get_stylesheet_for_scheme(theme_name, font_family, font_size)
            self.ui.point_counting_page.setStyleSheet(stylesheet)
            
        # Remove hardcoded font styles from specific elements
        if hasattr(self.ui, 'point_counting_page'):
            # Find and update class selector frame
            class_selector_frame = self.ui.point_counting_page.findChild(QFrame, 'class_selector_frame')
            if class_selector_frame:
                # Remove hardcoded font styling, let global theme handle it
                class_selector_frame.setStyleSheet("")
                
            # Find and update class display label
            class_display_label = self.ui.point_counting_page.findChild(QLabel, 'class_display_label')
            if class_display_label:
                # Remove hardcoded font styling, let global theme handle it
                class_display_label.setStyleSheet("")
                
            # Find and update current class name label
            current_class_name_label = self.ui.point_counting_page.findChild(QLabel, 'current_class_name_label')
            if current_class_name_label:
                # Remove hardcoded font styling, let global theme handle it
                current_class_name_label.setStyleSheet("")

    def cleanup(self):
        """Called when the application is closing or page is hidden."""
        print("Point Counting: Cleaning up and saving current state.")
        self.save_current_state_to_project()