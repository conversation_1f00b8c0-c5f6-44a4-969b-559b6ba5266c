// VisionLab AI - Service Worker
// Provides offline functionality and caching for better performance

const CACHE_NAME = 'visionlab-ai-v1.0.0';
const STATIC_CACHE = 'visionlab-static-v1';
const DYNAMIC_CACHE = 'visionlab-dynamic-v1';

// Files to cache for offline functionality
const STATIC_FILES = [
    '/',
    '/index.html',
    '/styles.css',
    '/script.js',
    '/manifest.json',
    // External resources (cached when accessed)
    'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap',
    'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css'
];

// Dynamic files that should be cached when accessed
const DYNAMIC_FILES = [
    '/confirm.php',
    '/process_waitlist.php',
    '/process_contact.php'
];

// Install event - cache static files
self.addEventListener('install', event => {
    console.log('Service Worker: Installing...');
    
    event.waitUntil(
        caches.open(STATIC_CACHE)
            .then(cache => {
                console.log('Service Worker: Caching static files');
                return cache.addAll(STATIC_FILES);
            })
            .catch(error => {
                console.error('Service Worker: Error caching static files:', error);
            })
    );
    
    // Force the waiting service worker to become the active service worker
    self.skipWaiting();
});

// Activate event - clean up old caches
self.addEventListener('activate', event => {
    console.log('Service Worker: Activating...');
    
    event.waitUntil(
        caches.keys().then(cacheNames => {
            return Promise.all(
                cacheNames.map(cacheName => {
                    if (cacheName !== STATIC_CACHE && cacheName !== DYNAMIC_CACHE) {
                        console.log('Service Worker: Deleting old cache:', cacheName);
                        return caches.delete(cacheName);
                    }
                })
            );
        })
    );
    
    // Ensure the service worker takes control immediately
    self.clients.claim();
});

// Fetch event - serve cached files or fetch from network
self.addEventListener('fetch', event => {
    const { request } = event;
    const url = new URL(request.url);
    
    // Skip non-GET requests
    if (request.method !== 'GET') {
        return;
    }
    
    // Skip chrome-extension and other non-http(s) requests
    if (!url.protocol.startsWith('http')) {
        return;
    }
    
    // Handle different types of requests
    if (isStaticAsset(request.url)) {
        // Static assets - cache first strategy
        event.respondWith(cacheFirst(request));
    } else if (isDynamicContent(request.url)) {
        // Dynamic content - network first strategy
        event.respondWith(networkFirst(request));
    } else if (isAPIRequest(request.url)) {
        // API requests - network only (don't cache)
        event.respondWith(networkOnly(request));
    } else {
        // Default - stale while revalidate
        event.respondWith(staleWhileRevalidate(request));
    }
});

// Cache first strategy - good for static assets
function cacheFirst(request) {
    return caches.match(request)
        .then(cachedResponse => {
            if (cachedResponse) {
                return cachedResponse;
            }
            
            return fetch(request)
                .then(networkResponse => {
                    // Cache the response for future use
                    if (networkResponse.status === 200) {
                        const responseClone = networkResponse.clone();
                        caches.open(STATIC_CACHE)
                            .then(cache => {
                                cache.put(request, responseClone);
                            });
                    }
                    return networkResponse;
                })
                .catch(error => {
                    console.error('Service Worker: Network request failed:', error);
                    // Return a fallback response if available
                    return getFallbackResponse(request);
                });
        });
}

// Network first strategy - good for dynamic content
function networkFirst(request) {
    return fetch(request)
        .then(networkResponse => {
            // Cache successful responses
            if (networkResponse.status === 200) {
                const responseClone = networkResponse.clone();
                caches.open(DYNAMIC_CACHE)
                    .then(cache => {
                        cache.put(request, responseClone);
                    });
            }
            return networkResponse;
        })
        .catch(error => {
            console.error('Service Worker: Network request failed, trying cache:', error);
            // Fallback to cache if network fails
            return caches.match(request)
                .then(cachedResponse => {
                    if (cachedResponse) {
                        return cachedResponse;
                    }
                    // Return fallback response
                    return getFallbackResponse(request);
                });
        });
}

// Network only strategy - for API requests that shouldn't be cached
function networkOnly(request) {
    return fetch(request)
        .catch(error => {
            console.error('Service Worker: API request failed:', error);
            // Return a custom offline response for API requests
            return new Response(
                JSON.stringify({
                    success: false,
                    message: 'You are currently offline. Please try again when you have an internet connection.',
                    offline: true
                }),
                {
                    status: 503,
                    statusText: 'Service Unavailable',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                }
            );
        });
}

// Stale while revalidate strategy - good for general content
function staleWhileRevalidate(request) {
    return caches.match(request)
        .then(cachedResponse => {
            // Fetch from network in background
            const networkFetch = fetch(request)
                .then(networkResponse => {
                    if (networkResponse.status === 200) {
                        const responseClone = networkResponse.clone();
                        caches.open(DYNAMIC_CACHE)
                            .then(cache => {
                                cache.put(request, responseClone);
                            });
                    }
                    return networkResponse;
                })
                .catch(error => {
                    console.error('Service Worker: Background fetch failed:', error);
                });
            
            // Return cached response immediately if available
            if (cachedResponse) {
                return cachedResponse;
            }
            
            // Otherwise wait for network response
            return networkFetch;
        });
}

// Helper functions
function isStaticAsset(url) {
    return /\.(css|js|png|jpg|jpeg|gif|svg|woff|woff2|ttf|eot|ico)$/i.test(url) ||
           url.includes('fonts.googleapis.com') ||
           url.includes('cdnjs.cloudflare.com');
}

function isDynamicContent(url) {
    return url.includes('.php') && !isAPIRequest(url);
}

function isAPIRequest(url) {
    return url.includes('process_waitlist.php') ||
           url.includes('process_contact.php') ||
           url.includes('/api/');
}

function getFallbackResponse(request) {
    const url = new URL(request.url);
    
    // Return offline page for HTML requests
    if (request.headers.get('accept').includes('text/html')) {
        return caches.match('/index.html')
            .then(response => {
                if (response) {
                    return response;
                }
                // Create a basic offline response
                return new Response(
                    `<!DOCTYPE html>
                    <html>
                    <head>
                        <title>VisionLab AI - Offline</title>
                        <meta charset="UTF-8">
                        <meta name="viewport" content="width=device-width, initial-scale=1.0">
                        <style>
                            body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
                            .offline { color: #666; }
                            .logo { font-size: 2em; color: #2563eb; margin-bottom: 20px; }
                        </style>
                    </head>
                    <body>
                        <div class="logo">🔍 VisionLab AI</div>
                        <h1>You're Offline</h1>
                        <p class="offline">Please check your internet connection and try again.</p>
                        <button onclick="window.location.reload()">Retry</button>
                    </body>
                    </html>`,
                    {
                        status: 200,
                        statusText: 'OK',
                        headers: {
                            'Content-Type': 'text/html'
                        }
                    }
                );
            });
    }
    
    // Return 404 for other requests
    return new Response('Not Found', {
        status: 404,
        statusText: 'Not Found'
    });
}

// Background sync for form submissions (if supported)
self.addEventListener('sync', event => {
    console.log('Service Worker: Background sync triggered:', event.tag);
    
    if (event.tag === 'waitlist-submission') {
        event.waitUntil(syncWaitlistSubmissions());
    } else if (event.tag === 'contact-submission') {
        event.waitUntil(syncContactSubmissions());
    }
});

// Sync pending waitlist submissions
function syncWaitlistSubmissions() {
    return new Promise((resolve, reject) => {
        // Get pending submissions from IndexedDB
        // This would require implementing IndexedDB storage in the main script
        console.log('Service Worker: Syncing waitlist submissions...');
        resolve();
    });
}

// Sync pending contact submissions
function syncContactSubmissions() {
    return new Promise((resolve, reject) => {
        // Get pending submissions from IndexedDB
        // This would require implementing IndexedDB storage in the main script
        console.log('Service Worker: Syncing contact submissions...');
        resolve();
    });
}

// Push notification handling (if implemented)
self.addEventListener('push', event => {
    console.log('Service Worker: Push notification received');
    
    const options = {
        body: 'VisionLab AI has an update for you!',
        icon: '/icon-192x192.png',
        badge: '/icon-72x72.png',
        vibrate: [100, 50, 100],
        data: {
            dateOfArrival: Date.now(),
            primaryKey: 1
        },
        actions: [
            {
                action: 'explore',
                title: 'Explore',
                icon: '/icon-explore.png'
            },
            {
                action: 'close',
                title: 'Close',
                icon: '/icon-close.png'
            }
        ]
    };
    
    if (event.data) {
        const data = event.data.json();
        options.body = data.body || options.body;
        options.title = data.title || 'VisionLab AI';
    }
    
    event.waitUntil(
        self.registration.showNotification('VisionLab AI', options)
    );
});

// Notification click handling
self.addEventListener('notificationclick', event => {
    console.log('Service Worker: Notification clicked');
    
    event.notification.close();
    
    if (event.action === 'explore') {
        event.waitUntil(
            clients.openWindow('/')
        );
    } else if (event.action === 'close') {
        // Just close the notification
        return;
    } else {
        // Default action - open the app
        event.waitUntil(
            clients.openWindow('/')
        );
    }
});

// Message handling from main thread
self.addEventListener('message', event => {
    console.log('Service Worker: Message received:', event.data);
    
    if (event.data && event.data.type === 'SKIP_WAITING') {
        self.skipWaiting();
    }
    
    if (event.data && event.data.type === 'GET_VERSION') {
        event.ports[0].postMessage({ version: CACHE_NAME });
    }
});

// Periodic background sync (if supported)
self.addEventListener('periodicsync', event => {
    console.log('Service Worker: Periodic sync triggered:', event.tag);
    
    if (event.tag === 'update-check') {
        event.waitUntil(checkForUpdates());
    }
});

function checkForUpdates() {
    return fetch('/version.json')
        .then(response => response.json())
        .then(data => {
            if (data.version !== CACHE_NAME) {
                console.log('Service Worker: New version available');
                // Notify the main thread about the update
                return self.registration.showNotification('Update Available', {
                    body: 'A new version of VisionLab AI is available!',
                    icon: '/icon-192x192.png',
                    tag: 'update-available'
                });
            }
        })
        .catch(error => {
            console.error('Service Worker: Error checking for updates:', error);
        });
}