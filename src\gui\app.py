
# src/gui/app.py

import os
import numpy as np
import cv2
import logging
from PIL import Image
from PySide6.QtWidgets import (QFileDialog, QMessageBox, QDialog, QColorDialog, QWidget,
                              QGraphicsScene, QApplication, QMenu) # Added QApplication
from PySide6.QtCore import Qt, QPoint, Slot
from PySide6.QtGui import QPixmap, QIcon # Added QIcon for Point Counting class selector
import json


# --- UI Import ---
from src.gui.ui.app_ui import VisionLabAiAppUI

# --- Handler Imports ---
from src.gui.handlers.image_handlers import ImageHandlers
from src.gui.handlers.segmentation_handlers import SegmentationHandlers
from src.gui.handlers.analysis_handlers import AnalysisHandlers
from src.gui.handlers.settings_handlers import SettingsHandlers
from src.gui.handlers.trainable_segmentation_handlers import TrainableSegmentationHandlers
from src.gui.handlers.gallery_handlers import GalleryHandlers
from src.gui.handlers.ai_assistant_handlers import AIAssistantHandlers
from src.gui.handlers.ai_assistant_handlers_markdown import AIAssistantMarkdownHandlers
# Import the NEW Point Counting Handler
from src.gui.handlers.point_counting_page_handler import PointCountingPageHandler
# Import the Advanced Segmentation Page Handler
from src.gui.handlers.advanced_segmentation_page_handler import AdvancedSegmentationPageHandler
# Import the About Page Handler
from src.gui.handlers.about_page_handler import AboutPageHandler

# --- Utility and Widget Imports ---
from src.utils.coco_utils import generate_coco_json
from src.gui.preprocessing_dialog import PreprocessingDialog
from src.gui.multi_image_gallery import MultiImageGallery
from src.utils.image_utils import resize_image, convert_cvimage_to_qpixmap, convert_cvimage_to_qimage
from src.grainsight_components.gui.widgets import CustomPixmapItem # Assuming this is used elsewhere
from src.gui.grain_analysis_widget import GrainAnalysisWidget
from src.gui.project_hub_page import ProjectHubPage
from src.gui.image_lab_page import ImageLabPage  # Import the new Image Lab page
from src.gui.advanced_segmentation_page import AdvancedSegmentationPage  # Import the Advanced Segmentation page
from src.gui.batch_processing_page import BatchProcessingPage  # Import the new Batch Processing page
# Import multi-image handler if used by trainable segmentation
try:
    from src.gui.handlers.multi_image_trainable_handler import MultiImageTrainableHandler
except ImportError:
    MultiImageTrainableHandler = None # Handle cases where it might not exist yet
    print("Warning: MultiImageTrainableHandler not found.")


logger = logging.getLogger(__name__)

class VisionLabAiApp(VisionLabAiAppUI, ImageHandlers, SegmentationHandlers, SettingsHandlers, TrainableSegmentationHandlers, GalleryHandlers, AIAssistantHandlers, AIAssistantMarkdownHandlers, AnalysisHandlers):
    """Main application class for VisionLab Ai that combines UI and functionality.

    Note: AnalysisHandlers removed to decouple Image Lab page from main app.
    Image Lab page now manages its own state independently.
    """

    def __getattr__(self, name):
        """Handle missing attributes gracefully."""
        if name.startswith('default_yolo_'):
            # Return a dummy object for YOLOv8 settings
            class DummyValue:
                def value(self):
                    return 0.25  # Default value
            return DummyValue()
        raise AttributeError(f"'{self.__class__.__name__}' object has no attribute '{name}'")

    def set_project(self, project):
        """Set the project for all handlers and components.

        Args:
            project: The project object
        """
        logger.info(f"Setting project for all handlers: {project.name if project else 'None'}")

        # Store the project in the app
        self.project = project

        # Propagate project to handlers/widgets that need it
        if hasattr(self, 'image_processing_page'):
            self.image_processing_page.project = project

        # Set project for trainable segmentation handlers
        # This is the most important part for our current issue
        if hasattr(self, 'trainable_segmentation_page') and hasattr(self.trainable_segmentation_page, 'handlers'):
            # Call the TrainableSegmentationHandlers.set_project method
            self.set_project_for_trainable_segmentation(project)

        if hasattr(self, 'grain_analysis_widget'):
            self.grain_analysis_widget.project = project

        # Update Point Counting handler's project reference
        if hasattr(self, 'point_counting_page_handler') and self.point_counting_page_handler:
            self.point_counting_page_handler.project = project
            logger.info("Updated Point Counting handler's project reference.")

        # Set current_project for the analysis handlers (Image Lab page)
        if hasattr(self, 'set_current_project'):
            self.set_current_project(project)
            logger.info("Set current_project for analysis handlers using set_current_project method.")
        elif hasattr(self, 'current_project'):
            self.current_project = project
            logger.info("Set current_project for analysis handlers using direct assignment.")

        # Set project for advanced segmentation page
        if hasattr(self, 'advanced_segmentation_page'):
            if hasattr(self.advanced_segmentation_page, 'set_project'):
                self.advanced_segmentation_page.set_project(project)
                logger.info(f"Updated Advanced Segmentation page project reference")

        logger.info("Project set for all handlers and components")

    def set_project_for_trainable_segmentation(self, project):
        """Set the project specifically for trainable segmentation handlers.

        Args:
            project: The project object
        """
        try:
            # Call the TrainableSegmentationHandlers.set_project method
            # This is the method we fixed earlier
            TrainableSegmentationHandlers.set_project(self, project)
            logger.info("Set project for trainable segmentation handlers")

            # Verify that the label settings manager is properly initialized
            if hasattr(self, 'label_settings_manager'):
                if hasattr(self.label_settings_manager, 'settings_file_path'):
                    logger.info(f"Label settings file path: {self.label_settings_manager.settings_file_path}")
                else:
                    logger.warning("Label settings file path not set")
            else:
                logger.warning("Label settings manager not initialized")
        except Exception as e:
            logger.exception(f"Error setting project for trainable segmentation handlers: {e}")

    def __init__(self):
        # Initialize the UI first
        super().__init__()

        # Explicitly call __init__ for handlers that need it
        SettingsHandlers.__init__(self)
        SegmentationHandlers.__init__(self)
        AnalysisHandlers.__init__(self)
        TrainableSegmentationHandlers.__init__(self)
        AIAssistantHandlers.__init__(self)

        # Initialize theme preview method
        self.update_theme_preview = self._update_theme_preview

        # Initialize Point Counting Page Handler (after UI setup)
        # It manages its own controls and state based on the UI passed via 'self'
        try:
            # Pass 'self' which now contains 'self.ui' elements from VisionLabAiAppUI
            # Also pass the project object if available
            self.point_counting_page_handler = PointCountingPageHandler(self, self.project if hasattr(self, 'project') else None)
            logger.info("Point Counting Handler Initialized.")
        except AttributeError as e:
             logger.error(f"Error initializing PointCountingPageHandler: {e}. Check UI setup.")
             # Ensure point_counting_page_handler exists but is None if init fails
             # This prevents crashes later if initialization failed.
             self.point_counting_page_handler = None
        except Exception as e:
             logger.exception("Unexpected error initializing PointCountingPageHandler.")
             self.point_counting_page_handler = None


        # Update palette combo with custom palettes (if applicable)
        if hasattr(self, 'update_palette_combo'):
            self.update_palette_combo()

        # Initialize Core Application Attributes (Data, not UI elements)
        # --- Unsupervised Segmentation Page State (Independent) ---
        self.image = None                # Current original image for unsupervised segmentation
        self.image_resized = None        # Resized version for display in unsupervised segmentation
        self.segmented_images = []       # History of segmented images (unsupervised segmentation)
        self.segmented_image = None      # Current displayed segmented image (unsupervised segmentation)
        self.new_colors = {}             # Colors used in segmentation result (unsupervised segmentation)
        self.label_percentages = {}      # Calculated percentages for segmentation (unsupervised segmentation)
        self.image_filename = None       # Basename of the current image (unsupervised segmentation)
        self.image_full_path = None      # Full path of the current image (unsupervised segmentation)
        self.original_image_size = None  # Dimensions (h, w) of the original image (unsupervised segmentation)
        self.worker = None               # Placeholder for background workers (unsupervised segmentation)
        self.crop_rect = None            # Store crop rectangle details (unsupervised segmentation)

        # --- Shared Application State ---
        self.common_size = (750, 750)    # Default display size (shared across pages)
        self.project = None              # Holds the current project object from Project Hub
        self.current_image_info = None   # Holds ImageInfo for the current image (shared)

        # --- Initialize Project Hub Page ---
        self.project_hub_page = ProjectHubPage(self)
        self.project_hub_page.switch_page_requested.connect(self.handle_analysis_page_switch)

        # --- Initialize AI Assistant page ---
        logger.info("Initializing AI Assistant page connections")
        self.setup_ai_assistant_connections() # Connect buttons/gallery within AI Assistant

        # Setup markdown support for AI Assistant page
        logger.info("Setting up markdown support for AI Assistant page")
        self.setup_markdown_support()

        # --- Replace Placeholders with Actual Widgets ---
        self.project_hub_page_index = self._replace_placeholder_tab("Project Hub", self.project_hub_page, insert_at=0)
        print(f"Project Hub tab index: {self.project_hub_page_index}")

        # --- Initialize and Insert Grain Analysis Widget ---
        self.grain_analysis_widget = GrainAnalysisWidget(self)
        self.grain_analysis_page_index = self._replace_placeholder_tab("Grain Analysis", self.grain_analysis_widget)
        print(f"Grain Analysis tab index: {self.grain_analysis_page_index}")

        # --- Initialize and Insert Image Lab Page ---
        self.image_lab_page = ImageLabPage(self, self.project if hasattr(self, 'project') else None)
        self.image_lab_page_index = self._replace_placeholder_tab("Image Lab", self.image_lab_page)
        print(f"Image Lab tab index: {self.image_lab_page_index}")

        # --- Initialize and Insert Advanced Segmentation Page ---
        self.advanced_segmentation_page = AdvancedSegmentationPage(self, self.project if hasattr(self, 'project') else None)
        self.advanced_segmentation_page_index = self.stacked_widget.addTab(self.advanced_segmentation_page, "Advanced Segmentation")
        print(f"Advanced Segmentation tab index: {self.advanced_segmentation_page_index}")
        
        # --- Initialize and Insert Batch Processing Page ---
        self.batch_processing_page = BatchProcessingPage(self)
        self.batch_processing_page_index = self.stacked_widget.addTab(self.batch_processing_page, "Batch Processing")
        print(f"Batch Processing tab index: {self.batch_processing_page_index}")
        # ---------------------------------------

        # Reorder tabs to the final desired order
        self.reorder_tabs()

        # Debug: Check if grain_analysis_page_index is still valid after reordering
        if hasattr(self, 'grain_analysis_page_index'):
            old_index = self.grain_analysis_page_index
            new_index = self.find_tab_index_by_name("Grain Analysis")
            if old_index != new_index:
                logger.warning(f"DEBUG: grain_analysis_page_index changed after reordering: {old_index} -> {new_index}")
                self.grain_analysis_page_index = new_index
                logger.info(f"DEBUG: Updated grain_analysis_page_index to {new_index}")
            else:
                logger.info(f"DEBUG: grain_analysis_page_index remained the same after reordering: {old_index}")
        else:
            logger.error("DEBUG: grain_analysis_page_index not found!")

        # --- Connect all signals ---
        self.connect_signals()
        
        # --- Initialize About Page Handler ---
        try:
            self.about_page_handler = AboutPageHandler(self)
            logger.info("About page handler initialized successfully")
        except Exception as e:
            logger.error(f"Error initializing About page handler: {e}")

        # --- Connect application shutdown event ---
        app = QApplication.instance()
        if app:
            app.aboutToQuit.connect(self.handle_app_shutdown)

        # --- Load initial settings ---
        self.load_settings()

        # No need to initialize theme preview here as it's now handled in load_settings
        # The theme is applied silently during startup

        # Initialize active tab styling for the current page
        try:
            # Apply initial styling to all toolbar buttons
            self.apply_toolbar_styling()

            # Set active tab
            current_index = self.stacked_widget.currentIndex()
            current_tab_text = self.stacked_widget.tabText(current_index)
            self.update_active_tab_styling(current_tab_text)
            logger.info(f"Initialized active tab styling for: {current_tab_text}")
        except Exception as e:
            logger.error(f"Error initializing active tab styling: {e}")

    def _replace_placeholder_tab(self, tab_name, new_widget, insert_at=None):
        """Finds a tab by name, removes it, and inserts the new widget."""
        found_index = -1
        for i in range(self.stacked_widget.count()):
            if self.stacked_widget.tabText(i) == tab_name:
                found_index = i
                break

        if found_index != -1:
            placeholder_widget = self.stacked_widget.widget(found_index)
            self.stacked_widget.removeTab(found_index)
            if placeholder_widget:
                placeholder_widget.deleteLater() # Clean up the placeholder
            if insert_at is not None:
                self.stacked_widget.insertTab(insert_at, new_widget, tab_name)
                self.update_tab_icon(tab_name)  # Apply icon after inserting tab
                return insert_at
            else:
                self.stacked_widget.insertTab(found_index, new_widget, tab_name)
                self.update_tab_icon(tab_name)  # Apply icon after inserting tab
                return found_index
        else:
            # Fallback: Add as a new tab if placeholder wasn't found
            if insert_at is not None:
                 self.stacked_widget.insertTab(insert_at, new_widget, tab_name)
                 self.update_tab_icon(tab_name)  # Apply icon after inserting tab
                 return insert_at
            else:
                new_index = self.stacked_widget.addTab(new_widget, tab_name)
                self.update_tab_icon(tab_name)  # Apply icon after adding tab
                logger.warning(f"Placeholder tab '{tab_name}' not found. Added as new tab at index {new_index}.")
                return new_index

    def save_file_dialog(self, title, file_filter, default_dir="", default_name=""):
        """Opens a save file dialog."""
        if not default_dir and self.image_full_path:
             default_dir = os.path.dirname(self.image_full_path)
        full_default_path = os.path.join(default_dir, default_name)

        options = QFileDialog.Options()
        # options |= QFileDialog.DontUseNativeDialog # Uncomment for non-native dialog
        file_path, selected_filter = QFileDialog.getSaveFileName(
            self,
            title,
            full_default_path, # Use combined path
            file_filter,
            options=options
        )
        return file_path, selected_filter

    def open_file_dialog(self, title, file_filter, default_dir=""):
        """Opens a file dialog."""
        options = QFileDialog.Options()
        # options |= QFileDialog.DontUseNativeDialog
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            title,
            default_dir,
            file_filter,
            options=options
        )
        return file_path

    def connect_signals(self):
        """Connects UI elements to their respective handler functions."""
        logger.info("Connecting application signals...")

        # --- File Menu ---
        # open_action removed - images are now only loaded from project hub
        # Add proper checks for menu actions
        if hasattr(self, 'save_action') and self.save_action is not None:
            self.save_action.triggered.connect(self.download_image) # Or a more specific save action
        if hasattr(self, 'exit_action') and self.exit_action is not None:
            self.exit_action.triggered.connect(self.close)

        # Add Gallery action (Revised - Access menu directly)
        try:
            # Attempt to access the File menu directly by its object name
            # Common names are self.menuFile or self.ui.menuFile
            if hasattr(self, 'menuFile'):
                file_menu = self.menuFile
            elif hasattr(self, 'ui') and hasattr(self.ui, 'menuFile'): # If UI elements are under self.ui
                file_menu = self.ui.menuFile
            else:
                file_menu = None
                logger.warning("Could not find 'menuFile' attribute directly.")

            if file_menu and isinstance(file_menu, QMenu): # Check if found and is a QMenu
                logger.debug("Adding legacy gallery action to File menu.")
                self.gallery_action = file_menu.addAction("Open Image Gallery (Legacy)")
                # Make it clear this is legacy if Project Hub is primary
                # self.gallery_action.setEnabled(False) # Optional: disable if truly legacy
                self.gallery_action.triggered.connect(self.open_image_gallery)
            elif file_menu:
                logger.warning(f"Found 'menuFile' but it's not a QMenu (Type: {type(file_menu)}). Cannot add gallery action.")
            else:
                logger.warning("'menuFile' not found or is None. Cannot add gallery action.")

        except Exception as e:
            logger.exception(f"An unexpected error occurred while trying to add gallery action: {e}")

        # Connect old menu actions only if they exist (for backward compatibility)
        # File menu
        if hasattr(self, 'open_action') and self.open_action is not None:
            self.open_action.triggered.connect(self.open_image)
        if hasattr(self, 'save_action') and self.save_action is not None:
            self.save_action.triggered.connect(self.save_segmented_image)
        if hasattr(self, 'exit_action') and self.exit_action is not None:
            self.exit_action.triggered.connect(self.close)

        # Edit menu
        if hasattr(self, 'rand_colors_action') and self.rand_colors_action is not None:
            self.rand_colors_action.triggered.connect(self.change_colors)
        if hasattr(self, 'pick_colors_action') and self.pick_colors_action is not None:
            self.pick_colors_action.triggered.connect(self.pick_colors)
        if hasattr(self, 'calc_percent_action') and self.calc_percent_action is not None:
            self.calc_percent_action.triggered.connect(self.calculate_label_percentages)
        if hasattr(self, 'color_palette_combo') and self.color_palette_combo is not None:
            self.color_palette_combo.currentTextChanged.connect(self.change_colors) # For segmentation page

        # Segmentation menu
        if hasattr(self, 'start_seg_action') and self.start_seg_action is not None:
            self.start_seg_action.triggered.connect(self.start_segmentation)
        if hasattr(self, 'merge_seg_action') and self.merge_seg_action is not None:
            self.merge_seg_action.triggered.connect(self.merge_selected_segments)
        if hasattr(self, 'preprocess_action') and self.preprocess_action is not None:
            self.preprocess_action.triggered.connect(self.open_preprocessing_window)

        # Help menu
        if hasattr(self, 'about_action') and self.about_action is not None:
            self.about_action.triggered.connect(self.show_about)

        # --- Connect new toolbar actions ---
        # These connections are handled in the ToolbarUI.connect_toolbar_actions method
        self.connect_toolbar_actions()

        # --- Sidebar Navigation Buttons ---
        # Skip connecting navigation buttons since we removed the navigation bar
        # These connections are now handled by the toolbar
        logger.info("Skipping navigation bar button connections since we're using the toolbar instead.")

        # --- Other Sidebar Buttons ---
        # upload_button removed
        self.download_button.clicked.connect(self.download_image) # Consider context: which image?
        self.export_coco_button.clicked.connect(self.export_coco) # For segmentation page result

        # --- Segmentation Page Controls (Managed by SegmentationHandlers) ---
        self.epoch_slider.valueChanged.connect(self.update_segmented_image) # Example connection
        # Connect post-processing buttons if they are part of the main UI structure
        if hasattr(self, 'change_colors_button'): self.change_colors_button.clicked.connect(self.change_colors)
        if hasattr(self, 'pick_colors_button'): self.pick_colors_button.clicked.connect(self.pick_colors)
        if hasattr(self, 'merge_button'): self.merge_button.clicked.connect(self.merge_selected_segments)
        if hasattr(self, 'save_palette_button'): self.save_palette_button.clicked.connect(self.save_custom_palette)
        if hasattr(self, 'manage_palettes_button'): self.manage_palettes_button.clicked.connect(self.manage_palettes)
        if hasattr(self, 'export_annotations_button') and self.export_annotations_button: # Connect export from seg page
            try:
                # Disconnect first to prevent duplicates if run multiple times
                self.export_annotations_button.clicked.disconnect()
            except RuntimeError:
                pass # No connection to disconnect
            self.export_annotations_button.clicked.connect(self.export_segments_as_annotations)
            logger.info("Connected export_annotations_button (Segmentation Page)")
        # Connect gallery signals if available
        if hasattr(self, 'process_gallery'):
            self.process_gallery.image_clicked.connect(self.on_process_gallery_image_clicked)
            self.process_gallery.remove_clicked.connect(self.on_process_gallery_remove_clicked)
        
        # Connect clear process gallery button
        if hasattr(self, 'clear_process_gallery_button'):
            self.clear_process_gallery_button.clicked.connect(self.clear_process_gallery)

        # --- Analysis Page Controls ---
        # Note: Analysis page controls removed - Image Lab page is now independent and manages its own connections

        # Connect threshold controls
        self.threshold_slider.valueChanged.connect(self.update_threshold_label)
        self.adaptive_block_size_slider.valueChanged.connect(self.update_adaptive_block_size_label)
        self.adaptive_c_slider.valueChanged.connect(self.update_adaptive_c_label)
        self.apply_threshold_btn.clicked.connect(self.apply_threshold)

        # Connect edge detection controls
        self.canny_low_threshold_slider.valueChanged.connect(self.update_canny_low_threshold_label)
        self.canny_high_threshold_slider.valueChanged.connect(self.update_canny_high_threshold_label)
        self.apply_edge_btn.clicked.connect(self.apply_edge_detection)

        # Connect morphological operations controls
        # Note: Morphological operation connections removed - Image Lab page manages its own operations

        # Note: Analysis gallery connections removed - Image Lab page manages its own gallery independently

        # --- Trainable Segmentation Page Controls (Managed by TrainableSegmentationHandlers) ---
        # trainable_upload_button removed
        self.label_selection_combo.currentIndexChanged.connect(self.set_current_label)
        self.draw_button.clicked.connect(self.toggle_draw_mode)
        self.erase_button.clicked.connect(self.toggle_erase_mode)
        self.clear_labels_button.clicked.connect(self.clear_all_labels)
        self.train_button.clicked.connect(self.train_classifier)
        self.apply_to_new_button.clicked.connect(self.apply_to_new_image)
        self.save_classifier_button.clicked.connect(self.save_classifier)
        self.load_classifier_button.clicked.connect(self.load_classifier)
        # Connect save segmentation results button
        self.save_segmentation_results_button.clicked.connect(self.save_segmentation_results)
        # Connect annotation buttons
        self.save_all_annotations_button.clicked.connect(self.save_all_annotations)
        self.load_all_annotations_button.clicked.connect(self.load_all_annotations)
        self.load_exported_annotations_button.clicked.connect(self.load_exported_annotations)
        # Connect quick save/load buttons
        self.quick_save_annotations_button.clicked.connect(self.quick_save_annotations)
        self.quick_load_annotations_button.clicked.connect(self.quick_load_annotations)
        # Connect manage labels button
        self.manage_labels_button.clicked.connect(self.show_label_management_dialog)
        self.segment_button.clicked.connect(self.start_segmentation) # Reuse start_segmentation? Or specific trainable one?
        self.stop_button.clicked.connect(self.stop_training) # For trainable classifier
        if hasattr(self, 'reload_button'): # Reload previous results button
             self.reload_button.clicked.connect(self.reload_previous_results)
        if hasattr(self, 'trainable_gallery'):
             self.trainable_gallery.image_clicked.connect(self.on_trainable_gallery_image_clicked)
             self.trainable_gallery.remove_clicked.connect(self.on_trainable_gallery_remove_clicked)
             # Set the gallery reference in the trainable segmentation handlers
             if hasattr(self, 'trainable_segmentation_handlers'):
                 self.trainable_segmentation_handlers.trainable_gallery = self.trainable_gallery
             else:
                 # Set it directly on self since TrainableSegmentationHandlers is mixed in
                 self.trainable_gallery = self.trainable_gallery
        
        # Connect clear trainable gallery button
        if hasattr(self, 'clear_trainable_gallery_button'):
            self.clear_trainable_gallery_button.clicked.connect(self.clear_trainable_gallery)
        # SAM buttons
        self.accept_sam_button.clicked.connect(self.accept_sam_prediction)
        self.reject_sam_button.clicked.connect(self.reject_sam_prediction)
        self.sam_magic_wand_button.clicked.connect(self.toggle_sam_magic_wand)

        # Connect brush size slider for Trainable Segmentation Page
        if hasattr(self, 'trainable_brush_size_slider') and hasattr(self, 'update_brush_size'):
            print(f"DEBUG: Connecting trainable_brush_size_slider in app.py connect_signals")
            try:
                self.trainable_brush_size_slider.valueChanged.disconnect(self.update_brush_size)
            except RuntimeError:
                pass
            except TypeError:
                pass
            self.trainable_brush_size_slider.valueChanged.connect(self.update_brush_size)
            print("DEBUG: Connected trainable_brush_size_slider.valueChanged to self.update_brush_size in VisionLabAiApp.connect_signals")
        else:
            print("DEBUG: Could not connect trainable_brush_size_slider in VisionLabAiApp.connect_signals - slider or update_brush_size method missing.")

        # --- Settings Page Controls (Managed by SettingsHandlers) ---
        self.setup_handlers()  # Call the setup_handlers method from SettingsHandlers

        # --- Point Counting Page Controls ---
        # These are connected INTERNALLY by the PointCountingPageHandler's _connect_signals method.
        # No need to connect them here in app.py.

        # --- Batch Processing Page Controls ---
        # These are connected INTERNALLY by the BatchProcessingPage's _connect_signals method.
        # No need to connect them here in app.py.

        # --- Zoom/Scroll Synchronization ---
        # Unsupervised segmentation page
        self.original_image_view.zoom_changed.connect(self.sync_zoom)
        self.segmented_image_view.zoom_changed.connect(self.sync_zoom)
        self.original_image_view.scroll_changed.connect(self.sync_scroll)
        self.segmented_image_view.scroll_changed.connect(self.sync_scroll)

        # Image Lab page (zoom/scroll sync is handled internally by the page)

        # Trainable segmentation page
        self.trainable_original_view.zoom_changed.connect(self.sync_zoom_trainable)
        self.trainable_result_view.zoom_changed.connect(self.sync_zoom_trainable)
        self.trainable_original_view.scroll_changed.connect(self.sync_scroll_trainable)
        self.trainable_result_view.scroll_changed.connect(self.sync_scroll_trainable)
        
        # Trainable segmentation side-by-side views synchronization
        self.trainable_original_sync_view.zoom_changed.connect(self.sync_zoom_trainable_side_by_side)
        self.trainable_result_sync_view.zoom_changed.connect(self.sync_zoom_trainable_side_by_side)
        self.trainable_original_sync_view.scroll_changed.connect(self.sync_scroll_trainable_side_by_side)
        self.trainable_result_sync_view.scroll_changed.connect(self.sync_scroll_trainable_side_by_side)
        # Add zoom/scroll sync for Point Counting if needed (connect QPixmapView signals)
        # self.point_counting_image_view.zoom_changed.connect(...)
        # self.point_counting_image_view.scroll_changed.connect(...)

        logger.info("Application signals connected.")

    def find_tab_index_by_name(self, tab_name):
        """Find the index of a tab by its name."""
        for i in range(self.stacked_widget.count()):
            if self.stacked_widget.tabText(i) == tab_name:
                return i
        logger.warning(f"Tab '{tab_name}' not found.")
        return -1

    def apply_toolbar_styling(self):
        """Apply custom styling to all toolbar buttons."""
        try:
            # Force update of toolbar styling
            if hasattr(self, 'main_toolbar'):
                self.main_toolbar.setStyleSheet("")  # Clear stylesheet

                # Try to load custom stylesheet
                try:
                    with open("src/gui/styles/custom_toolbar_style.css", "r") as f:
                        self.main_toolbar.setStyleSheet(f.read())
                except Exception as e:
                    logger.error(f"Error loading custom toolbar style: {e}")

                # Force update
                self.main_toolbar.update()
                logger.debug("Applied custom toolbar styling")
        except Exception as e:
            logger.exception(f"Error applying toolbar styling: {e}")

    def update_active_tab_styling(self, active_tab_name):
        """Updates the styling of toolbar buttons to indicate the active tab.

        Args:
            active_tab_name (str): The name of the active tab
        """
        try:
            # Map tab names to their corresponding toolbar actions
            tab_action_map = {
                "Project Hub": self.project_hub_action,
                "Unsupervised Segmentation": self.unsupervised_segmentation_action,
                "Trainable Segmentation": self.trainable_segmentation_action,
                "Point Counting": self.point_counting_action,
                "Grain Analysis": self.grain_analysis_action,
                "Advanced Segmentation": getattr(self, 'advanced_segmentation_action', None),
                "Image Lab": self.image_lab_action,
                "Settings": self.settings_action,
                "AI Assistant": self.ai_assistant_action
            }

            # Clear active state from all toolbar buttons
            for action_name, action in tab_action_map.items():
                if action and hasattr(action, 'parentWidget') and action.parentWidget():
                    button = action.parentWidget()
                    button.setProperty("active", "false")
                    button.style().unpolish(button)
                    button.style().polish(button)
                    logger.debug(f"Cleared active styling for tab: {action_name}")

            # Set active state for the current tab's button
            if active_tab_name in tab_action_map and tab_action_map[active_tab_name]:
                action = tab_action_map[active_tab_name]
                if hasattr(action, 'parentWidget') and action.parentWidget():
                    button = action.parentWidget()
                    button.setProperty("active", "true")

                    # Force style update
                    button.style().unpolish(button)
                    button.style().polish(button)
                    button.update()

                    # Also update the toolbar to ensure the style is applied
                    if hasattr(self, 'main_toolbar'):
                        self.main_toolbar.update()

                    logger.debug(f"Set active styling for tab: {active_tab_name}")
                else:
                    logger.warning(f"Could not find parent widget for action: {active_tab_name}")
            else:
                logger.warning(f"Tab name not found in action map: {active_tab_name}")
        except Exception as e:
            logger.exception(f"Error updating active tab styling: {e}")




    def switch_page(self, index):
        """Switches to the specified page index and handles state saving/loading."""
        if index < 0 or index >= self.stacked_widget.count():
            logger.warning(f"Invalid page index requested: {index}")
            return

        current_index = self.stacked_widget.currentIndex()
        if current_index == index:
            return # Already on the requested page

        current_tab_text = self.stacked_widget.tabText(current_index)
        new_tab_text = self.stacked_widget.tabText(index)
        logger.info(f"Switching from page '{current_tab_text}' (idx {current_index}) to '{new_tab_text}' (idx {index})")

        # Update active tab styling in toolbar
        self.update_active_tab_styling(new_tab_text)

        # --- State Saving Logic (Before Switch) ---
        try:
            if current_tab_text == "Trainable Segmentation":
                 # Save current image annotations to multi_image_handler before switching
                 if hasattr(self, 'multi_image_handler') and hasattr(self, 'training_labels') and self.training_labels is not None:
                     current_path = self.multi_image_handler.current_image_path
                     if current_path:
                         logger.debug(f"Saving annotations for current image before page switch: {current_path}")

                         # Create a fresh copy to avoid reference issues
                         annotations_copy = self.training_labels.copy()

                         # Ensure the annotations are a numpy array of type uint8
                         if not isinstance(annotations_copy, np.ndarray):
                             logger.debug(f"Converting annotations to numpy array")
                             annotations_copy = np.array(annotations_copy, dtype=np.uint8)
                         elif annotations_copy.dtype != np.uint8:
                             logger.debug(f"Converting annotations dtype from {annotations_copy.dtype} to uint8")
                             annotations_copy = annotations_copy.astype(np.uint8)

                         # Save to multi_image_handler
                         self.multi_image_handler.set_annotations(current_path, annotations_copy)
                         logger.debug(f"Successfully saved annotations before switching away")

                         # Also save segmentation results if available
                         if hasattr(self, 'result') and self.result is not None:
                             logger.debug(f"Saving segmentation result for current image before page switch: {current_path}")
                             self.multi_image_handler.set_segmentation_result(current_path, self.result.copy())

                         # Also save label names and colors to make them persistent
                         if hasattr(self, 'label_names') and self.label_names:
                             logger.debug(f"Saving label_names before switching away")
                             if not hasattr(self.multi_image_handler, 'label_names'):
                                 self.multi_image_handler.label_names = {}
                             self.multi_image_handler.label_names = self.label_names.copy()

                         # Save mask colors
                         if hasattr(self, 'mask_colors') and self.mask_colors is not None:
                             logger.debug(f"Saving mask_colors before switching away")
                             self.multi_image_handler.mask_colors = self.mask_colors.copy()

                 # Save state using the handler's method if possible
                 if hasattr(self, 'trainable_segmentation_page') and hasattr(self.trainable_segmentation_page, 'handlers'):
                     handlers = self.trainable_segmentation_page.handlers
                     if hasattr(handlers, 'save_trainable_segmentation_state'):
                         logger.debug("Saving Trainable Segmentation state via page handlers.")
                         handlers.save_trainable_segmentation_state()
                 # Fallback to app method if page handler not found
                 elif hasattr(self, 'save_trainable_segmentation_state'):
                     logger.debug("Saving Trainable Segmentation state via app method.")
                     self.save_trainable_segmentation_state()

            elif current_tab_text == "Point Counting":
                 if self.point_counting_page_handler:
                     logger.debug("Saving Point Counting state.")
                     self.point_counting_page_handler.save_current_state_to_project()

            # Add saving logic for other pages if needed (e.g., Analysis state)
            elif current_tab_text == "Image Lab":
                if hasattr(self, 'image_lab_page') and hasattr(self.image_lab_page, 'save_state'):
                    logger.debug("Saving Image Lab state.")
                    self.image_lab_page.save_state()

            elif current_tab_text == "Advanced Segmentation":
                if hasattr(self, 'advanced_segmentation_page') and hasattr(self.advanced_segmentation_page, 'save_state'):
                    logger.debug("Saving Advanced Segmentation state.")
                    self.advanced_segmentation_page.save_state()

            elif current_tab_text == "Unsupervised Segmentation": # Unsupervised Segmentation
                if hasattr(self, 'save_segmentation_state'):
                    logger.debug("Saving Segmentation state.")
                    self.save_segmentation_state()

            elif current_tab_text == "Grain Analysis":
                if hasattr(self, 'grain_analysis_widget'):
                    logger.debug("Saving Grain Analysis state.")
                    # Save the current state before switching
                    if hasattr(self.grain_analysis_widget, 'save_grain_analysis_state'):
                        self.grain_analysis_widget.save_grain_analysis_state()
                    # Save any pending changes
                    if hasattr(self.grain_analysis_widget, 'save_all_pending_changes'):
                        self.grain_analysis_widget.save_all_pending_changes()

        except Exception as e:
            logger.exception(f"Error saving state for page '{current_tab_text}' before switching: {e}")

        # --- Perform the Switch ---
        logger.info(f"DEBUG: About to switch to page index {index} with name '{new_tab_text}'")
        super().switch_page(index) # Call the UI method to switch tab and update buttons
        logger.info(f"DEBUG: After switch_page call, current index is {self.stacked_widget.currentIndex()} with name '{self.stacked_widget.tabText(self.stacked_widget.currentIndex())}'")

        # Verify the switch was successful
        if self.stacked_widget.currentIndex() != index:
            logger.error(f"ERROR: Failed to switch to page index {index}. Current index is {self.stacked_widget.currentIndex()}")
            # Try again with direct method
            self.stacked_widget.setCurrentIndex(index)
            logger.info(f"DEBUG: After direct setCurrentIndex call, current index is {self.stacked_widget.currentIndex()}")

        # --- State Loading/Update Logic (After Switch) ---
        try:
            if new_tab_text == "Trainable Segmentation":
                logger.debug("Switched to Trainable Segmentation page.")

                # Ensure the project is properly set for trainable segmentation
                if hasattr(self, 'project') and self.project:
                    self.set_project_for_trainable_segmentation(self.project)
                    logger.info(f"Reset project for trainable segmentation when switching to the page: {self.project.name}")

                # Initialize annotation path manager if needed
                if not hasattr(self, 'annotation_path_manager'):
                    from src.gui.handlers.annotation_path_manager import AnnotationPathManager
                    self.annotation_path_manager = AnnotationPathManager()
                    logger.debug("Created new annotation_path_manager")
                    if hasattr(self, 'project') and self.project:
                        project_file = self.project.project_file
                        project_dir = os.path.dirname(project_file)
                        project_name = self.project.name
                        mapping_file = os.path.join(project_dir, f"{project_name}_data", "state", "trainable_segmentation", "annotation_paths.json")
                        if os.path.exists(mapping_file):
                            self.annotation_path_manager.load_from_file(mapping_file)
                            logger.debug(f"Loaded annotation path mapping from {mapping_file}")

                # Initialize label settings manager if needed
                if not hasattr(self, 'label_settings_manager'):
                    from src.gui.handlers.label_settings_manager import LabelSettingsManager
                    self.label_settings_manager = LabelSettingsManager()
                    logger.debug("Created new label_settings_manager")
                    if hasattr(self, 'project') and self.project:
                        self.label_settings_manager.set_project(self.project)
                        logger.debug(f"Set project for label_settings_manager")

                # The automatic reloading of original NPZ annotations via annotation_path_manager
                # is removed here. Annotations are now consistently loaded from multi_image_handler
                # by the TrainableSegmentationHandlers.load_image/load_image_at_index methods,
                # or initialized if not present. This prevents overwriting user modifications.
                logger.info("Trainable Segmentation: Automatic reload from original NPZ is disabled. Annotations will be sourced from memory or initialized.")

                # Ensure the current image's annotations and UI are up-to-date.
                # This is typically handled by load_image_at_index or on_trainable_gallery_image_clicked
                # when an image becomes active within the TrainableSegmentationHandlers.
                # Here, we just ensure the UI reflects the current state if an image is already loaded.
                if hasattr(self, 'multi_image_handler') and self.multi_image_handler.current_image_path:
                    current_image_file_path = self.multi_image_handler.current_image_path
                    logger.debug(f"Trainable Segmentation page: current image is {current_image_file_path}. Ensuring its state is loaded from multi_image_handler.")

                    if hasattr(self, 'trainable_image') and self.trainable_image is not None:
                        # Get annotations from multi_image_handler
                        current_annotations = self.multi_image_handler.get_annotations(current_image_file_path)
                        if current_annotations is not None:
                            # Always use the annotations from multi_image_handler as the source of truth
                            logger.debug(f"Loading annotations from multi_image_handler for {current_image_file_path}")

                            # Create a fresh copy to avoid reference issues
                            self.training_labels = current_annotations.copy()

                            # Ensure the annotations are a numpy array of type uint8
                            if not isinstance(self.training_labels, np.ndarray):
                                logger.debug(f"Converting training_labels to numpy array")
                                self.training_labels = np.array(self.training_labels, dtype=np.uint8)
                            elif self.training_labels.dtype != np.uint8:
                                logger.debug(f"Converting training_labels dtype from {self.training_labels.dtype} to uint8")
                                self.training_labels = self.training_labels.astype(np.uint8)

                            # Verify dimensions match the image
                            if self.training_labels.shape != self.trainable_image.shape[:2]:
                                logger.debug(f"training_labels shape {self.training_labels.shape} doesn't match image shape {self.trainable_image.shape[:2]}")
                                try:
                                    # Try to resize training_labels to match image dimensions
                                    import cv2
                                    resized_labels = cv2.resize(
                                        self.training_labels,
                                        (self.trainable_image.shape[1], self.trainable_image.shape[0]),
                                        interpolation=cv2.INTER_NEAREST
                                    )
                                    self.training_labels = resized_labels
                                    logger.debug(f"Resized training_labels to {self.training_labels.shape}")
                                    # Save the resized annotations back to the handler
                                    self.multi_image_handler.set_annotations(current_image_file_path, self.training_labels.copy())
                                except Exception as e:
                                    logger.exception(f"Error resizing training_labels: {e}")
                                    # Create new empty labels with correct dimensions
                                    self.training_labels = np.zeros(self.trainable_image.shape[:2], dtype=np.uint8)
                                    # Save these empty annotations to the handler
                                    self.multi_image_handler.set_annotations(current_image_file_path, self.training_labels.copy())

                            logger.debug(f"Successfully loaded annotations from multi_image_handler for {current_image_file_path}")
                        else:
                            logger.warning(f"Annotations for {current_image_file_path} not found in multi_image_handler. Initializing.")
                            self.training_labels = np.zeros(self.trainable_image.shape[:2], dtype=np.uint8)
                            self.multi_image_handler.set_annotations(current_image_file_path, self.training_labels.copy())

                        # Also reload segmentation result
                        self.result = self.multi_image_handler.get_segmentation_result(current_image_file_path)
                        if self.result is not None:
                             logger.debug(f"Reloaded segmentation result for {current_image_file_path}")

                        # Also reload label names and colors
                        if hasattr(self.multi_image_handler, 'label_names') and self.multi_image_handler.label_names:
                            logger.debug(f"Reloaded label_names from multi_image_handler")
                            self.label_names = self.multi_image_handler.label_names.copy()

                        # Reload mask colors
                        if hasattr(self.multi_image_handler, 'mask_colors') and self.multi_image_handler.mask_colors is not None:
                            logger.debug(f"Reloaded mask_colors from multi_image_handler")
                            self.mask_colors = self.multi_image_handler.mask_colors.copy()
                    else:
                        # No image currently displayed in trainable_original_view, clear labels
                        self.training_labels = None
                        self.result = None
                        logger.debug("No current image in trainable_original_view; training_labels and result cleared.")


                # Refresh label/color UI from handler's current state
                if hasattr(self, 'trainable_segmentation_page') and hasattr(self.trainable_segmentation_page, 'handlers'):
                    handlers = self.trainable_segmentation_page.handlers
                    if hasattr(handlers, 'update_label_color_indicator'):
                        handlers.update_label_color_indicator()
                        logger.debug("Updated label selection combo box on page switch.")
                    if hasattr(handlers, 'display_trainable_image'):
                        handlers.display_trainable_image() # This will use the potentially updated self.training_labels
                        logger.debug("Refreshed trainable image display on page switch.")

                # Synchronize gallery if it exists
                if hasattr(self, 'trainable_gallery') and hasattr(self, 'sync_gallery_selection_with_current_image'):
                     self.sync_gallery_selection_with_current_image()
                     logger.debug("Synchronized trainable gallery selection.")


            elif new_tab_text == "Project Hub":
                logger.debug("Switched to Project Hub page.")
                # Refresh the project view when switching back to it
                if hasattr(self.project_hub_page, 'refresh_project_view'):
                    self.project_hub_page.refresh_project_view()
                elif hasattr(self.project_hub_page, 'update_project_list'): # Fallback
                    self.project_hub_page.update_project_list()

            elif new_tab_text == "Point Counting":
                # State is loaded when images are sent from Project Hub.
                # If switching back, ensure the UI reflects the loaded state.
                logger.debug("Switched to Point Counting page.")
                if self.point_counting_page_handler:
                    # Force UI update from current state in case it wasn't triggered otherwise
                    self.point_counting_page_handler._update_ui_for_current_state()

            elif new_tab_text == "AI Assistant":
                # Check if AI Assistant components need to be reinitialized
                logger.debug("Switched to AI Assistant page.")
                if not hasattr(self, 'ai_assistant_gemini_worker') or self.ai_assistant_gemini_worker is None:
                    logger.info("AI Assistant worker is None, reinitializing...")
                    try:
                        from src.gui.dialogs.gemini_settings_dialog import GeminiSettingsDialog
                        from src.ai_assistant_components.src.gemini.gemini_worker import GeminiWorker

                        # Get API key and model name from settings
                        api_key = GeminiSettingsDialog.get_api_key()
                        model_name = GeminiSettingsDialog.get_model_name()

                        # Create a new worker
                        if hasattr(self, 'ai_assistant_api_key'):
                            self.ai_assistant_api_key = api_key

                        self.ai_assistant_gemini_worker = GeminiWorker(api_key, model_name=model_name)

                        # Connect the signals
                        self.ai_assistant_gemini_worker.result_ready.connect(self.handle_ai_assistant_text_result)
                        self.ai_assistant_gemini_worker.structured_data_ready.connect(self.handle_ai_assistant_structured_data)
                        self.ai_assistant_gemini_worker.bounding_boxes_ready.connect(self.handle_ai_assistant_bounding_boxes)
                        self.ai_assistant_gemini_worker.error_occurred.connect(self.handle_ai_assistant_analysis_error)
                        self.ai_assistant_gemini_worker.status_update.connect(self.update_ai_assistant_status)
                        self.ai_assistant_gemini_worker.finished.connect(self.on_ai_assistant_analysis_finished)

                        logger.info("Reinitialized AI Assistant components when switching to AI Assistant page")
                    except Exception as e:
                        logger.exception(f"Error reinitializing AI Assistant components: {e}")

            elif new_tab_text == "Image Lab":
                logger.debug("Switched to Image Lab page.")
                if hasattr(self, 'image_lab_page') and hasattr(self.image_lab_page, 'load_state'):
                    self.image_lab_page.load_state()

            elif new_tab_text == "Grain Analysis":
                logger.debug("Switched to Grain Analysis page.")
                # Set the project for the grain analysis widget
                if hasattr(self, 'grain_analysis_widget') and hasattr(self, 'project') and self.project:
                    logger.info(f"Setting project for grain analysis widget: {self.project.name}")
                    self.grain_analysis_widget.set_project(self.project)

                    # If there's a current image loaded, make sure it's loaded in the grain analysis widget
                    if hasattr(self, 'current_image_path') and self.current_image_path:
                        logger.info(f"Loading current image in grain analysis widget: {self.current_image_path}")
                        self.grain_analysis_widget.load_image(self.current_image_path)

            # Add loading/update logic for other pages if needed

        except Exception as e:
            logger.exception(f"Error loading state/updating UI for page '{new_tab_text}' after switching: {e}")

        # Update the window title
        self.setWindowTitle(f"VisionLab Ai V4 - {new_tab_text}")

    def setup_ai_assistant_connections(self):
        """Sets up connections specific to the AI Assistant page UI elements."""
        logger.info("Setting up AI Assistant connections")
        try:
            # Connect gallery
            if hasattr(self, 'ai_assistant_gallery'):
                self.ai_assistant_gallery.image_clicked.connect(self.on_ai_assistant_image_clicked)
                self.ai_assistant_gallery.remove_clicked.connect(self.on_ai_assistant_gallery_remove_clicked)
                logger.info("Connected ai_assistant_gallery.image_clicked and remove_clicked")
            else:
                logger.warning("ai_assistant_gallery not found for connection.")

            # Connect buttons
            if hasattr(self, 'ai_assistant_analyze_button'):
                self.ai_assistant_analyze_button.clicked.connect(self.start_ai_assistant_analysis)
                logger.info("Connected ai_assistant_analyze_button")
            else:
                logger.warning("ai_assistant_analyze_button not found.")

            if hasattr(self, 'ai_assistant_settings_button'):
                self.ai_assistant_settings_button.clicked.connect(self.show_gemini_settings_dialog)
                logger.info("Connected ai_assistant_settings_button")
            else:
                logger.warning("ai_assistant_settings_button not found.")

            if hasattr(self, 'ai_assistant_cancel_button'):
                self.ai_assistant_cancel_button.clicked.connect(self.cancel_ai_assistant_analysis)
                logger.info("Connected ai_assistant_cancel_button")
            else:
                logger.warning("ai_assistant_cancel_button not found.")

            # Connect clear gallery button
            if hasattr(self, 'clear_ai_assistant_gallery_button'):
                self.clear_ai_assistant_gallery_button.clicked.connect(self.clear_ai_assistant_gallery)
                logger.info("Connected clear_ai_assistant_gallery_button")
            else:
                logger.warning("clear_ai_assistant_gallery_button not found.")
        except Exception as e:
            logger.exception(f"Error setting up AI Assistant connections: {e}")

    # --- AI Assistant Action Methods (delegating to handler) ---
    def start_ai_assistant_analysis(self):
        """Start the AI Assistant analysis process."""
        logger.info("GUI: start_ai_assistant_analysis called")
        if hasattr(self, 'ai_assistant_gemini_worker'): # Check if handler initialized worker
            AIAssistantHandlers.start_ai_assistant_analysis(self)
        else:
            logger.error("AI Assistant worker not found. Cannot start analysis.")
            QMessageBox.critical(self, "Error", "AI Assistant not properly initialized.")

    def show_gemini_settings_dialog(self):
        """Show the Gemini settings dialog."""
        logger.info("GUI: show_gemini_settings_dialog called")
        # This method likely remains within the App class as it involves dialogs
        try:
            from src.gui.dialogs.gemini_settings_dialog import GeminiSettingsDialog
            dialog = GeminiSettingsDialog(self) # Pass self to access settings
            result = dialog.exec()
            logger.info(f"Gemini Settings dialog result: {result}")
            if result: # If OK was clicked
                # Reload API key and update worker without showing message boxes
                # Use the static method directly to avoid message boxes
                if hasattr(self, 'settings'):
                    # Get API key from settings using the GeminiSettingsDialog method
                    self.ai_assistant_api_key = GeminiSettingsDialog.get_api_key()
                    logger.info(f"Updated API key from settings")

                    # Get model name from settings using the GeminiSettingsDialog method
                    model_name = GeminiSettingsDialog.get_model_name()
                    logger.info(f"Updated model name from settings: {model_name}")

                    # Update worker if it exists
                    if hasattr(self, 'ai_assistant_gemini_worker') and self.ai_assistant_gemini_worker:
                        from src.ai_assistant_components.src.gemini.gemini_worker import GeminiWorker
                        self.ai_assistant_gemini_worker = GeminiWorker(self.ai_assistant_api_key, model_name=model_name)

                        # Connect the signals
                        self.ai_assistant_gemini_worker.result_ready.connect(self.handle_ai_assistant_text_result)
                        self.ai_assistant_gemini_worker.structured_data_ready.connect(self.handle_ai_assistant_structured_data)
                        self.ai_assistant_gemini_worker.bounding_boxes_ready.connect(self.handle_ai_assistant_bounding_boxes)
                        self.ai_assistant_gemini_worker.error_occurred.connect(self.handle_ai_assistant_analysis_error)
                        self.ai_assistant_gemini_worker.status_update.connect(self.update_ai_assistant_status)
                        self.ai_assistant_gemini_worker.finished.connect(self.on_ai_assistant_analysis_finished)

                    # Update status in the UI
                    if hasattr(self, 'update_ai_assistant_status'):
                        self.update_ai_assistant_status(f"API key updated successfully. Using model: {model_name}")
        except ImportError:
            logger.error("GeminiSettingsDialog not found.")
            QMessageBox.critical(self, "Error", "Settings dialog component is missing.")
        except Exception as e:
            logger.exception(f"Error showing Gemini settings dialog: {e}")
            QMessageBox.critical(self, "Error", f"An error occurred: {e}")

    def cancel_ai_assistant_analysis(self):
        """Cancel the AI Assistant analysis process."""
        logger.info("GUI: cancel_ai_assistant_analysis called")
        if hasattr(self, 'ai_assistant_gemini_worker'):
            AIAssistantHandlers.cancel_ai_assistant_analysis(self)
        else:
            logger.error("AI Assistant worker not found. Cannot cancel analysis.")




    def keyPressEvent(self, event):
        """Handles key press events, primarily for SAM interaction for now."""
        # Let specific pages handle keys first if they need to (e.g., Point Counting uses QShortcut)
        # super().keyPressEvent(event) # Pass to parent if needed

        if event.key() == Qt.Key_Return or event.key() == Qt.Key_Enter:
             # Check if in SAM drawing mode (Trainable Segmentation)
             if hasattr(self, 'sam_magic_wand_active') and self.sam_magic_wand_active and \
                hasattr(self, 'drawing_sam_bbox') and self.drawing_sam_bbox:
                 logger.debug("Enter pressed during SAM bbox drawing.")
                 self.drawing_sam_bbox = False
                 if hasattr(self, 'sam_bbox') and self.sam_bbox and len(self.sam_bbox) == 4 :
                     # Finalize bbox coordinates might happen inside predict_sam_mask
                     self.predict_sam_mask()
                 event.accept()
                 return
             # Check if there's a temporary SAM prediction to accept
             elif hasattr(self, 'temp_sam_prediction') and self.temp_sam_prediction is not None:
                 logger.debug("Enter pressed with active SAM prediction.")
                 self.accept_sam_prediction()
                 event.accept()
                 return

        # Allow other key events (like Point Counting shortcuts) to be handled
        # If not handled by specific logic above, don't accept the event here broadly
        # event.ignore() # Let other handlers/widgets try to process it

    # --- Zoom/Scroll Synchronization Methods ---
    def sync_zoom(self, scale):
        """Sync zoom for Segmentation page views."""
        sender = self.sender()
        if sender == self.original_image_view:
            target = self.segmented_image_view
        elif sender == self.segmented_image_view:
            target = self.original_image_view
        else: return # Should not happen

        # Only update if the zoom scale is different
        if target.zoom_scale != scale:
            # Calculate the center point of the sender's viewport in image coordinates
            sender_viewport = sender.viewport().rect()
            sender_center_x = sender_viewport.center().x()
            sender_center_y = sender_viewport.center().y()
            
            # Convert viewport center to image coordinates
            sender_h_scroll = sender.horizontalScrollBar().value()
            sender_v_scroll = sender.verticalScrollBar().value()
            
            # Calculate the center point in image coordinates (accounting for current zoom)
            image_center_x = (sender_center_x + sender_h_scroll) / sender.zoom_scale
            image_center_y = (sender_center_y + sender_v_scroll) / sender.zoom_scale
            
            # Update target zoom scale
            target.setZoomScale(scale)
            
            # Calculate new scroll position to maintain the same center point
            target_viewport = target.viewport().rect()
            target_center_x = target_viewport.center().x()
            target_center_y = target_viewport.center().y()
            
            # Calculate scroll position to center on the same image point
            new_h_value = max(0, int(image_center_x * scale - target_center_x))
            new_v_value = max(0, int(image_center_y * scale - target_center_y))
            
            # Update scroll position
            target.setScrollPosition(new_h_value, new_v_value)



    def sync_zoom_trainable(self, scale):
        """Sync zoom for Trainable Segmentation page views."""
        sender = self.sender()
        if sender == self.trainable_original_view:
            target = self.trainable_result_view
        elif sender == self.trainable_result_view:
            target = self.trainable_original_view
        else: return

        # Only update if the zoom scale is different
        if target.zoom_scale != scale:
            # Calculate the center point of the sender's viewport in image coordinates
            sender_viewport = sender.viewport().rect()
            sender_center_x = sender_viewport.center().x()
            sender_center_y = sender_viewport.center().y()
            
            # Convert viewport center to image coordinates
            sender_h_scroll = sender.horizontalScrollBar().value()
            sender_v_scroll = sender.verticalScrollBar().value()
            
            # Calculate the center point in image coordinates (accounting for current zoom)
            image_center_x = (sender_center_x + sender_h_scroll) / sender.zoom_scale
            image_center_y = (sender_center_y + sender_v_scroll) / sender.zoom_scale
            
            # Update target zoom scale
            target.setZoomScale(scale)
            
            # Calculate new scroll position to maintain the same center point
            target_viewport = target.viewport().rect()
            target_center_x = target_viewport.center().x()
            target_center_y = target_viewport.center().y()
            
            # Calculate scroll position to center on the same image point
            new_h_value = max(0, int(image_center_x * scale - target_center_x))
            new_v_value = max(0, int(image_center_y * scale - target_center_y))
            
            # Update scroll position
            target.setScrollPosition(new_h_value, new_v_value)

    def sync_scroll_trainable(self, h_value, v_value):
        """Sync scroll for Trainable Segmentation page views."""
        sender = self.sender()
        if sender == self.trainable_original_view:
            target = self.trainable_result_view
        elif sender == self.trainable_result_view:
            target = self.trainable_original_view
        else: return

        # Update scroll position directly
        target.setScrollPosition(h_value, v_value)

    def sync_zoom_trainable_side_by_side(self, scale):
        """Sync zoom for Trainable Segmentation side-by-side views."""
        sender = self.sender()
        if sender == self.trainable_original_sync_view:
            target = self.trainable_result_sync_view
        elif sender == self.trainable_result_sync_view:
            target = self.trainable_original_sync_view
        else: return

        # Only update if the zoom scale is different
        if target.zoom_scale != scale:
            # Calculate the center point of the sender's viewport in image coordinates
            sender_viewport = sender.viewport().rect()
            sender_center_x = sender_viewport.center().x()
            sender_center_y = sender_viewport.center().y()
            
            # Convert viewport center to image coordinates
            sender_h_scroll = sender.horizontalScrollBar().value()
            sender_v_scroll = sender.verticalScrollBar().value()
            
            # Calculate the center point in image coordinates (accounting for current zoom)
            image_center_x = (sender_center_x + sender_h_scroll) / sender.zoom_scale
            image_center_y = (sender_center_y + sender_v_scroll) / sender.zoom_scale
            
            # Update target zoom scale
            target.setZoomScale(scale)
            
            # Calculate new scroll position to maintain the same center point
            target_viewport = target.viewport().rect()
            target_center_x = target_viewport.center().x()
            target_center_y = target_viewport.center().y()
            
            # Calculate scroll position to center on the same image point
            new_h_value = max(0, int(image_center_x * scale - target_center_x))
            new_v_value = max(0, int(image_center_y * scale - target_center_y))
            
            # Update scroll position
            target.setScrollPosition(new_h_value, new_v_value)

    def sync_scroll_trainable_side_by_side(self, h_value, v_value):
        """Sync scroll for Trainable Segmentation side-by-side views."""
        sender = self.sender()
        if sender == self.trainable_original_sync_view:
            target = self.trainable_result_sync_view
        elif sender == self.trainable_result_sync_view:
            target = self.trainable_original_sync_view
        else: return

        # Update scroll position directly
        target.setScrollPosition(h_value, v_value)

    def sync_scroll(self, h_value, v_value):
        """Sync scroll for Segmentation page views."""
        sender = self.sender()
        if sender == self.original_image_view:
            target = self.segmented_image_view
        elif sender == self.segmented_image_view:
            target = self.original_image_view
        else: return

        # Update scroll position directly
        target.setScrollPosition(h_value, v_value)



    # --- Other Utility and Action Methods ---

    def show_about(self):
        """Displays the About dialog."""
        QMessageBox.information(
            self,
            "About VisionLab Ai",
            "VisionLab Ai v4.0\nImage Segmentation and Analysis Tool\n\n"
            "Developed by Fares Azzam\nContact: <EMAIL>\n\n"
            "Citation:\nAzzam, F., Blaise, T., & Brigaud, B. (2024).\n"
            "Automated petrographic image analysis by supervised\n"
            "and unsupervised machine learning methods.\nSedimentologika, 2(2).\n"
            "https://doi.org/10.57035/journals/sdk.2024.e22.1594"
        )

    def create_new_project(self):
        """Creates a new project."""
        # Check if a project is already open
        if hasattr(self, 'current_project') and self.current_project:
            reply = QMessageBox.question(self, "Create New Project",
                                       "Creating a new project will close the current project. All unsaved changes will be lost. Continue?",
                                       QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
            if reply == QMessageBox.No:
                return

            # Reset application state before creating a new project
            self.reset_application_state()

        # Delegate to project hub page
        if hasattr(self, 'project_hub_page'):
            # Switch to the project hub page first
            project_hub_page_index = self.find_tab_index_by_name("Project Hub")
            if project_hub_page_index != -1:
                self.switch_page(project_hub_page_index)
            # Call the create_new_project method on the project hub page
            self.project_hub_page.create_new_project()
        else:
            logger.error("Project hub page not found")

    def open_project(self):
        """Opens an existing project."""
        # Check if a project is already open
        if hasattr(self, 'current_project') and self.current_project:
            reply = QMessageBox.question(self, "Open Project",
                                       "Opening a project will close the current project. All unsaved changes will be lost. Continue?",
                                       QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
            if reply == QMessageBox.No:
                return

            # Reset application state before opening a new project
            self.reset_application_state()

        # Delegate to project hub page
        if hasattr(self, 'project_hub_page'):
            # Switch to the project hub page first
            project_hub_page_index = self.find_tab_index_by_name("Project Hub")
            if project_hub_page_index != -1:
                self.switch_page(project_hub_page_index)
            # Call the open_project method on the project hub page
            self.project_hub_page.open_project()
        else:
            logger.error("Project hub page not found")

    def import_images(self):
        """Imports images into the current project."""
        # Delegate to project hub page
        if hasattr(self, 'project_hub_page'):
            # Switch to the project hub page first
            project_hub_page_index = self.find_tab_index_by_name("Project Hub")
            if project_hub_page_index != -1:
                self.switch_page(project_hub_page_index)
            # Call the import_images method on the project hub page
            self.project_hub_page.import_images()
        else:
            logger.error("Project hub page not found")

    def save_project(self):
        """Saves the current project."""
        # Delegate to project hub page
        if hasattr(self, 'project_hub_page'):
            # Call the save_project method on the project hub page
            self.project_hub_page.save_project()
        else:
            logger.error("Project hub page not found")

    def save_project_as(self):
        """Saves the current project with a new name."""
        # Delegate to project hub page
        if hasattr(self, 'project_hub_page'):
            # Switch to the project hub page first
            project_hub_page_index = self.find_tab_index_by_name("Project Hub")
            if project_hub_page_index != -1:
                self.switch_page(project_hub_page_index)
            # Call the save_project_as method on the project hub page
            if hasattr(self.project_hub_page, 'save_project_as'):
                self.project_hub_page.save_project_as()
            else:
                # Fallback to regular save if save_as is not available
                self.project_hub_page.save_project()
        else:
            logger.error("Project hub page not found")

    def export_results(self):
        """Exports analysis results."""
        # Determine which page is active and delegate to the appropriate handler
        current_index = self.stacked_widget.currentIndex()
        current_tab_text = self.stacked_widget.tabText(current_index)

        if current_tab_text == "Unsupervised Segmentation":
            if hasattr(self, 'save_segmentation_results'):
                self.save_segmentation_results()
        elif current_tab_text == "Trainable Segmentation":
            if hasattr(self, 'save_trainable_results'):
                self.save_trainable_results()
        elif current_tab_text == "Grain Analysis":
            if hasattr(self, 'grain_analysis_widget') and hasattr(self.grain_analysis_widget, 'export_results'):
                self.grain_analysis_widget.export_results()
        elif current_tab_text == "Point Counting":
            if hasattr(self, 'point_counting_page_handler') and hasattr(self.point_counting_page_handler, 'export_results'):
                self.point_counting_page_handler.export_results()
        else:
            QMessageBox.information(self, "Export Results", "No exportable results available on this page.")

    def export_coco(self):
        """Exports annotations in COCO format."""
        # Determine which page is active and delegate to the appropriate handler
        current_index = self.stacked_widget.currentIndex()
        current_tab_text = self.stacked_widget.tabText(current_index)

        if current_tab_text == "Unsupervised Segmentation":
            if hasattr(self, 'export_coco_annotations'):
                self.export_coco_annotations()
        elif current_tab_text == "Trainable Segmentation":
            if hasattr(self, 'export_trainable_coco'):
                self.export_trainable_coco()
        else:
            QMessageBox.information(self, "Export COCO", "COCO export is not available on this page.")

    def show_documentation(self):
        """Shows the application documentation."""
        QMessageBox.information(self, "Documentation", "Documentation will be available in a future update.")

    def reset_layout(self):
        """Resets the application layout to default."""
        reply = QMessageBox.question(self, "Reset Layout",
                                   "This will reset the application layout to default. Continue?",
                                   QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
        if reply == QMessageBox.No:
            return

        # Reset layout code here
        QMessageBox.information(self, "Reset Layout", "Layout reset will be available in a future update.")

    def open_preprocessing_window(self):
        """Opens the image preprocessing dialog."""
        # Ensure an image is loaded (potentially check current page context)
        if self.image is None:
            QMessageBox.warning(self, "Warning", "Please load an image via Project Hub first.")
            return

        preprocess_dialog = PreprocessingDialog(self.image, self) # Pass original image
        if preprocess_dialog.exec() == QDialog.Accepted:
            try:
                processed = preprocess_dialog.get_result_image()
                if processed is not None:
                    # Update the main image attribute and relevant displays
                    self.image = processed
                    self.image_resized = resize_image(self.image, self.common_size) # Resize again

                    # Update displays on relevant pages
                    self.display_original_image(self.image_resized) # Segmentation page
                    # Note: Analysis page reset removed - Image Lab page manages its own state independently
                    if hasattr(self, 'trainable_original_view'): # Trainable page
                         # Need a method to update trainable base image and clear labels maybe?
                         self.update_trainable_base_image(self.image)
                    # Add updates for other pages if needed

                    QMessageBox.information(self, "Success", "Preprocessing applied successfully!")
                else:
                    QMessageBox.warning(self, "Warning", "Preprocessing did not return a valid image.")
            except Exception as e:
                logger.exception("Error applying preprocessing results.")
                QMessageBox.critical(self, "Error", f"Failed to apply preprocessing: {e}")


    def export_coco(self):
        """Exports the segmentation result to COCO JSON format."""
        # This assumes segmentation happened on the Segmentation Page
        if self.segmented_image is None:
            QMessageBox.warning(self, "Warning", "No segmented image available (run segmentation first).")
            return
        if not self.new_colors: # Color map generated during segmentation
            QMessageBox.warning(self, "Warning", "Segmentation labels/colors not found.")
            return
        if not self.image_filename or not self.original_image_size:
             QMessageBox.warning(self, "Warning", "Original image information missing.")
             return

        # Convert color keys to lists if needed by generate_coco_json
        selected_labels = [list(color) for color in self.new_colors.keys()]

        default_name = os.path.splitext(self.image_filename)[0] + "_coco.json"
        file_path, _ = self.save_file_dialog("Save COCO JSON", "JSON Files (*.json)", default_name=default_name)

        if file_path:
            try:
                logger.info(f"Exporting segmentation to COCO: {file_path}")
                # Pass necessary info: result image, original size, filename, path, output path, colors/labels
                generate_coco_json(
                    self.segmented_image,
                    self.original_image_size,
                    self.image_filename,
                    self.image_full_path, # Full path to original image
                    file_path,
                    selected_labels # The list of colors [[R,G,B], [R,G,B], ...]
                    )
                QMessageBox.information(self, "Success", f"COCO JSON saved successfully to\n{file_path}")
            except Exception as e:
                logger.exception(f"Failed to save COCO JSON: {e}")
                QMessageBox.critical(self, "Error", f"Failed to save COCO JSON: {str(e)}")

    def export_segments_as_annotations(self):
        """Exports the unsupervised segmentation result as annotations for trainable segmentation."""
        logger.debug("App: export_segments_as_annotations called")

        # Check prerequisites from the Segmentation Page context
        if not hasattr(self, 'segmented_image') or self.segmented_image is None:
            QMessageBox.warning(self, "Warning", "No segmented image available (run unsupervised segmentation first).")
            return
        if not hasattr(self, 'image') or self.image is None:
             QMessageBox.warning(self, "Warning", "Original image data is missing.")
             return
        if not hasattr(self, 'new_colors') or not self.new_colors:
             QMessageBox.warning(self, "Warning", "Segmentation color/label information is missing.")
             return
        if not hasattr(self, 'segment_names') or self.segment_names is None:
             logger.warning("Segment names missing, using defaults.")
             # Provide default names if missing? Or require them?
             # self.segment_names = {color: f"Segment_{i}" for i, color in enumerate(self.new_colors.keys())}


        try:
            # Ensure segment_names is initialized
            if not hasattr(self, 'segment_names') or self.segment_names is None:
                logger.warning("Initializing empty segment_names dictionary")
                self.segment_names = {}

            # 1. Create Label Map (Color -> Integer ID)
            # Ensure background (black) is mapped to 0, others start from 1
            unique_colors = set(tuple(c) for c in self.segmented_image.reshape(-1, 3))
            logger.debug(f"Found {len(unique_colors)} unique colors in segmented image")

            label_map = {}
            current_label_id = 1
            segment_names_for_export = {} # {label_id: name}
            segment_colors_for_export = {} # {label_id: [R, G, B]}

            # Prioritize colors present in self.new_colors and self.segment_names
            # Use a custom sorting approach to handle mixed key types (tuples and strings)
            try:
                color_keys = list(self.new_colors.keys())
                logger.debug(f"Got {len(color_keys)} color keys from new_colors")

                # Log the types of keys for debugging
                key_types = {}
                for k in color_keys:
                    key_type = type(k).__name__
                    key_types[key_type] = key_types.get(key_type, 0) + 1
                logger.debug(f"Key types in new_colors: {key_types}")

                # Separate keys by type and sort each group separately
                tuple_keys = [k for k in color_keys if isinstance(k, tuple)]
                string_keys = [k for k in color_keys if isinstance(k, str)]
                other_keys = [k for k in color_keys if not isinstance(k, (tuple, str))]

                logger.debug(f"Separated keys: {len(tuple_keys)} tuples, {len(string_keys)} strings, {len(other_keys)} other types")
            except Exception as e:
                logger.exception(f"Error processing color keys: {e}")
                # Fallback to empty lists if there's an error
                tuple_keys = []
                string_keys = []
                other_keys = []

            # Sort each group with error handling
            try:
                sorted_tuple_keys = sorted(tuple_keys)
                logger.debug(f"Sorted {len(sorted_tuple_keys)} tuple keys")
            except Exception as e:
                logger.exception(f"Error sorting tuple keys: {e}")
                sorted_tuple_keys = tuple_keys  # Use unsorted if sorting fails

            try:
                sorted_string_keys = sorted(string_keys)
                logger.debug(f"Sorted {len(sorted_string_keys)} string keys")
            except Exception as e:
                logger.exception(f"Error sorting string keys: {e}")
                sorted_string_keys = string_keys  # Use unsorted if sorting fails

            try:
                sorted_other_keys = sorted(other_keys) if other_keys else []
                logger.debug(f"Sorted {len(sorted_other_keys)} other keys")
            except Exception as e:
                logger.exception(f"Error sorting other keys: {e}")
                sorted_other_keys = other_keys  # Use unsorted if sorting fails

            # Combine the sorted groups
            try:
                sorted_colors = sorted_tuple_keys + sorted_string_keys + sorted_other_keys
                logger.debug(f"Combined sorted colors: {len(sorted_colors)} items")
            except Exception as e:
                logger.exception(f"Error combining sorted keys: {e}")
                # Fallback to using all keys unsorted if combining fails
                sorted_colors = tuple_keys + string_keys + other_keys
                logger.debug(f"Using unsorted keys as fallback: {len(sorted_colors)} items")

            for color in sorted_colors:
                if color == (0, 0, 0): # Explicitly map black to 0
                    label_map[color] = 0
                else:
                    label_map[color] = current_label_id
                    # Get segment name, provide default if missing
                    name = self.segment_names.get(color, f"Segment {current_label_id}")
                    segment_names_for_export[current_label_id] = name
                    # Store the color itself, making sure it's a list of standard Python integers
                    try:
                        # Handle different color types
                        if isinstance(color, tuple) and all(isinstance(c, (int, float)) for c in color):
                            # Tuple of numbers - convert to list of integers
                            segment_colors_for_export[current_label_id] = [int(c) for c in color]
                        elif isinstance(color, str):
                            # String representation of a color - could be various formats
                            logger.debug(f"Converting string color: {color}")
                            if color.startswith('(') and color.endswith(')'):
                                # Looks like a string representation of a tuple
                                # Extract the numbers using regex
                                import re
                                numbers = re.findall(r'\d+', color)
                                if len(numbers) >= 3:
                                    segment_colors_for_export[current_label_id] = [int(n) for n in numbers[:3]]
                                else:
                                    # Fallback to a default color if parsing fails
                                    segment_colors_for_export[current_label_id] = [255, 0, 0]  # Default to red
                            else:
                                # Other string format - fallback to default
                                segment_colors_for_export[current_label_id] = [255, 0, 0]  # Default to red
                        else:
                            # Other type - try to convert or use default
                            try:
                                # Try to convert to list of integers if possible
                                segment_colors_for_export[current_label_id] = [int(c) for c in color]
                            except (TypeError, ValueError):
                                # Fallback to a default color
                                segment_colors_for_export[current_label_id] = [255, 0, 0]  # Default to red
                    except Exception as e:
                        logger.exception(f"Error converting color {color} to list of integers: {e}")
                        # Fallback to a default color
                        segment_colors_for_export[current_label_id] = [255, 0, 0]  # Default to red

                    current_label_id += 1

            # Map any remaining unique colors found in the image (if any)
            for color in unique_colors:
                if color not in label_map:
                    if color == (0, 0, 0):
                        label_map[color] = 0
                    else:
                        logger.warning(f"Color {color} found in image but not in new_colors. Assigning new ID.")
                        label_map[color] = current_label_id
                        segment_names_for_export[current_label_id] = f"Unknown Segment {current_label_id}"
                        # Store the color for these unknown segments as well
                        try:
                            # Handle different color types
                            if isinstance(color, tuple) and all(isinstance(c, (int, float)) for c in color):
                                # Tuple of numbers - convert to list of integers
                                segment_colors_for_export[current_label_id] = [int(c) for c in color]
                            elif isinstance(color, str):
                                # String representation of a color - could be various formats
                                logger.debug(f"Converting string color for unknown segment: {color}")
                                if color.startswith('(') and color.endswith(')'):
                                    # Looks like a string representation of a tuple
                                    # Extract the numbers using regex
                                    import re
                                    numbers = re.findall(r'\d+', color)
                                    if len(numbers) >= 3:
                                        segment_colors_for_export[current_label_id] = [int(n) for n in numbers[:3]]
                                    else:
                                        # Fallback to a default color if parsing fails
                                        segment_colors_for_export[current_label_id] = [0, 0, 255]  # Default to blue for unknown
                                else:
                                    # Other string format - fallback to default
                                    segment_colors_for_export[current_label_id] = [0, 0, 255]  # Default to blue for unknown
                            else:
                                # Other type - try to convert or use default
                                try:
                                    # Try to convert to list of integers if possible
                                    segment_colors_for_export[current_label_id] = [int(c) for c in color]
                                except (TypeError, ValueError):
                                    # Fallback to a default color
                                    segment_colors_for_export[current_label_id] = [0, 0, 255]  # Default to blue for unknown
                        except Exception as e:
                            logger.exception(f"Error converting unknown segment color {color} to list of integers: {e}")
                            # Fallback to a default color
                            segment_colors_for_export[current_label_id] = [0, 0, 255]  # Default to blue for unknown

                        current_label_id += 1

            logger.debug(f"Label map created: {label_map}")
            logger.debug(f"Segment names for export: {segment_names_for_export}")
            logger.debug(f"Segment colors for export: {segment_colors_for_export}")


            # 2. Create Annotation Mask (Integer Labels)
            annotations = np.zeros(self.segmented_image.shape[:2], dtype=np.uint8)
            for r in range(self.segmented_image.shape[0]):
                for c in range(self.segmented_image.shape[1]):
                    color = tuple(self.segmented_image[r, c])
                    annotations[r, c] = label_map.get(color, 0) # Default to 0 (background) if color somehow not mapped

            logger.debug(f"Annotation mask created, unique values: {np.unique(annotations)}")

            # 3. Get Original Image and Path
            original_image = self.image.copy() # Use the full-res original
            image_path = self.image_full_path if hasattr(self, 'image_full_path') else "unknown"

            # 4. Save Dialog
            default_name = os.path.splitext(self.image_filename)[0] + "_annotations.npz"
            file_path, _ = self.save_file_dialog(
                "Export Segmentation as Annotations",
                "NumPy Archive (*.npz)",
                default_name=default_name
            )

            if not file_path:
                return

            # Ensure .npz extension
            if not file_path.lower().endswith('.npz'):
                file_path += '.npz'

            # Convert segment_names and segment_colors to JSON strings for robust saving
            segment_names_json = json.dumps(segment_names_for_export)
            segment_colors_json = json.dumps(segment_colors_for_export)

            # 5. Save Data
            np.savez_compressed(
                file_path,
                annotations=annotations,          # The uint8 label mask
                image=original_image,             # The original image data
                segment_names_json=segment_names_json, # Dict {label_id: name} as JSON string
                segment_colors_json=segment_colors_json, # Dict {label_id: [R,G,B]} as JSON string
                image_path=image_path             # Path to the original image
            )
            logger.info(f"Successfully exported annotations to {file_path}")
            QMessageBox.information(self, "Success", f"Annotations exported successfully to\n{file_path}")

        except Exception as e:
            logger.exception("Failed to export segments as annotations.")
            QMessageBox.critical(self, "Error", f"Failed to export annotations: {e}")


    def open_image_gallery(self):
        """Opens the multi-image gallery dialog (Legacy)."""
        # This is less relevant now with Project Hub, but kept for potential use
        logger.warning("Opening legacy image gallery.")
        gallery = MultiImageGallery(self)
        gallery.images_selected.connect(self.handle_gallery_images)
        gallery.exec()

    def handle_gallery_images(self, images, filenames):
        """Handles images selected from the legacy gallery."""
        logger.warning("Handling images from legacy gallery.")
        if not images or not filenames:
            return
        # This simplified handler just loads the first image, similar to old open_image
        # It doesn't integrate with the project system.
        self.image = images[0]
        self.image_filename = os.path.basename(filenames[0])
        self.image_full_path = filenames[0]
        self.original_image_size = self.image.shape[:2]
        if not self.common_size: self.common_size = (750, 750)
        self.image_resized = resize_image(self.image, self.common_size)

        # Only update the Unsupervised Segmentation page, not the Image Lab page
        # Switch to process page
        self.switch_page(self.find_tab_index_by_name("Unsupervised Segmentation"))
        self.display_original_image(self.image_resized)

        # Don't automatically update the Image Lab page
        # The Image Lab page should only be updated through its own gallery

    @Slot(str, list, list)
    def handle_analysis_page_switch(self, analysis_type: str, image_paths: list, image_infos: list):
        """Handles the request from Project Hub to load images and switch to an analysis page."""
        logger.info(f"Request received to switch to '{analysis_type}' with {len(image_paths)} image(s).")
        if not image_paths or not image_infos:
            QMessageBox.warning(self, "Error", "No valid images selected from Project Hub.")
            return

        # Store the current project from the project hub page
        self.project = self.project_hub_page.current_project
        if self.project:
            # Use the new set_project method to propagate the project to all handlers
            self.set_project(self.project)
            logger.info(f"Current project set: {self.project.name}")
        else:
            logger.warning("No active project found in Project Hub.")


        try:
            # --- Load Image Data ---
            # Clear previous multi-image data if necessary
            self.images = []
            self.image_filenames = []
            self.image_full_paths = []
            self.image_infos = []

            loaded_count = 0
            for image_path, image_info in zip(image_paths, image_infos):
                try:
                    image_data = cv2.imread(image_path)
                    if image_data is None:
                        logger.warning(f"Failed to load image: {image_path}")
                        continue
                    image_data = cv2.cvtColor(image_data, cv2.COLOR_BGR2RGB) # Convert to RGB

                    self.images.append(image_data)
                    self.image_filenames.append(image_info.filename) # Use filename from info
                    self.image_full_paths.append(image_path)
                    self.image_infos.append(image_info)
                    loaded_count += 1

                    # Set first loaded image as the primary 'current' image for the app
                    if loaded_count == 1:
                         self.image = image_data
                         self.image_filename = image_info.filename
                         self.image_full_path = image_path
                         self.original_image_size = image_data.shape[:2]
                         self.current_image_info = image_info
                         if not self.common_size: self.common_size = (750, 750)
                         self.image_resized = resize_image(self.image, self.common_size)
                         # Note: processed_image removed - Image Lab page manages its own state

                except Exception as e:
                    logger.exception(f"Error loading image {image_path}: {e}")
                    QMessageBox.warning(self, "Load Error", f"Error loading image:\n{os.path.basename(image_path)}\n{e}")

            if loaded_count == 0:
                QMessageBox.critical(self, "Error", "Failed to load any of the selected images.")
                return

            logger.info(f"Successfully loaded {loaded_count} images.")

             # --- Determine Target Page ---
            page_index = -1
            page_name = "Unknown"
            if analysis_type == 'unsupervised_segmentation':
                page_name = "Unsupervised Segmentation"
            elif analysis_type == 'trainable_segmentation':
                page_name = "Trainable Segmentation"
            elif analysis_type == 'grain_size_analysis' or analysis_type == 'grain_analysis':
                 # Use index directly as widget was inserted
                 page_index = self.grain_analysis_page_index
                 page_name = "Grain Analysis"
                 # Add detailed debug logging
                 logger.info(f"DEBUG: Grain Analysis page requested with type '{analysis_type}'")
                 logger.info(f"DEBUG: Grain Analysis page index: {page_index}")
                 logger.info(f"DEBUG: Grain Analysis page name: {page_name}")
                 # Log a warning if using the deprecated 'grain_analysis' identifier
                 if analysis_type == 'grain_analysis':
                     logger.warning("Using deprecated 'grain_analysis' identifier. Please use 'grain_size_analysis' instead.")
            elif analysis_type == 'image_lab': # Using the Image Lab page
                page_name = "Image Lab"
            elif analysis_type == 'ai_assistant':
                page_name = "AI Assistant"

                # Check if AI Assistant components need to be reinitialized
                if not hasattr(self, 'ai_assistant_gemini_worker') or self.ai_assistant_gemini_worker is None:
                    logger.info("AI Assistant worker is None, reinitializing...")
                    try:
                        from src.gui.dialogs.gemini_settings_dialog import GeminiSettingsDialog
                        from src.ai_assistant_components.src.gemini.gemini_worker import GeminiWorker

                        # Get API key and model name from settings
                        api_key = GeminiSettingsDialog.get_api_key()
                        model_name = GeminiSettingsDialog.get_model_name()

                        # Create a new worker
                        if hasattr(self, 'ai_assistant_api_key'):
                            self.ai_assistant_api_key = api_key

                        self.ai_assistant_gemini_worker = GeminiWorker(api_key, model_name=model_name)

                        # Connect the signals
                        self.ai_assistant_gemini_worker.result_ready.connect(self.handle_ai_assistant_text_result)
                        self.ai_assistant_gemini_worker.structured_data_ready.connect(self.handle_ai_assistant_structured_data)
                        self.ai_assistant_gemini_worker.bounding_boxes_ready.connect(self.handle_ai_assistant_bounding_boxes)
                        self.ai_assistant_gemini_worker.error_occurred.connect(self.handle_ai_assistant_analysis_error)
                        self.ai_assistant_gemini_worker.status_update.connect(self.update_ai_assistant_status)
                        self.ai_assistant_gemini_worker.finished.connect(self.on_ai_assistant_analysis_finished)

                        logger.info("Reinitialized AI Assistant components when switching from Project Hub")
                    except Exception as e:
                        logger.exception(f"Error reinitializing AI Assistant components: {e}")
            elif analysis_type == 'point_counting':
                page_name = "Point Counting"
            elif analysis_type == 'advanced_segmentation':
                page_name = "Advanced Segmentation"
            elif analysis_type == 'batch_processing':
                page_name = "Batch Processing"

            if page_index == -1 and page_name != "Unknown": # Find index if not already known
                 page_index = self.find_tab_index_by_name(page_name)

            if page_index == -1 :
                raise ValueError(f"Could not find page for analysis type: {analysis_type}")

            # --- Switch Page and Update UI ---
            logger.info(f"DEBUG: Before switch_page call in handle_analysis_page_switch, page_index={page_index}, page_name='{page_name}'")
            logger.info(f"DEBUG: Current tab index before switch: {self.stacked_widget.currentIndex()}")

            # Get all tab names for debugging
            tab_names = []
            for i in range(self.stacked_widget.count()):
                tab_names.append(f"{i}: {self.stacked_widget.tabText(i)}")
            logger.info(f"DEBUG: All tabs: {tab_names}")

            self.switch_page(page_index) # Switch the tab

            logger.info(f"DEBUG: After switch_page call in handle_analysis_page_switch, current index is {self.stacked_widget.currentIndex()}")
            logger.info(f"Switched to page: {page_name}")

            # Update the specific page with loaded image(s)
            if page_name == "Unsupervised Segmentation":
                self.display_original_image(self.image_resized) # Display first image
                if hasattr(self, 'process_gallery'):
                    # Check for existing images in the gallery
                    existing_images = len(self.process_gallery.images) > 0
                    existing_paths = set(self.process_gallery.file_paths) if existing_images else set()

                    # Only clear if no existing images
                    if not existing_images:
                        self.process_gallery.clear_images()

                    # Add new images, skipping duplicates
                    added_count = 0
                    first_new_image_index = None
                    first_uploaded_image_index = None  # Track the first image from upload (new or existing)

                    for img, fname, fpath in zip(self.images, self.image_filenames, self.image_full_paths):
                        if fpath not in existing_paths:
                            # Store the index of the first new image added
                            if first_new_image_index is None:
                                first_new_image_index = len(self.process_gallery.images)
                            self.process_gallery.add_image(img, fname, fpath)
                            added_count += 1
                            existing_paths.add(fpath)

                        # Track the first uploaded image (whether new or existing)
                        if first_uploaded_image_index is None:
                            if fpath in existing_paths:
                                # Find the index of this existing image in the gallery
                                try:
                                    first_uploaded_image_index = self.process_gallery.file_paths.index(fpath)
                                except ValueError:
                                    # If not found, it means it was just added
                                    first_uploaded_image_index = len(self.process_gallery.images) - 1

                    # Select appropriate image based on the situation
                    if self.process_gallery.images:
                        if not existing_images and added_count > 0:
                            # If gallery was empty, select the first image
                            self.process_gallery.select_image(0)
                            self.on_process_gallery_image_clicked(0) # Load first image data fully
                        elif first_new_image_index is not None:
                            # If adding new images to existing gallery, select the first newly added image
                            self.process_gallery.select_image(first_new_image_index)
                            self.on_process_gallery_image_clicked(first_new_image_index) # Load the new image data fully
                        elif first_uploaded_image_index is not None:
                            # If uploading existing images, select the first uploaded image
                            self.process_gallery.select_image(first_uploaded_image_index)
                            self.on_process_gallery_image_clicked(first_uploaded_image_index) # Load the existing image data fully

                    # Show appropriate feedback message to the user
                    if page_name == "Unsupervised Segmentation":
                        if added_count > 0 and len(self.images) - added_count > 0:
                            # Mixed case: some new, some existing
                            existing_count = len(self.images) - added_count
                            QMessageBox.information(self, "Images Loaded",
                                                  f"{added_count} new image(s) added to gallery.\n{existing_count} image(s) were already in the gallery and have been selected.")
                        elif added_count > 0:
                            # All new images
                            if added_count == 1:
                                QMessageBox.information(self, "Image Added", "New image added to gallery and selected.")
                        elif len(self.images) > 0:
                            # All existing images
                            if len(self.images) == 1:
                                QMessageBox.information(self, "Existing Image", "This image is already in the gallery and has been selected.")
                            else:
                                QMessageBox.information(self, "Existing Images", f"These {len(self.images)} images are already in the gallery. The first one has been selected.")
                if hasattr(self, 'setup_process_navigation'): self.setup_process_navigation()
                if hasattr(self, 'update_image_counter'): self.update_image_counter()

                # Check if the image_info_label exists before updating it
                if hasattr(self, 'update_image_info') and hasattr(self, 'image_info_label') and self.image_info_label is not None:
                    try:
                        self.update_image_info(self.image)
                    except RuntimeError as e:
                        logger.warning(f"Could not update image info: {e}")
                        # The label might have been deleted, so don't try to update it


            elif page_name == "Image Lab": # New independent Image Lab page
                logger.info("Loading images into Image Lab page.")

                # Set the project in the Image Lab page
                if hasattr(self, 'image_lab_page') and hasattr(self, 'project') and self.project:
                    self.image_lab_page.set_project(self.project)
                    logger.info(f"Set project for Image Lab page: {self.project.name}")

                # Load images into the Image Lab page
                if hasattr(self, 'image_lab_page'):
                    self.image_lab_page.load_images(self.image_full_paths, self.image_infos)
                    logger.info(f"Loaded {len(self.image_full_paths)} images into Image Lab page")
                else:
                    logger.warning("Image Lab page not found.")


            elif page_name == "Trainable Segmentation":
                logger.info("Loading images into Trainable Segmentation page.")
                
                # Use the dedicated load_images_from_project_hub method from trainable segmentation handlers
                if hasattr(self, 'load_images_from_project_hub'):
                    self.load_images_from_project_hub(self.image_full_paths, self.image_infos)
                    logger.info(f"Loaded {len(self.image_full_paths)} images into Trainable Segmentation page")
                else:
                    logger.warning("load_images_from_project_hub method not found in trainable segmentation handlers.")


            elif page_name == "Grain Analysis":
                 if hasattr(self, 'grain_analysis_widget'):
                     logger.info("Loading images into Grain Analysis widget.")
                     # Use the widget's own method to load images if available
                     if hasattr(self.grain_analysis_widget, 'load_image_list'):
                          self.grain_analysis_widget.load_image_list(self.image_full_paths, self.image_infos)
                     # Fallback: load into gallery if method above doesn't exist
                     elif hasattr(self.grain_analysis_widget, 'grain_gallery'):
                         # Check for existing images in the gallery
                         existing_images = len(self.grain_analysis_widget.grain_gallery.images) > 0
                         existing_paths = set(self.grain_analysis_widget.grain_gallery.file_paths) if existing_images else set()

                         # Only clear if no existing images
                         if not existing_images:
                             self.grain_analysis_widget.grain_gallery.clear_images()

                         # Add new images, skipping duplicates
                         added_count = 0
                         for img, filename, filepath in zip(self.images, self.image_filenames, self.image_full_paths):
                              # Skip if this image is already in the gallery
                              if filepath in existing_paths:
                                  continue

                              # Grain analysis might expect PIL images
                              pil_image = Image.fromarray(img)
                              self.grain_analysis_widget.grain_gallery.add_image(pil_image, filename, filepath)
                              added_count += 1
                              existing_paths.add(filepath)

                         # Select first image if we just loaded the gallery, otherwise keep current selection
                         if self.grain_analysis_widget.grain_gallery.images and not existing_images and added_count > 0:
                              self.grain_analysis_widget.grain_gallery.select_image(0)
                              self.grain_analysis_widget.on_gallery_image_clicked(0) # Trigger load for first image
                     else: # Absolute fallback: just display first image
                         self.grain_analysis_widget.load_image(self.image_full_path)
                     # Ensure actions/buttons are updated based on project/image state
                     if hasattr(self.grain_analysis_widget, 'update_action_states'):
                          self.grain_analysis_widget.update_action_states()

            elif page_name == "AI Assistant":
                 logger.info("Loading images into AI Assistant.")
                 if hasattr(self, 'ai_assistant_gallery'):
                     # Check for existing images in the gallery
                     existing_images = len(self.ai_assistant_gallery.images) > 0
                     existing_paths = set(self.ai_assistant_gallery.file_paths) if existing_images else set()

                     # Only clear if no existing images
                     if not existing_images:
                         self.ai_assistant_gallery.clear_images()

                     # Add new images, skipping duplicates
                     added_count = 0
                     for img, fname, fpath in zip(self.images, self.image_filenames, self.image_full_paths):
                         if fpath not in existing_paths:
                             self.ai_assistant_gallery.add_image(img, fname, fpath)
                             added_count += 1
                             existing_paths.add(fpath)

                     # Select first image if we just loaded the gallery, otherwise keep current selection
                     if self.ai_assistant_gallery.images and not existing_images and added_count > 0:
                         self.ai_assistant_gallery.select_image(0)
                         self.on_ai_assistant_image_clicked(0) # Trigger load for first image
                 else:
                     logger.warning("AI Assistant gallery not found.")


            elif page_name == "Point Counting":
                logger.info("Loading images into Point Counting.")
                if self.point_counting_page_handler:
                    self.point_counting_page_handler.load_images_from_project_hub(self.image_full_paths, self.image_infos)
                else:
                    QMessageBox.critical(self, "Error", "Point Counting handler is not available.")

            elif page_name == "Advanced Segmentation":
                logger.info("Loading images into Advanced Segmentation.")
                if hasattr(self, 'advanced_segmentation_page'):
                    # Update the project reference in the Advanced Segmentation page
                    if hasattr(self, 'project') and self.project:
                        # Use the new set_project method
                        if hasattr(self.advanced_segmentation_page, 'set_project'):
                            self.advanced_segmentation_page.set_project(self.project)
                            logger.info(f"Updated Advanced Segmentation page project reference to {self.project.name}")

                    # Load images into the Advanced Segmentation page
                    self.advanced_segmentation_page.load_images_from_project_hub(self.image_full_paths, self.image_infos)

                    # Advanced segmentation state loading is now manual
                    # No automatic state loading after loading images
                    logger.info("Advanced Segmentation state loading is now manual. Use 'Load Annotations' button instead.")
                else:
                    QMessageBox.critical(self, "Error", "Advanced Segmentation page is not available.")

            elif page_name == "Batch Processing":
                logger.info("Loading images into Batch Processing.")
                if hasattr(self, 'batch_processing_page'):
                    # Update the project reference in the Batch Processing page
                    if hasattr(self, 'project') and self.project:
                        if hasattr(self.batch_processing_page, 'set_project'):
                            self.batch_processing_page.set_project(self.project)
                            logger.info(f"Updated Batch Processing page project reference to {self.project.name}")

                    # Load images into the Batch Processing page
                    if hasattr(self.batch_processing_page, 'load_images_from_project_hub'):
                        self.batch_processing_page.load_images_from_project_hub(self.image_full_paths, self.image_infos)
                        logger.info(f"Loaded {len(self.image_full_paths)} images into Batch Processing page")
                    else:
                        logger.warning("Batch Processing page does not have load_images_from_project_hub method")
                else:
                    QMessageBox.critical(self, "Error", "Batch Processing page is not available.")

            # --- Show message for multiple images ---
            if len(self.images) > 1:
                QMessageBox.information(self, "Multiple Images Loaded",
                                      f"{len(self.images)} images loaded.\nUse gallery thumbnails or navigation buttons (if available on the page) to switch.")

        except Exception as e:
            logger.exception(f"Error handling page switch for '{analysis_type}': {e}")
            QMessageBox.critical(self, "Page Load Error", f"Failed to load data for {analysis_type}:\n{str(e)}")

    def _check_for_unsaved_annotations(self):
        """Check for unsaved annotations in trainable and advanced segmentation pages.
        
        Returns:
            list: List of page names that have unsaved annotations
        """
        pages_with_unsaved_data = []
        
        try:
            # Check trainable segmentation page
            if hasattr(self, 'trainable_segmentation_page') and self.trainable_segmentation_page:
                if hasattr(self.trainable_segmentation_page, 'handlers'):
                    handlers = self.trainable_segmentation_page.handlers
                    # Check if there are existing annotations that might be unsaved
                    if hasattr(handlers, '_has_existing_annotations'):
                        has_annotations = handlers._has_existing_annotations()
                        if has_annotations:
                            pages_with_unsaved_data.append("Trainable Segmentation")
                        
            # Check advanced segmentation page
            if hasattr(self, 'advanced_segmentation_page') and self.advanced_segmentation_page:
                if hasattr(self.advanced_segmentation_page, 'handler'):
                    handler = self.advanced_segmentation_page.handler
                    # Check if annotations have been modified
                    if hasattr(handler, '_annotations_modified'):
                        annotations_modified = handler._annotations_modified
                        if annotations_modified:
                            pages_with_unsaved_data.append("Advanced Segmentation")
                        
        except Exception as e:
            logger.exception(f"Error checking for unsaved annotations: {e}")
        return pages_with_unsaved_data

    def closeEvent(self, event):
        """Handle application close event."""
        try:
            # First check for unsaved annotations
            pages_with_unsaved_data = self._check_for_unsaved_annotations()
            
            if pages_with_unsaved_data:
                # Import the dialog here to avoid circular imports
                from src.gui.dialogs.unsaved_annotations_dialog import UnsavedAnnotationsDialog
                from PySide6.QtWidgets import QDialog
                
                dialog = UnsavedAnnotationsDialog(self, pages_with_unsaved_data)
                result = dialog.exec()
                
                if result == QDialog.DialogCode.Rejected:  # User chose "Return to App"
                    event.ignore()
                    return
                # If result == QDialog.DialogCode.Accepted, continue with closing
            
            # Show general close confirmation dialog to prevent accidental closes
            from src.gui.dialogs.close_confirmation_dialog import CloseConfirmationDialog
            from PySide6.QtWidgets import QDialog
            
            confirmation_dialog = CloseConfirmationDialog(self)
            result = confirmation_dialog.exec()
            
            if result == QDialog.DialogCode.Rejected:  # User chose "Cancel"
                event.ignore()
                return
            # If result == QDialog.DialogCode.Accepted, continue with closing
            
        except Exception as e:
            logger.exception(f"Error during close confirmation: {e}")
        
        # Save settings before closing
        # This will save the theme settings but won't apply the theme
        # since we've modified save_settings() to not call apply_theme()
        self.save_settings()

        # Call the specific handler for proper shutdown sequence
        self.handle_app_shutdown() # Call this first

        # Accept the close event
        event.accept()

    def handle_app_shutdown(self):
        """Handle application shutdown event."""
        logger.info("Application shutdown initiated")

        # Save state for all pages
        try:
            # Save point counting state
            if hasattr(self, 'point_counting_page_handler') and self.point_counting_page_handler:
                self.point_counting_page_handler.handle_app_shutdown()

            # Trainable segmentation state saving is now manual
            # No automatic state saving during application shutdown

            # Advanced segmentation state saving is now manual
            # No automatic state saving during application shutdown

            # Save unsupervised segmentation state
            if hasattr(self, 'save_segmentation_state'):
                self.save_segmentation_state()

            # Save Image Lab state (independent page)
            if hasattr(self, 'image_lab_page') and hasattr(self.image_lab_page, 'save_state'):
                self.image_lab_page.save_state()
            # Note: save_analysis_state removed - Image Lab page manages its own state

            logger.info("All page states saved during shutdown")
        except Exception as e:
            logger.exception(f"Error saving state during shutdown: {e}")

    def load_settings(self):
        """Loads settings from QSettings and applies them to the UI.

        This overrides the SettingsHandlers.load_settings method to ensure
        the theme is applied silently during application startup.
        """
        # Call the parent class's load_settings method to load all settings
        result = SettingsHandlers.load_settings(self)

        # Apply the theme silently (without showing a message box)
        try:
            # Apply theme with show_message=False to avoid showing a message box
            self.apply_theme(show_message=False)
            logger.info("Theme applied silently during application startup")
        except Exception as e:
            logger.error(f"Error applying theme during startup: {e}")

        return result

    def _update_theme_preview(self):
        """Updates the theme preview based on selected options."""
        try:
            from src.gui.styles.theme_config import apply_theme

            # Get selected theme options
            theme_base = self.theme_combo.currentText().lower().replace(" theme", "")
            color_scheme = self.color_scheme_combo.currentText().lower()
            font_family = self.font_family_combo.currentText()

            # Determine font size
            font_size_name = self.font_size_combo.currentText()
            try:
                from src.gui.styles.theme_config import FONT_SIZES
                font_size = FONT_SIZES.get(font_size_name, 10)
            except ImportError:
                # Default font sizes if import fails
                font_sizes = {"small": 8, "normal": 10, "medium": 12, "large": 14, "extra-large": 16}
                font_size = font_sizes.get(font_size_name, 10)

            # Construct theme name
            theme_name = f"{color_scheme}-{theme_base}"

            # Apply theme to preview frame
            apply_theme(self.theme_preview_frame, theme_name, font_family, font_size)
        except Exception as e:
            logger.error(f"Error updating theme preview: {e}")

    def clear_all_galleries(self):
        """
        Clear all image galleries across all pages using their dedicated clear methods.
        This is a simpler and cleaner approach than the complex reset_application_state.
        """
        logger.info("Clearing all image galleries...")
        
        try:
            # Clear Process/Unsupervised Segmentation gallery
            if hasattr(self, 'process_gallery'):
                if hasattr(self.process_gallery, 'clear_images'):
                    self.process_gallery.clear_images()
                    logger.info("Process gallery cleared")
            
            # Clear Trainable Segmentation gallery (app inherits from TrainableSegmentationHandlers)
            try:
                if hasattr(self, 'clear_trainable_gallery'):
                    self.clear_trainable_gallery()
                    logger.info("Trainable segmentation gallery cleared")
                else:
                    logger.warning("clear_trainable_gallery method not found")
            except Exception as e:
                logger.error(f"Error clearing trainable segmentation gallery: {e}")
            
            # Clear Image Lab gallery
            if hasattr(self, 'image_lab_page_handler') and self.image_lab_page_handler:
                if hasattr(self.image_lab_page_handler, 'clear_image_lab_gallery'):
                    self.image_lab_page_handler.clear_image_lab_gallery()
                    logger.info("Image lab gallery cleared")
            
            # Clear Point Counting gallery
            if hasattr(self, 'point_counting_page_handler') and self.point_counting_page_handler:
                if hasattr(self.point_counting_page_handler, 'clear_point_counting_gallery'):
                    self.point_counting_page_handler.clear_point_counting_gallery()
                    logger.info("Point counting gallery cleared")
            
            # Clear Advanced Segmentation gallery (via page's handler)
            try:
                if hasattr(self, 'advanced_segmentation_page') and self.advanced_segmentation_page:
                    if hasattr(self.advanced_segmentation_page, 'handler') and hasattr(self.advanced_segmentation_page.handler, 'clear_advanced_segmentation_gallery'):
                        self.advanced_segmentation_page.handler.clear_advanced_segmentation_gallery()
                        logger.info("Advanced segmentation gallery cleared")
                    else:
                        logger.warning("Advanced segmentation page handler or clear method not found")
                else:
                    logger.warning("Advanced segmentation page not found")
            except Exception as e:
                logger.error(f"Error clearing advanced segmentation gallery: {e}")
            
            # Clear Analysis gallery
            if hasattr(self, 'analysis_page_handler') and self.analysis_page_handler:
                if hasattr(self.analysis_page_handler, 'clear_analysis_gallery'):
                    self.analysis_page_handler.clear_analysis_gallery()
                    logger.info("Analysis gallery cleared")
            
            # Clear AI Assistant gallery (app inherits from AIAssistantHandlers)
            try:
                if hasattr(self, 'clear_ai_assistant_gallery'):
                    self.clear_ai_assistant_gallery()
                    logger.info("AI assistant gallery cleared")
                else:
                    logger.warning("clear_ai_assistant_gallery method not found")
            except Exception as e:
                logger.error(f"Error clearing AI assistant gallery: {e}")
            
            # Clear Grain Analysis gallery
            if hasattr(self, 'grain_analysis_widget') and self.grain_analysis_widget:
                if hasattr(self.grain_analysis_widget, 'clear_grain_gallery'):
                    self.grain_analysis_widget.clear_grain_gallery()
                    logger.info("Grain analysis gallery cleared")
            
            # Clear Batch Processing data
            if hasattr(self, 'batch_processing_page') and self.batch_processing_page:
                try:
                    # Clear selected images and results
                    if hasattr(self.batch_processing_page, 'selected_images'):
                        self.batch_processing_page.selected_images = []
                    if hasattr(self.batch_processing_page, 'selected_image_infos'):
                        self.batch_processing_page.selected_image_infos = []
                    if hasattr(self.batch_processing_page, 'processing_results'):
                        self.batch_processing_page.processing_results.clear()
                    if hasattr(self.batch_processing_page, 'individual_scale_factors'):
                        self.batch_processing_page.individual_scale_factors.clear()
                    
                    # Clear UI elements
                    if hasattr(self.batch_processing_page, 'selected_images_label'):
                        self.batch_processing_page.selected_images_label.setText("0 images selected")
                    if hasattr(self.batch_processing_page, 'selected_images_list'):
                        self.batch_processing_page.selected_images_list.clear()
                    if hasattr(self.batch_processing_page, '_update_results_grid'):
                        self.batch_processing_page._update_results_grid()
                    if hasattr(self.batch_processing_page, '_reset_progress'):
                        self.batch_processing_page._reset_progress()
                    
                    logger.info("Batch processing data cleared")
                except Exception as e:
                    logger.error(f"Error clearing batch processing data: {e}")
            
            logger.info("All image galleries cleared successfully")
            
        except Exception as e:
            logger.exception(f"Error clearing galleries: {e}")

    def reset_application_state(self, show_loading_indicator=True):
        """Resets the entire application state to prevent conflicts when switching projects.
        This is a global state reset that simulates closing and reopening the application.

        Args:
            show_loading_indicator: Whether to show a loading indicator during reset
        """
        logger.info("Performing global application state reset for project switching")

        # Show loading indicator if requested
        prev_status = None
        if show_loading_indicator and hasattr(self, 'statusBar'):
            prev_status = self.statusBar().currentMessage()
            self.statusBar().showMessage("Resetting application state...")
            QApplication.processEvents()  # Force UI update

        try:
            # First, save any current state that needs to be preserved
            # (like application settings, not project-specific data)
            if hasattr(self, 'save_settings'):
                self.save_settings()

            # Switch to Project Hub page first to avoid page-specific issues
            project_hub_index = self.find_tab_index_by_name("Project Hub")
            if project_hub_index != -1:
                super().switch_page(project_hub_index)  # Use the UI method directly to avoid state saving/loading
                QApplication.processEvents()  # Force UI update

            # --- RESET CORE APPLICATION STATE ---

            # Reset core application attributes
            self.image = None
            self.image_resized = None
            self.segmented_images = []
            self.segmented_image = None
            self.new_colors = {}
            self.label_percentages = {}
            self.common_size = (750, 750)  # Reset to default
            self.image_filename = None
            self.image_full_path = None
            self.original_image_size = None
            self.crop_rect = None
            # Note: processed_image and analysis_states removed - Image Lab page manages its own state
            self.current_image_info = None

            # --- CLEAR ALL GALLERIES USING THE SIMPLE APPROACH ---
            self.clear_all_galleries()

            # --- RESET REMAINING UI ELEMENTS ---
            
            # Reset image views
            if hasattr(self, 'original_image_view'):
                self.original_image_view.clear()
            if hasattr(self, 'segmented_image_view'):
                self.segmented_image_view.clear()
            # Also clear the synchronized views
            if hasattr(self, 'original_sync_view'):
                self.original_sync_view.set_pixmap(QPixmap())
            if hasattr(self, 'segmented_sync_view'):
                self.segmented_sync_view.set_pixmap(QPixmap())
            # Clear stored pixmaps for synchronization
            if hasattr(self, '_original_pixmap'):
                delattr(self, '_original_pixmap')
            if hasattr(self, '_segmented_pixmap'):
                delattr(self, '_segmented_pixmap')
            if hasattr(self, 'segment_grid_widget'):
                if hasattr(self.segment_grid_widget, 'clear_segments'):
                    self.segment_grid_widget.clear_segments()
            # Hide segment grid container if it exists
            if hasattr(self, 'segment_grid_container'):
                self.segment_grid_container.hide()

            # Reset label percentages and colors
            if hasattr(self, 'info_frame'):
                # Clear all widgets in the info frame
                while self.info_frame.layout().count():
                    item = self.info_frame.layout().takeAt(0)
                    if item.widget():
                        item.widget().deleteLater()
                    elif item.layout():
                        # Clear nested layouts
                        while item.layout().count():
                            child = item.layout().takeAt(0)
                            if child.widget():
                                child.widget().deleteLater()

            # Reset merge segments UI
            if hasattr(self, 'merge_inner_layout'):
                # Clear all widgets in the merge inner layout
                while self.merge_inner_layout.count():
                    item = self.merge_inner_layout.takeAt(0)
                    if item.widget():
                        item.widget().deleteLater()
                    elif item.layout():
                        # Clear nested layouts
                        while item.layout().count():
                            child = item.layout().takeAt(0)
                            if child.widget():
                                child.widget().deleteLater()
            # Reset merge variables
            if hasattr(self, 'merge_vars'):
                self.merge_vars = {}

            # Reset segmentation data
            self.segmented_images = []
            self.segmented_image = None
            self.new_colors = {}
            self.label_percentages = {}
            if hasattr(self, 'segment_names'):
                self.segment_names = {}

            # Reset segmentation controls
            if hasattr(self, 'epoch_slider'):
                self.epoch_slider.setValue(0)
                self.epoch_slider.setEnabled(False)
            if hasattr(self, 'epoch_label'):
                self.epoch_label.setText("Epoch: 0")
            if hasattr(self, 'color_palette_combo'):
                self.color_palette_combo.setCurrentIndex(0)
            if hasattr(self, 'train_epoch'):
                self.train_epoch.setValue(25)  # Reset to default
            if hasattr(self, 'mod_dim1'):
                self.mod_dim1.setValue(25)  # Reset to default
            if hasattr(self, 'mod_dim2'):
                self.mod_dim2.setValue(25)  # Reset to default
            if hasattr(self, 'segmentation_method'):
                self.segmentation_method.setCurrentIndex(0)  # Reset to default

            # Reset Image Lab page
            if hasattr(self, 'image_lab_page') and hasattr(self.image_lab_page, 'clear'):
                # Use the Image Lab page's clear method
                self.image_lab_page.clear()
                logger.info("Reset Image Lab page")

            # Reset Trainable Segmentation page
            if hasattr(self, 'trainable_gallery'):
                self.trainable_gallery.clear()
            if hasattr(self, 'trainable_original_view'):
                self.trainable_original_view.clear()
            if hasattr(self, 'trainable_result_view'):
                self.trainable_result_view.clear()
            # Reset trainable controls
            if hasattr(self, 'label_selection_combo'):
                self.label_selection_combo.clear()
            if hasattr(self, 'draw_button'):
                self.draw_button.setChecked(False)
            if hasattr(self, 'erase_button'):
                self.erase_button.setChecked(False)
            if hasattr(self, 'sam_magic_wand_button'):
                self.sam_magic_wand_button.setChecked(False)

            # Reset multi-image handler if it exists
            if hasattr(self, 'multi_image_handler'):
                if hasattr(self.multi_image_handler, 'clear_all_images'):
                    self.multi_image_handler.clear_all_images()
                elif hasattr(self.multi_image_handler, 'clear'):
                    self.multi_image_handler.clear()
                # Also reset image paths if they exist
                if hasattr(self, 'image_paths'):
                    self.image_paths = []
                if hasattr(self, 'current_image_index'):
                    self.current_image_index = -1

            # Reset trainable segmentation attributes
            if hasattr(self, 'training_labels'):
                self.training_labels = None
            if hasattr(self, 'preview_labels'):
                self.preview_labels = None
            if hasattr(self, 'features'):
                self.features = None
            if hasattr(self, 'classifier'):
                self.classifier = None
            if hasattr(self, 'result'):
                self.result = None
            if hasattr(self, 'trainable_image'):
                self.trainable_image = None

            # Reset trainable segmentation UI elements
            if hasattr(self, 'trainable_original_view'):
                self.trainable_original_view.clear()
            if hasattr(self, 'trainable_result_view'):
                self.trainable_result_view.clear()
            if hasattr(self, 'feature_importance_view'):
                self.feature_importance_view.clear()

            # Reset class percentages widget
            if hasattr(self, 'class_percentages_widget'):
                self.class_percentages_widget.update_percentages({}, {})

            # Reset label names and colors to defaults
            if hasattr(self, 'label_names'):
                from src.gui.handlers.trainable_segmentation_handlers import DEFAULT_LABEL_NAMES, DEFAULT_MASK_COLORS
                # Reinitialize label_names as a dictionary
                self.label_names = {}
                for i, name in enumerate(DEFAULT_LABEL_NAMES):
                    self.label_names[i+1] = name # 1-indexed
            if hasattr(self, 'mask_colors'):
                from src.gui.handlers.trainable_segmentation_handlers import DEFAULT_MASK_COLORS
                self.mask_colors = DEFAULT_MASK_COLORS.copy()

            if hasattr(self, 'max_label_index'):
                 self.max_label_index = len(self.label_names) if hasattr(self, 'label_names') and isinstance(self.label_names, dict) else len(DEFAULT_LABEL_NAMES)


            # Reset label selection combo
            if hasattr(self, 'label_selection_combo'):
                self.label_selection_combo.clear()
                if hasattr(self, 'label_names') and isinstance(self.label_names, dict):
                    for idx in sorted(self.label_names.keys()):
                        self.label_selection_combo.addItem(self.label_names[idx])
                    if self.label_names:
                         self.label_selection_combo.setCurrentIndex(0)


            # Reset current label
            if hasattr(self, 'current_label'):
                self.current_label = 1

            # Update label color indicator
            if hasattr(self, 'update_label_color_indicator'):
                self.update_label_color_indicator()

            # Reset Point Counting page
            if hasattr(self, 'point_counting_page_handler') and self.point_counting_page_handler:
                self.point_counting_page_handler.reset_state()

            # Reset AI Assistant page
            if hasattr(self, 'ai_assistant_gallery'):
                self.ai_assistant_gallery.clear()
            if hasattr(self, 'ai_assistant_result_text'):
                self.ai_assistant_result_text.clear()
            if hasattr(self, 'ai_assistant_prompt_text'):
                self.ai_assistant_prompt_text.clear()
            if hasattr(self, 'ai_assistant_context_text'):
                self.ai_assistant_context_text.clear()

            # Reset AI Assistant image preview
            if hasattr(self, 'ai_assistant_image_preview'):
                self.ai_assistant_image_preview.clear()
            if hasattr(self, 'ai_assistant_image_scene'):
                self.ai_assistant_image_scene.clear()
            if hasattr(self, 'ai_assistant_image_item'):
                self.ai_assistant_image_item = None
            self.ai_assistant_current_pixmap = None
            self.ai_assistant_current_image_id = None

            # Reset AI Assistant structured data table
            if hasattr(self, 'ai_assistant_structured_data_table'):
                self.ai_assistant_structured_data_table.setRowCount(0)
                self.ai_assistant_structured_data_table.clearContents()

            # Reset AI Assistant bounding boxes
            self.ai_assistant_bounding_boxes = []

            # Reinitialize AI Assistant components
            try:
                from src.gui.dialogs.gemini_settings_dialog import GeminiSettingsDialog
                from src.ai_assistant_components.src.gemini.gemini_worker import GeminiWorker

                # Get API key and model name from settings
                api_key = GeminiSettingsDialog.get_api_key()
                model_name = GeminiSettingsDialog.get_model_name()

                # Create a new worker
                if hasattr(self, 'ai_assistant_api_key'):
                    self.ai_assistant_api_key = api_key

                if hasattr(self, 'ai_assistant_gemini_worker'):
                    self.ai_assistant_gemini_worker = GeminiWorker(api_key, model_name=model_name)

                    # Connect the signals
                    self.ai_assistant_gemini_worker.result_ready.connect(self.handle_ai_assistant_text_result)
                    self.ai_assistant_gemini_worker.structured_data_ready.connect(self.handle_ai_assistant_structured_data)
                    self.ai_assistant_gemini_worker.bounding_boxes_ready.connect(self.handle_ai_assistant_bounding_boxes)
                    self.ai_assistant_gemini_worker.error_occurred.connect(self.handle_ai_assistant_analysis_error)
                    self.ai_assistant_gemini_worker.status_update.connect(self.update_ai_assistant_status)
                    self.ai_assistant_gemini_worker.finished.connect(self.on_ai_assistant_analysis_finished)

                    logger.info("Reinitialized AI Assistant components after reset")
            except Exception as e:
                logger.exception(f"Error reinitializing AI Assistant components: {e}")

            # Reset Grain Analysis widget
            if hasattr(self, 'grain_analysis_widget'):
                if hasattr(self.grain_analysis_widget, 'reset_grain_analysis_state'):
                    self.grain_analysis_widget.reset_grain_analysis_state(clear_image=True)
                elif hasattr(self.grain_analysis_widget, 'reset_app_state'):
                    self.grain_analysis_widget.reset_app_state(clear_image=True)

                # Also reset the grain analysis gallery if it exists
                if hasattr(self, 'grain_gallery'):
                    self.grain_gallery.clear_images()
                elif hasattr(self.grain_analysis_widget, 'grain_gallery'):
                    self.grain_analysis_widget.grain_gallery.clear_images()

            # --- RESET BACKGROUND PROCESSES ---

            # Reset any active workers or threads
            if hasattr(self, 'worker') and self.worker:
                try:
                    self.worker.cancel()
                    self.worker = None
                except Exception as e:
                    logger.warning(f"Error canceling worker: {e}")

            # Cancel any other background processes
            for attr_name in dir(self):
                if attr_name.endswith('_worker') or attr_name.endswith('_thread'):
                    worker = getattr(self, attr_name, None)
                    if worker and hasattr(worker, 'cancel'):
                        try:
                            worker.cancel()
                            setattr(self, attr_name, None)
                        except Exception as e:
                            logger.warning(f"Error canceling {attr_name}: {e}")
                    elif worker and hasattr(worker, 'terminate'):
                        try:
                            worker.terminate()
                            setattr(self, attr_name, None)
                        except Exception as e:
                            logger.warning(f"Error terminating {attr_name}: {e}")

            # --- RESET STATE STORAGE ---

            # Clear session states from memory
            if hasattr(self, 'session_state'):
                self.session_state = None

            # --- RESET PROJECT REFERENCE ---

            # Clear project reference (will be set by the project hub after loading)
            self.project = None

            # --- RESET UI ELEMENTS ---

            # Update window title
            self.setWindowTitle("VisionLab Ai - Project Hub")

            # Force UI update
            QApplication.processEvents()

            logger.info("Application state reset completed successfully")

        except Exception as e:
            logger.exception(f"Error during application state reset: {e}")
        finally:
            # Restore previous status message or clear
            if show_loading_indicator and hasattr(self, 'statusBar'):
                if prev_status:
                    self.statusBar().showMessage(prev_status)
                else:
                    self.statusBar().clearMessage()

    def clear_process_gallery(self):
        """Clears all images from the process gallery."""
        if hasattr(self, 'process_gallery'):
            self.process_gallery.clear_images()
            logger.info("Process gallery cleared successfully")
        
        # Clear the process image views as well
        if hasattr(self, 'original_image_view'):
            self.original_image_view.clear()
            logger.info("Process original image view cleared")
        if hasattr(self, 'segmented_image_view'):
            self.segmented_image_view.clear()
            logger.info("Process segmented image view cleared")

    def clear_trainable_gallery(self):
        """Clears all images from the trainable segmentation gallery."""
        if hasattr(self, 'trainable_gallery'):
            self.trainable_gallery.clear_images()
            logger.info("Trainable gallery cleared successfully")
        
        # Clear trainable segmentation image views as well
        try:
            if hasattr(self, 'trainable_original_view') and self.trainable_original_view:
                self.trainable_original_view.clear()
                logger.info("Trainable original view cleared")
            if hasattr(self, 'trainable_result_view') and self.trainable_result_view:
                self.trainable_result_view.clear()
                logger.info("Trainable result view cleared")
            if hasattr(self, 'trainable_original_sync_view') and self.trainable_original_sync_view:
                self.trainable_original_sync_view.clear()
                logger.info("Trainable original sync view cleared")
            if hasattr(self, 'trainable_result_sync_view') and self.trainable_result_sync_view:
                self.trainable_result_sync_view.clear()
                logger.info("Trainable result sync view cleared")
        except Exception as e:
            logger.error(f"Error clearing trainable segmentation image views: {e}")
        
        # Clear trained classifier and related data
        try:
            if hasattr(self, 'classifier'):
                self.classifier = None
                logger.info("Trained classifier cleared")
            if hasattr(self, 'features'):
                self.features = None
                logger.info("Features cleared")
            if hasattr(self, 'feature_params'):
                self.feature_params = None
                logger.info("Feature parameters cleared")
            if hasattr(self, 'result'):
                self.result = None
                logger.info("Result cleared")
            
            # Clear feature importance view
            if hasattr(self, 'feature_importance_view') and self.feature_importance_view:
                self.feature_importance_view.clear()
                logger.info("Feature importance view cleared")
            
            # Reset class percentages widget
            if hasattr(self, 'class_percentages_widget') and self.class_percentages_widget:
                self.class_percentages_widget.update_percentages({}, {})
                logger.info("Class percentages widget reset")
            
            # Clear classifier and features in trainable segmentation handlers
            if hasattr(self, 'trainable_segmentation_page_handler') and self.trainable_segmentation_page_handler:
                if hasattr(self.trainable_segmentation_page_handler, 'clear_classifier_and_features'):
                    self.trainable_segmentation_page_handler.clear_classifier_and_features()
                    logger.info("Trainable segmentation handler classifier data cleared")
                
        except Exception as e:
            logger.error(f"Error clearing trained classifier and related data: {e}")

