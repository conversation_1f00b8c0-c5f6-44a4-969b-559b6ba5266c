# src/gui/handlers/segmentation_handlers.py

import os
import numpy as np
import cv2
import logging
import pickle
import json
import copy
from PySide6.QtWidgets import QMessageBox, QCheckBox, QHBoxLayout, QLabel, QColorDialog, QFileDialog
from PySide6.QtGui import QColor, QPixmap

from src.gui.single_segment_dialog import SingleSegmentDialog
from src.gui.multi_segment_dialog import MultiSegmentDialog
from src.widgets.segment_grid_widget import SegmentGridWidget
import matplotlib.pyplot as plt

logger = logging.getLogger(__name__)

from src.workers.segmentation_worker import SegmentationWorker
from src.utils.image_utils import resize_image, convert_cvimage_to_qimage
from src.gui.color_picker_dialog import ColorPickerDialog
from src.gui.custom_palette_dialog import CustomPaletteDialog
from src.gui.palette_management_dialog import PaletteManagementDialog

class SegmentationHandlers:
    """Class for handling segmentation-related functions."""

    def __init__(self):
        # Dictionary to store segmentation states for each image
        self.segmentation_states = {}

        # Dictionary to store segment names
        self.segment_names = {}

        # Dictionary to store custom palettes
        self.custom_palettes = {}

        # Store the currently displayed single segment (if any)
        self.current_single_segment = None

        # Store the original segmented image (for restoring after single segment view)
        self.original_segmented_image = None

        # Store the segment grid container widget
        self.segment_grid_container = None

        # Cache for segment images to avoid redundant processing
        # Format: {image_path: {segment_color: segment_image}}
        self.segment_image_cache = {}

        # Flag to indicate if self.segmented_image stores colors in RGB format
        self.segmented_image_is_rgb = False

        # Path to custom palettes file
        self.custom_palettes_file = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))), 'custom_palettes.json')

        # Load custom palettes if file exists
        self.load_custom_palettes()

        # Connect reload button if it exists
        if hasattr(self, 'reload_button'):
            self.reload_button.clicked.connect(self.reload_previous_results)

        # Connect display segments button if it exists
        if hasattr(self, 'display_multi_segments_button'):
            self.display_multi_segments_button.clicked.connect(self.show_multi_segment_dialog)

        # Store reference to the segment grid container and connect to segment selection signal
        if hasattr(self, 'image_grid_splitter') and self.image_grid_splitter.count() > 1:
            self.segment_grid_container = self.image_grid_splitter.widget(1)

        # Connect to segment grid's signals if it exists
        if hasattr(self, 'segment_grid'):
            self.segment_grid.segment_selected.connect(self.display_single_segment)
            self.segment_grid.save_segment_as_png.connect(self.save_segment_as_png)
            self.segment_grid.export_segment_as_annotations.connect(self.export_segment_as_annotations)

        # Connect the show full segmentation button if it exists
        if hasattr(self, 'show_full_segmentation_button'):
            self.show_full_segmentation_button.clicked.connect(lambda: self.display_single_segment(None))

        # Initialize predefined color palettes
        self.predefined_palettes = {
            'tab20': plt.cm.tab20,
            'viridis': plt.cm.viridis,
            'plasma': plt.cm.plasma,
            'magma': plt.cm.magma,
            'inferno': plt.cm.inferno,
            'cividis': plt.cm.cividis,
            'Pastel1': plt.cm.Pastel1,
            'Set1': plt.cm.Set1,
            'Set2': plt.cm.Set2,
            'Set3': plt.cm.Set3,
            'Paired': plt.cm.Paired
        }

        # Custom color palettes for random generation
        self.color_palettes = {
            'Default': lambda: tuple(np.random.randint(50, 220, 3)),
            'Vibrant': lambda: tuple(np.random.choice([np.random.randint(200, 255), np.random.randint(0, 50)], 3)),
            'Pastel': lambda: tuple(np.random.randint(150, 230, 3)),
            'Grayscale': lambda: tuple([v]*3 for v in [np.random.randint(50, 220)]),
            'High Contrast': lambda: tuple(np.random.choice([255, 0], 3))
        }

    def start_segmentation(self):
        """Starts the image segmentation process."""
        # Use the current image from the gallery if available
        current_image = None

        # First, check if we have a process gallery with a selected image
        if hasattr(self, 'process_gallery') and self.process_gallery.selected_index >= 0:
            selected_index = self.process_gallery.selected_index
            if 0 <= selected_index < len(self.process_gallery.images):
                # Get the image path from the gallery
                current_image_path = self.process_gallery.file_paths[selected_index]
                # Load the image directly from disk to ensure we're using the correct one
                try:
                    current_image = cv2.imread(current_image_path)
                    if current_image is not None:
                        current_image = cv2.cvtColor(current_image, cv2.COLOR_BGR2RGB)
                        print(f"DEBUG: Loaded image for segmentation from gallery: {current_image_path}")
                except Exception as e:
                    print(f"DEBUG: Error loading image from gallery: {e}")

        # If we couldn't get the image from the gallery, fall back to self.image
        if current_image is None:
            if hasattr(self, 'image') and self.image is not None:
                current_image = self.image
                print(f"DEBUG: Using self.image for segmentation")
            else:
                QMessageBox.warning(self, "Warning", "Please upload an image first.")
                return

        self.disable_buttons(training=True)

        params = {
            'train_epoch': self.train_epoch.value(),
            'mod_dim1': self.mod_dim1.value(),
            'mod_dim2': self.mod_dim2.value(),
            'min_label_num': self.min_label_num.value(),
            'max_label_num': self.max_label_num.value(),
            'target_size': (self.target_size_width.value(), self.target_size_height.value()),
            'segmentation_method': self.segmentation_method.currentText(),
            'learning_rate': self.learning_rate_spinbox.value(),
            'use_adaptive_lr': self.adaptive_lr_checkbox.isChecked(),
            'lr_patience': 3,  # Default patience for learning rate reduction
            'lr_factor': 0.5,   # Default factor to reduce learning rate by
            # New optimization parameters
            'momentum': self.momentum_spinbox.value() if hasattr(self, 'momentum_spinbox') else 0.9,
            'use_adaptive_momentum': self.adaptive_momentum_checkbox.isChecked() if hasattr(self, 'adaptive_momentum_checkbox') else False,
            'adaptive_momentum_min': self.adaptive_momentum_min_spinbox.value() if hasattr(self, 'adaptive_momentum_min_spinbox') else 0.1,
            'adaptive_momentum_max': self.adaptive_momentum_max_spinbox.value() if hasattr(self, 'adaptive_momentum_max_spinbox') else 0.99,
            'adaptive_momentum_patience': self.adaptive_momentum_patience_spinbox.value() if hasattr(self, 'adaptive_momentum_patience_spinbox') else 5,
            'weight_decay': self.weight_decay_spinbox.value() if hasattr(self, 'weight_decay_spinbox') else 0.0,
            'optimizer_type': self.optimizer_combo.currentText() if hasattr(self, 'optimizer_combo') else 'SGD',
            'use_gradient_clipping': self.gradient_clipping_checkbox.isChecked() if hasattr(self, 'gradient_clipping_checkbox') else False,
            'gradient_clip_value': self.gradient_clip_value_spinbox.value() if hasattr(self, 'gradient_clip_value_spinbox') else 1.0
        }

        # Save the current state before starting a new segmentation
        # This ensures we don't lose any existing segmentation data for other images
        if hasattr(self, 'current_image_path') and self.current_image_path:
            self.save_segmentation_state()

        self.common_size = (self.target_size_width.value(), self.target_size_height.value())
        current_image_resized = resize_image(current_image, self.common_size)

        # Reset segmented_images for new segmentation on current image only
        # This won't affect cached results for other images
        self.segmented_images = []

        # Reset segment-related state for resegmentation
        self.original_segmented_image = None
        self.current_single_segment = None

        # Hide the segment grid during segmentation
        if hasattr(self, 'segment_grid_container') and self.segment_grid_container:
            print(f"DEBUG: Hiding segment grid during segmentation")
            self.segment_grid_container.hide()

        # Clear the segment image cache for this image
        if hasattr(self, 'segment_image_cache') and hasattr(self, 'current_image_path'):
            if not hasattr(self, 'segment_image_cache'):
                self.segment_image_cache = {}
            if self.current_image_path in self.segment_image_cache:
                print(f"DEBUG: Clearing segment image cache for {self.current_image_path} before segmentation")
                self.segment_image_cache[self.current_image_path] = {}

        self.worker = SegmentationWorker(current_image_resized, params)
        self.worker.progress.connect(self.segmentation_progress)
        self.worker.finished.connect(self.post_segmentation_update)
        self.worker.start()

    def save_segmentation_state(self):
        """Saves the current segmentation state for the current image.

        Simplified to only save the final segmented image and essential settings.
        """
        if hasattr(self, 'current_image_path') and self.current_image_path:
            # Only save if we have a segmented image
            if not hasattr(self, 'segmented_image') or self.segmented_image is None:
                print(f"DEBUG: No segmented image to save for {self.current_image_path}")
                return

            print(f"DEBUG: Saving segmentation state for {self.current_image_path}")

            # Create a deep copy of the current segmented image
            segmented_image_copy = self.segmented_image.copy()

            # Convert numpy arrays to lists for JSON serialization
            new_colors_serializable = {}
            if hasattr(self, 'new_colors'):
                for key, value in self.new_colors.items():
                    # Convert tuple keys to strings
                    str_key = str(key)
                    # Convert tuple values to lists
                    if isinstance(value, tuple):
                        # Convert any numpy types in the tuple to native Python types
                        value = [int(v) if hasattr(v, 'item') else v for v in value]
                    # Convert any other numpy types
                    elif hasattr(value, 'item'):
                        value = value.item()
                    new_colors_serializable[str_key] = value

            # Convert segment_names keys to strings for JSON serialization
            segment_names_serializable = {}
            if hasattr(self, 'segment_names'):
                for key, value in self.segment_names.items():
                    # Convert tuple keys to strings
                    if isinstance(key, tuple):
                        # Convert any numpy types in the tuple to native Python types
                        key_list = [int(k) if hasattr(k, 'item') else k for k in key]
                        str_key = str(tuple(key_list))
                    else:
                        # Handle non-tuple keys
                        str_key = str(key)

                    # Convert value if it's a numpy type
                    if hasattr(value, 'item'):
                        value = value.item()

                    segment_names_serializable[str_key] = value

            # Convert label_percentages keys to strings for JSON serialization
            label_percentages_serializable = {}
            if hasattr(self, 'label_percentages'):
                for key, value in self.label_percentages.items():
                    # Convert tuple keys to strings
                    if isinstance(key, tuple):
                        # Convert any numpy types in the tuple to native Python types
                        key_list = [int(k) if hasattr(k, 'item') else k for k in key]
                        str_key = str(tuple(key_list))
                    else:
                        # Handle non-tuple keys
                        str_key = str(key)

                    # Convert numpy types to Python native types
                    if hasattr(value, 'item'):
                        value = value.item()  # Convert numpy scalar to Python scalar
                    label_percentages_serializable[str_key] = value

            # Store only essential state information
            state = {
                'segmented_image': segmented_image_copy,
                'segmented_image_is_rgb': self.segmented_image_is_rgb, # Save the flag
                'new_colors': new_colors_serializable,
                'segment_names': segment_names_serializable,
                'label_percentages': label_percentages_serializable,
                'custom_palettes': self.convert_numpy_types(self.custom_palettes.copy()) if hasattr(self, 'custom_palettes') else {},
                'timestamp': self.get_current_timestamp()
            }

            # Store the state in memory
            self.segmentation_states[self.current_image_path] = state
            print(f"DEBUG: Segmentation state saved for {self.current_image_path}")

            # Save to disk if project is available
            if hasattr(self, 'project') and self.project:
                try:
                    # Backup creation disabled for performance reasons

                    # Save the segmentation state
                    image_id = os.path.basename(self.current_image_path)
                    self.project.save_unsupervised_segmentation_state(image_id, state)
                    print(f"DEBUG: Segmentation state saved to project for {image_id}")

                    # Save the project file to ensure changes are persisted
                    self.project.save()
                    print(f"DEBUG: Project saved")
                except Exception as e:
                    print(f"DEBUG: Error saving segmentation state to project: {e}")
                    import traceback
                    traceback.print_exc()

    def convert_numpy_types(self, obj):
        """Recursively converts NumPy types to native Python types for JSON serialization.

        Args:
            obj: The object to convert (can be a dict, list, tuple, or scalar)

        Returns:
            The converted object with all NumPy types replaced by native Python types
        """
        if isinstance(obj, dict):
            return {self.convert_key_for_json(k): self.convert_numpy_types(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self.convert_numpy_types(item) for item in obj]
        elif isinstance(obj, tuple):
            return tuple(self.convert_numpy_types(item) for item in obj)
        elif hasattr(obj, 'item'):
            return obj.item()  # Convert NumPy scalar to Python scalar
        else:
            return obj

    def convert_key_for_json(self, key):
        """Converts a dictionary key to a string suitable for JSON serialization.

        Args:
            key: The key to convert

        Returns:
            A string representation of the key
        """
        if isinstance(key, tuple):
            # Convert tuple elements to native Python types
            key_list = [int(k) if hasattr(k, 'item') else k for k in key]
            return str(tuple(key_list))
        elif hasattr(key, 'item'):
            return str(key.item())
        else:
            return str(key)

    def normalize_color_tuple(self, color):
        """Normalizes a color tuple to ensure consistent format.

        Args:
            color: A color tuple or other format

        Returns:
            A normalized color tuple
        """
        if isinstance(color, tuple) and len(color) == 3:
            # Ensure all elements are integers
            return tuple(int(c) if hasattr(c, 'item') else c for c in color)
        return color

    def get_current_timestamp(self):
        """Returns the current timestamp as a string."""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    def load_segmentation_state(self, show_message=True):
        """Loads the segmentation state for the current image.

        Args:
            show_message: Whether to show a message box when state is loaded. Default is True.

        Returns:
            True if state was loaded successfully, False otherwise.
        """
        import time
        start_time = time.time()
        print(f"DEBUG: load_segmentation_state called for {self.current_image_path if hasattr(self, 'current_image_path') else 'unknown path'}")

        # First try to load from memory
        state = None
        if hasattr(self, 'current_image_path') and self.current_image_path in self.segmentation_states:
            print(f"DEBUG: Loading segmentation state from memory for {self.current_image_path}")
            state = self.segmentation_states[self.current_image_path]
        # If not in memory, try to load from project
        elif hasattr(self, 'project') and self.project and hasattr(self, 'current_image_path'):
            try:
                image_id = os.path.basename(self.current_image_path)
                print(f"DEBUG: Attempting to load segmentation state from project for {image_id}")
                state = self.project.load_image_processing_state(image_id)
                if state:
                    print(f"DEBUG: Successfully loaded state from project for {image_id}")
                    # Store in memory for future use
                    self.segmentation_states[self.current_image_path] = state
            except Exception as e:
                print(f"DEBUG: Error loading segmentation state from project: {e}")
                import traceback
                traceback.print_exc()

        if state and 'segmented_image' in state and state['segmented_image'] is not None:
            print(f"DEBUG: Restoring segmentation state")

            # Create a deep copy of the segmented image
            self.segmented_image = state['segmented_image'].copy()
            self.segmented_image_is_rgb = state.get('segmented_image_is_rgb', True) # Load flag, default True for old states
            print(f"DEBUG: Restored segmented image with shape {self.segmented_image.shape}, is_rgb: {self.segmented_image_is_rgb}")

            # Initialize segmented_images with just the segmented image
            # This is needed for the epoch slider to work
            self.segmented_images = [self.segmented_image.copy()]

            # Restore color settings
            if 'new_colors' in state and state['new_colors'] is not None:
                # Convert serialized new_colors back to the original format
                new_colors = {}
                for key_str, value in state['new_colors'].items():
                    # Try to convert string keys back to tuples
                    try:
                        # Check if key_str is already a tuple (from older versions)
                        if isinstance(key_str, tuple):
                            key = key_str
                        else:
                            # Remove parentheses and split by comma
                            key_str_clean = key_str.strip('()')
                            if ',' in key_str_clean:
                                # It's a tuple representation
                                key_parts = key_str_clean.split(',')
                                key = tuple(int(part.strip()) for part in key_parts)
                            else:
                                # It might be a single integer
                                key = int(key_str_clean)
                    except (ValueError, AttributeError):
                        # If conversion fails, use the string as is
                        key = key_str

                    # Convert list values back to tuples if needed
                    if isinstance(value, list) and len(value) == 3:
                        value = tuple(value)

                    new_colors[key] = value

                self.new_colors = new_colors
                print(f"DEBUG: Restored color mappings")

            # Restore segment names
            if 'segment_names' in state and state['segment_names'] is not None:
                # Convert serialized segment_names back to the original format
                segment_names = {}
                for key_str, value in state['segment_names'].items():
                    # Try to convert string keys back to tuples
                    try:
                        # Check if key_str is already a tuple (from older versions)
                        if isinstance(key_str, tuple):
                            key = key_str
                        else:
                            # Remove parentheses and split by comma
                            key_str_clean = key_str.strip('()')
                            if ',' in key_str_clean:
                                # It's a tuple representation
                                key_parts = key_str_clean.split(',')
                                key = tuple(int(part.strip()) for part in key_parts)
                            else:
                                # It might be a single integer
                                key = int(key_str_clean)
                    except (ValueError, AttributeError):
                        # If conversion fails, use the string as is
                        key = key_str

                    segment_names[key] = value

                self.segment_names = segment_names
                print(f"DEBUG: Restored segment names")
            else:
                # Initialize segment names if they don't exist
                self.segment_names = {}
                # Get unique colors in the segmented image
                unique_colors = set()
                for y in range(self.segmented_image.shape[0]):
                    for x in range(self.segmented_image.shape[1]):
                        unique_colors.add(tuple(self.segmented_image[y, x]))

                # Add default segment names
                for i, color in enumerate(unique_colors):
                    if color not in self.segment_names:
                        self.segment_names[color] = f"Segment {i+1}"
                print(f"DEBUG: Initialized default segment names")

            # Restore label percentages
            if 'label_percentages' in state and state['label_percentages'] is not None:
                # Convert serialized label_percentages back to the original format
                label_percentages = {}
                for key_str, value in state['label_percentages'].items():
                    # Try to convert string keys back to tuples
                    try:
                        # Check if key_str is already a tuple (from older versions)
                        if isinstance(key_str, tuple):
                            key = key_str
                        else:
                            # Remove parentheses and split by comma
                            key_str_clean = key_str.strip('()')
                            if ',' in key_str_clean:
                                # It's a tuple representation
                                key_parts = key_str_clean.split(',')
                                key = tuple(int(part.strip()) for part in key_parts)
                            else:
                                # It might be a single integer
                                key = int(key_str_clean)
                    except (ValueError, AttributeError):
                        # If conversion fails, use the string as is
                        key = key_str

                    label_percentages[key] = value

                self.label_percentages = label_percentages
                print(f"DEBUG: Restored label percentages")
            else:
                # Calculate if not in state
                self.calculate_label_percentages()

            # Restore custom palettes
            if 'custom_palettes' in state and state['custom_palettes'] is not None:
                # Merge with existing palettes, giving priority to the loaded ones
                loaded_palettes = state['custom_palettes'].copy()
                for name, palette in loaded_palettes.items():
                    self.custom_palettes[name] = palette
                print(f"DEBUG: Restored {len(loaded_palettes)} custom palettes")
                # Update the palette combo box if it exists
                if hasattr(self, 'update_palette_combo'):
                    self.update_palette_combo()

            # Update the UI with the segmented image
            print(f"DEBUG: Displaying segmented image with unique values: {np.unique(self.segmented_image)}")
            self.display_segmented_image(self.segmented_image)

            # Update color correspondence and merge listbox
            self.update_color_correspondence()
            self.update_merge_listbox()

            # Update the epoch slider (simplified to just 1 epoch)
            self.epoch_slider.setEnabled(True)
            self.epoch_slider.setMaximum(1)
            self.epoch_slider.setValue(1)
            self.epoch_label.setText("Epoch: 1")

            # Enable the reload button if it exists
            if hasattr(self, 'reload_button'):
                self.reload_button.setEnabled(True)

            # Automatically display top segments in grid
            self.display_multiple_segments()

            # Show a message to the user if requested
            if show_message:
                timestamp = state.get('timestamp', 'unknown time')
                QMessageBox.information(self, "Previous Results Loaded", f"Previous segmentation results from {timestamp} have been loaded.")

            end_time = time.time()
            print(f"DEBUG: load_segmentation_state completed in {end_time - start_time:.2f} seconds")
            return True
        else:
            end_time = time.time()
            print(f"DEBUG: No segmentation state found for {self.current_image_path if hasattr(self, 'current_image_path') else 'unknown path'}")
            print(f"DEBUG: load_segmentation_state completed in {end_time - start_time:.2f} seconds")
            # Reset segmentation-related attributes if no state exists
            self.segmented_images = []
            self.segmented_image = None
            self.label_percentages = {}
            self.segmented_image_is_rgb = False # Reset if no image/state

            # Clear the segmented image view
            if hasattr(self, 'segmented_image_view'):
                self.segmented_image_view.clear()

            # Reset the epoch slider
            self.epoch_slider.setEnabled(False)
            self.epoch_slider.setValue(0)
            self.epoch_label.setText("Epoch: 0")

            # Clear the merge listbox
            self.update_merge_listbox()

            # Disable the reload button if it exists
            if hasattr(self, 'reload_button'):
                self.reload_button.setEnabled(False)

            # Hide the segment grid if no segmentation data is available
            if hasattr(self, 'segment_grid_container') and self.segment_grid_container.isVisible():
                self.segment_grid_container.hide()

            return False

    def fast_load_segmentation_state(self):
        """A faster version of load_segmentation_state that skips UI updates until the end.
        This is optimized for switching between images.
        """
        import time
        start_time = time.time()
        print(f"DEBUG: fast_load_segmentation_state called for {self.current_image_path}")

        # First try to load from memory
        state = None
        if hasattr(self, 'current_image_path') and self.current_image_path in self.segmentation_states:
            print(f"DEBUG: Loading segmentation state from memory for {self.current_image_path}")
            state = self.segmentation_states[self.current_image_path]
        # If not in memory, try to load from project
        elif hasattr(self, 'project') and self.project:
            try:
                image_id = os.path.basename(self.current_image_path)
                print(f"DEBUG: Attempting to load segmentation state from project for {image_id}")
                state = self.project.load_image_processing_state(image_id)
                if state:
                    print(f"DEBUG: Successfully loaded state from project for {image_id}")
                    # Store in memory for future use
                    self.segmentation_states[self.current_image_path] = state
            except Exception as e:
                print(f"DEBUG: Error loading segmentation state from project: {e}")
                import traceback
                traceback.print_exc()

        if state and 'segmented_image' in state and state['segmented_image'] is not None:
            # Create a deep copy of the segmented image
            self.segmented_image = state['segmented_image'].copy()
            self.segmented_image_is_rgb = state.get('segmented_image_is_rgb', True) # Load flag, default True
            print(f"DEBUG: Restored segmented image in fast_load, is_rgb: {self.segmented_image_is_rgb}")

            # Reset the original segmented image and current single segment
            self.original_segmented_image = None
            self.current_single_segment = None

            # Initialize segmented_images with just the segmented image
            self.segmented_images = [self.segmented_image.copy()]

            # Restore color settings, segment names, and label percentages with deep copies
            # to avoid cross-contamination between images
            if 'new_colors' in state and state['new_colors'] is not None:
                self.new_colors = copy.deepcopy(state['new_colors'])

            if 'segment_names' in state and state['segment_names'] is not None:
                # Convert serialized segment_names back to the original format
                segment_names = {}
                for key_str, value in state['segment_names'].items():
                    # Try to convert string keys back to tuples
                    try:
                        # Check if key_str is already a tuple (from older versions)
                        if isinstance(key_str, tuple):
                            key = key_str
                        else:
                            # Remove parentheses and split by comma
                            key_str_clean = key_str.strip('()')
                            if ',' in key_str_clean:
                                # It's a tuple representation
                                key_parts = key_str_clean.split(',')
                                key = tuple(int(part.strip()) for part in key_parts)
                            else:
                                # It might be a single integer
                                key = int(key_str_clean)
                    except (ValueError, AttributeError):
                        # If conversion fails, use the string as is
                        key = key_str

                    segment_names[key] = value

                self.segment_names = segment_names
                print(f"DEBUG: Restored segment names in fast_load: {len(segment_names)} names")
            else:
                # Initialize segment names if they don't exist
                self.segment_names = {}
                # Get unique colors in the segmented image
                unique_colors = set()
                for y in range(self.segmented_image.shape[0]):
                    for x in range(self.segmented_image.shape[1]):
                        unique_colors.add(tuple(self.segmented_image[y, x]))

                # Add default segment names
                for i, color in enumerate(unique_colors):
                    if color not in self.segment_names:
                        self.segment_names[color] = f"Segment {i+1}"

            if 'label_percentages' in state and state['label_percentages'] is not None:
                self.label_percentages = copy.deepcopy(state['label_percentages'])
            else:
                # Calculate percentages but don't update UI
                self.calculate_label_percentages(update_ui=False)

            if 'custom_palettes' in state and state['custom_palettes'] is not None:
                self.custom_palettes = copy.deepcopy(state['custom_palettes'])

            # Display the segmented image
            self.display_segmented_image(self.segmented_image)
            
            # Ensure original image matches segmented image dimensions for proper synchronization
            if hasattr(self, 'image') and self.image is not None:
                # Get the segmented image dimensions
                seg_height, seg_width = self.segmented_image.shape[:2]
                
                # Resize the original image to match segmented image dimensions
                from src.utils.image_utils import resize_image
                self.image_resized = resize_image(self.image, (seg_width, seg_height))
                self.display_original_image(self.image_resized)
                print(f"DEBUG: Resized original image to match segmented image dimensions: {seg_width}x{seg_height}")

            # Update the epoch slider
            self.epoch_slider.setEnabled(True)
            self.epoch_slider.setMaximum(1)
            self.epoch_slider.setValue(1)
            self.epoch_label.setText("Epoch: 1")

            # Enable the reload button if it exists
            if hasattr(self, 'reload_button'):
                self.reload_button.setEnabled(True)

            end_time = time.time()
            print(f"DEBUG: fast_load_segmentation_state completed in {end_time - start_time:.2f} seconds")
            return True
        else:
            end_time = time.time()
            print(f"DEBUG: No segmentation state found in fast_load_segmentation_state")
            print(f"DEBUG: fast_load_segmentation_state completed in {end_time - start_time:.2f} seconds")
            return False

    def reload_previous_results(self):
        """Reloads previous segmentation results for the current image."""
        print(f"DEBUG: reload_previous_results called for {self.current_image_path if hasattr(self, 'current_image_path') else 'unknown path'}")

        # Check if we have a current image path
        if not hasattr(self, 'current_image_path') or not self.current_image_path:
            QMessageBox.warning(self, "Warning", "No image loaded. Please load an image first.")
            return

        # Try to load the state
        success = self.load_segmentation_state()

        if not success:
            QMessageBox.warning(self, "Warning", "No previous results found for this image.")

    def switch_image(self, image_path):
        """Handles switching between images."""
        print(f"DEBUG: switch_image called with {image_path}")
        # Always save the current state before switching, even if it's empty
        if hasattr(self, 'current_image_path') and self.current_image_path:
            # Ensure we have the necessary attributes before saving
            if not hasattr(self, 'segmented_images'):
                self.segmented_images = []
            if not hasattr(self, 'segmented_image'):
                self.segmented_image = None
            if not hasattr(self, 'common_size'):
                self.common_size = None
            if not hasattr(self, 'new_colors'):
                self.new_colors = {}

            # Save the current state
            self.save_segmentation_state()

        # Update the current image path
        self.current_image_path = image_path

        # Initialize color-related attributes if they don't exist
        if not hasattr(self, 'new_colors'):
            self.new_colors = {}

        # Manage segment image cache to prevent excessive memory usage
        # Keep only the last 5 images in the cache
        if hasattr(self, 'segment_image_cache') and len(self.segment_image_cache) > 5:
            # Get the oldest entries (excluding the current image)
            cache_keys = list(self.segment_image_cache.keys())
            if self.current_image_path in cache_keys:
                cache_keys.remove(self.current_image_path)

            # Remove the oldest entries until we have 4 or fewer (plus current image = 5 max)
            while len(cache_keys) > 4:
                oldest_key = cache_keys.pop(0)
                del self.segment_image_cache[oldest_key]

        # Reset segmentation-related attributes for the new image
        # This ensures each image has its own independent state
        self.segmented_images = []
        self.segmented_image = None
        self.segmented_image_is_rgb = False # Reset flag
        self.original_segmented_image = None  # Reset the original segmented image
        self.current_single_segment = None    # Reset the current single segment
        self.label_percentages = {}

        # Make sure the original image is displayed
        if hasattr(self, 'image') and self.image is not None and hasattr(self, 'common_size'):
            self.image_resized = resize_image(self.image, self.common_size)
            self.display_original_image(self.image_resized)
            print(f"DEBUG: Displayed original image before loading state")

        # Clear the segmented image view
        if hasattr(self, 'segmented_image_view'):
            self.segmented_image_view.clear()

        # Also clear the synchronized segmented view
        if hasattr(self, 'segmented_sync_view'):
            self.segmented_sync_view.set_pixmap(QPixmap())  # Clear with empty pixmap

        # Clear cached pixmaps used for synchronization
        if hasattr(self, '_segmented_pixmap'):
            self._segmented_pixmap = None

        # Reset segmented image variables to ensure clean state
        self.segmented_image = None
        self.segmented_images = []

        # Reset the epoch slider
        self.epoch_slider.setEnabled(False)
        self.epoch_slider.setValue(0)
        self.epoch_label.setText("Epoch: 0")

        # Clear the merge listbox
        self.update_merge_listbox()

        # Hide the segment grid when switching to a new image
        # It will be shown again only if segmentation data is available
        if hasattr(self, 'segment_grid_container') and self.segment_grid_container:
            print(f"DEBUG: Hiding segment grid when switching to {image_path}")
            self.segment_grid_container.hide()

        # Reset segment grid related variables
        self.original_segmented_image = None
        self.current_single_segment = None

        # Check if we need to load state from disk
        has_state_in_memory = hasattr(self, 'segmentation_states') and self.current_image_path in self.segmentation_states
        has_state_in_project = False

        if not has_state_in_memory and hasattr(self, 'project') and self.project and hasattr(self, 'current_image_path'):
            # Check if state exists in project without loading it
            image_id = os.path.basename(self.current_image_path)
            has_state_in_project = hasattr(self.project, 'has_unsupervised_segmentation_state') and \
                                  self.project.has_unsupervised_segmentation_state(image_id)

        # Only load state if it exists (avoids unnecessary disk operations)
        if has_state_in_memory or has_state_in_project:
            # Use a more direct and faster state loading approach
            self.fast_load_segmentation_state()

            # Display top segments in grid after loading state
            if hasattr(self, 'segmented_image') and self.segmented_image is not None:
                print(f"DEBUG: Displaying top segments in grid after loading state for {self.current_image_path}")
                # Ensure label percentages are calculated before displaying segments
                if not hasattr(self, 'label_percentages') or not self.label_percentages:
                    self.calculate_label_percentages(update_ui=False)
                self.display_multiple_segments()
        else:
            print(f"DEBUG: No state to load for {self.current_image_path}")

            # Hide the segment grid if no segmentation data is available
            if hasattr(self, 'segment_grid_container') and self.segment_grid_container.isVisible():
                self.segment_grid_container.hide()

        # Check if we have previous results and enable/disable the reload button accordingly
        if hasattr(self, 'reload_button'):
            has_previous_results = hasattr(self, 'current_image_path') and (
                (hasattr(self, 'segmentation_states') and self.current_image_path in self.segmentation_states) or
                (hasattr(self, 'project') and self.project and
                 hasattr(self.project, 'has_image_processing_state') and
                 self.project.has_image_processing_state(os.path.basename(self.current_image_path)))
            )
            self.reload_button.setEnabled(has_previous_results)

            # Update the button text to indicate if results are available
            if has_previous_results:
                self.reload_button.setText("Reload Previous Results (Available)")
            else:
                self.reload_button.setText("Reload Previous Results")

    def stop_training(self):
        """Stops the segmentation training process."""
        if self.worker:
            self.worker.stop()

    def segmentation_progress(self, epoch, image):
        """Updates the progress bar and displays intermediate results."""
        total_epochs = self.train_epoch.value()
        progress_percentage = (epoch / total_epochs) * 100
        self.progress.setValue(int(progress_percentage))
        self.segmented_images.append(image.copy())
        # Only display the image without calculating percentages during progress
        self.display_segmented_image(image)

    def get_unique_segment_colors(self):
        """Gets the unique segment colors from the segmented image.

        Returns:
            dict: Dictionary mapping segment colors to themselves
        """
        if not hasattr(self, 'segmented_image') or self.segmented_image is None:
            return {}

        # Get unique colors from the segmented image
        unique_colors = set()
        for y in range(self.segmented_image.shape[0]):
            for x in range(self.segmented_image.shape[1]):
                unique_colors.add(tuple(self.segmented_image[y, x]))

        # Create a dictionary of segment colors
        segment_colors = {}
        for color in unique_colors:
            segment_colors[color] = color

        return segment_colors

    def show_single_segment_dialog(self):
        """Shows the dialog for selecting a single segment to display."""
        if not hasattr(self, 'segmented_image') or self.segmented_image is None:
            QMessageBox.warning(self, "Warning", "No segmented image available. Please segment an image first.")
            return

        # Get unique segment colors
        segment_colors = self.get_unique_segment_colors()

        # Create and show the dialog
        dialog = SingleSegmentDialog(segment_colors, self.segment_names, self)
        dialog.segment_selected.connect(self.display_single_segment)
        dialog.exec()

    def show_multi_segment_dialog(self):
        """Shows the dialog for selecting multiple segments to display in a grid."""
        if not hasattr(self, 'segmented_image') or self.segmented_image is None:
            QMessageBox.warning(self, "Warning", "No segmented image available. Please segment an image first.")
            return

        # Get unique segment colors
        segment_colors = self.get_unique_segment_colors()

        # Create and show the dialog
        dialog = MultiSegmentDialog(segment_colors, self.segment_names, self)
        dialog.segments_selected.connect(self.display_multiple_segments)
        dialog.segments_selected_for_preview.connect(self.display_combined_segments_in_preview)
        dialog.exec()

    def create_segment_image(self, selected_color):
        """Creates an image with only the selected segment using vectorized operations.

        Args:
            selected_color: The color of the segment to display

        Returns:
            numpy.ndarray: Image with only the selected segment
        """
        if not hasattr(self, 'segmented_image') or self.segmented_image is None:
            print(f"DEBUG: create_segment_image - No segmented image available")
            return None

        try:
            # Verify the color is valid (tuple of 3 integers)
            if not isinstance(selected_color, tuple) or len(selected_color) != 3:
                print(f"DEBUG: create_segment_image - Invalid color format: {selected_color}")
                return None

            # Check if we have this segment in the cache
            if hasattr(self, 'current_image_path') and self.current_image_path:
                # Initialize cache for this image if it doesn't exist
                if not hasattr(self, 'segment_image_cache'):
                    self.segment_image_cache = {}

                if self.current_image_path not in self.segment_image_cache:
                    self.segment_image_cache[self.current_image_path] = {}

                # Return cached image if available
                if selected_color in self.segment_image_cache[self.current_image_path]:
                    return self.segment_image_cache[self.current_image_path][selected_color]

            # Create a new image with black background
            single_segment_image = np.zeros_like(self.segmented_image)

            # Convert selected_color to numpy array for comparison
            color_array = np.array(selected_color)

            # Create a mask for the selected color (True where colors match)
            # This is much faster than pixel-by-pixel comparison
            mask = np.all(self.segmented_image == color_array, axis=2)

            # If the mask is empty, the segment doesn't exist
            if not np.any(mask):
                print(f"DEBUG: create_segment_image - Empty mask for color {selected_color} (segment may have been merged)")
                return None

            # Copy only the selected segment using the mask
            # This is a vectorized operation that's much faster than loops
            single_segment_image[mask] = self.segmented_image[mask]

            # Cache the result
            if hasattr(self, 'current_image_path') and self.current_image_path:
                if hasattr(self, 'segment_image_cache') and self.current_image_path in self.segment_image_cache:
                    self.segment_image_cache[self.current_image_path][selected_color] = single_segment_image

            return single_segment_image

        except Exception as e:
            print(f"DEBUG: create_segment_image - Error processing segment for color {selected_color}: {e}")
            return None

    def display_single_segment(self, selected_color):
        """Displays only the selected segment in the segmented image.

        Args:
            selected_color: The color of the segment to display, or None to show all segments
        """
        if not hasattr(self, 'segmented_image') or self.segmented_image is None:
            return

        # If selected_color is None, show all segments (reset)
        if selected_color is None:
            # Check if we have an original segmented image
            if self.original_segmented_image is not None:
                # Use the original segmented image
                self.segmented_image = self.original_segmented_image.copy()
                self.current_single_segment = None
                self.display_segmented_image(self.segmented_image)
                self.calculate_label_percentages(update_ui=True)

                # Update status message
                if hasattr(self, 'statusBar'):
                    self.statusBar().showMessage("Showing full segmentation", 3000)
            elif hasattr(self, 'segmented_images') and self.segmented_images:
                # If no original image but we have segmented images, use the last one
                self.segmented_image = self.segmented_images[-1].copy()
                self.current_single_segment = None
                self.display_segmented_image(self.segmented_image)
                self.calculate_label_percentages(update_ui=True)

                # Update status message
                if hasattr(self, 'statusBar'):
                    self.statusBar().showMessage("Showing full segmentation", 3000)
            return

        # Store the original segmented image if not already stored
        if self.original_segmented_image is None or self.current_single_segment is None:
            self.original_segmented_image = self.segmented_image.copy()

        # Create a new image with only the selected segment
        single_segment_image = self.create_segment_image(selected_color)
        if single_segment_image is None:
            return

        # Update the current single segment
        self.current_single_segment = selected_color

        # Display the single segment image
        self.display_segmented_image(single_segment_image)

        # Update the status message
        segment_name = self.segment_names.get(selected_color, "Unknown Segment")
        if hasattr(self, 'statusBar'):
            self.statusBar().showMessage(f"Displaying segment: {segment_name}", 3000)

    def display_multiple_segments(self, selected_colors=None):
        """Displays multiple segments in a grid layout.

        Args:
            selected_colors: List of colors of the segments to display. If None, displays top 7 segments by percentage.
        """
        print(f"DEBUG: display_multiple_segments called with selected_colors={selected_colors}")

        # Check if we have a segmented image
        if not hasattr(self, 'segmented_image') or self.segmented_image is None:
            print("DEBUG: No segmented image available, hiding segment grid")
            if hasattr(self, 'segment_grid_container') and self.segment_grid_container:
                self.segment_grid_container.hide()
            return

        # Make sure we have label percentages calculated
        if not hasattr(self, 'label_percentages') or not self.label_percentages:
            print("DEBUG: No label percentages available, calculating now")
            self.calculate_label_percentages(update_ui=False)

        # If still no label percentages, there might be no segments in the image
        if not hasattr(self, 'label_percentages') or not self.label_percentages:
            print("DEBUG: No segments found in the image, hiding segment grid")
            if hasattr(self, 'segment_grid_container') and self.segment_grid_container:
                self.segment_grid_container.hide()
            return

        # If no specific colors provided, use top 7 segments by percentage
        if not selected_colors:
            try:
                # Sort segments by percentage (descending)
                sorted_segments = sorted(self.label_percentages.items(), key=lambda x: x[1], reverse=True)
                # Take top 7 or all if less than 7
                selected_colors = [color for color, _ in sorted_segments[:7]]
                print(f"DEBUG: Selected top {len(selected_colors)} segments by percentage")
            except Exception as e:
                print(f"DEBUG: Error selecting top segments: {e}")
                # Fallback: get unique colors from the segmented image
                selected_colors = self.get_unique_segment_colors()
                print(f"DEBUG: Fallback: selected {len(selected_colors)} unique colors from image")

        # If still no segments to display, hide the grid and return
        if not selected_colors:
            print("DEBUG: No segments to display, hiding segment grid")
            if hasattr(self, 'segment_grid_container') and self.segment_grid_container:
                self.segment_grid_container.hide()
            return

        # Store the original segmented image if not already stored
        if self.original_segmented_image is None:
            self.original_segmented_image = self.segmented_image.copy()

        # Make sure we're showing the full segmentation in the main view
        if self.current_single_segment is not None:
            self.segmented_image = self.original_segmented_image.copy()
            self.current_single_segment = None
            self.display_segmented_image(self.segmented_image)
            self.calculate_label_percentages(update_ui=True)

        # Create segment images and convert to QPixmap
        segment_pixmaps = []
        segment_names = []
        segment_colors = []
        segment_percentages = []

        for color in selected_colors:
            # Create segment image
            segment_image = self.create_segment_image(color)
            if segment_image is None:
                continue

            # Convert to QPixmap
            q_img = convert_cvimage_to_qimage(segment_image, already_rgb=self.segmented_image_is_rgb)
            pixmap = QPixmap.fromImage(q_img)
            segment_pixmaps.append(pixmap)

            # Normalize the color tuple
            normalized_color = self.normalize_color_tuple(color)

            # Get segment name
            segment_name = self.segment_names.get(normalized_color, None)

            # If not found with normalized color, try with the original color
            if segment_name is None:
                segment_name = self.segment_names.get(color, None)

            # If still not found, try to find a matching color by RGB values
            if segment_name is None:
                for key, name in self.segment_names.items():
                    if isinstance(key, tuple) and len(key) == 3 and isinstance(color, tuple) and len(color) == 3:
                        if all(abs(k - c) < 2 for k, c in zip(key, color)):  # Allow small differences
                            segment_name = name
                            break

            # If still not found, use default name
            if segment_name is None:
                segment_name = f"Segment {len(segment_names) + 1}"
                # Add it to the segment_names dictionary for future use
                self.segment_names[normalized_color] = segment_name

            segment_names.append(segment_name)

            # Store the original color
            segment_colors.append(color)

            # Get segment percentage if available
            percentage = self.label_percentages.get(color, None)
            segment_percentages.append(percentage)

        # Update the segment grid
        if hasattr(self, 'segment_grid'):
            self.segment_grid.set_segments(segment_pixmaps, segment_names, segment_colors, segment_percentages)

        # Show the segment grid container and make sure it's visible
        if self.segment_grid_container:
            self.segment_grid_container.show()

            # Make sure the splitter gives enough space to the grid
            if hasattr(self, 'image_grid_splitter'):
                sizes = self.image_grid_splitter.sizes()
                if len(sizes) >= 2 and sizes[1] < 200:  # If grid has less than 200 pixels height
                    # Set to 70% for image view, 30% for grid
                    total_height = sum(sizes)
                    self.image_grid_splitter.setSizes([int(total_height * 0.7), int(total_height * 0.3)])

    def display_combined_segments_in_preview(self, selected_colors):
        """Displays multiple selected segments combined in the main preview.
        
        Args:
            selected_colors: List of colors of the segments to combine and display
        """
        print(f"DEBUG: display_combined_segments_in_preview called with {len(selected_colors)} segments")
        
        # Check if we have a segmented image
        if not hasattr(self, 'segmented_image') or self.segmented_image is None:
            print("DEBUG: No segmented image available")
            return
            
        # If no colors selected, show full segmentation
        if not selected_colors:
            print("DEBUG: No segments selected, showing full segmentation")
            self.display_segmented_image(self.segmented_image)
            return
            
        # Store the original segmented image if not already stored
        if self.original_segmented_image is None:
            self.original_segmented_image = self.segmented_image.copy()
            
        try:
            # Create a combined image with black background
            combined_image = np.zeros_like(self.segmented_image)
            
            # Add each selected segment to the combined image
            for color in selected_colors:
                # Verify the color is valid
                if not isinstance(color, tuple) or len(color) != 3:
                    print(f"DEBUG: Skipping invalid color format: {color}")
                    continue
                    
                # Convert color to numpy array for comparison
                color_array = np.array(color)
                
                # Create mask for this segment
                mask = np.all(self.segmented_image == color_array, axis=2)
                
                # If the mask is not empty, add this segment to the combined image
                if np.any(mask):
                    combined_image[mask] = self.segmented_image[mask]
                    print(f"DEBUG: Added segment with color {color} to combined image")
                else:
                    print(f"DEBUG: No pixels found for segment color {color}")
            
            # Update the current segmented image and display it
            self.segmented_image = combined_image
            self.current_single_segment = None  # Clear single segment state
            self.display_segmented_image(combined_image)
            
            # Update status message
            segment_names = []
            for color in selected_colors:
                segment_name = self.segment_names.get(color, f"Segment {color}")
                segment_names.append(segment_name)
            
            status_message = f"Displaying combined segments: {', '.join(segment_names)}"
            if hasattr(self, 'statusBar'):
                self.statusBar().showMessage(status_message, 5000)
            
            print(f"DEBUG: Successfully displayed {len(selected_colors)} combined segments")
            
        except Exception as e:
            print(f"DEBUG: Error creating combined segments image: {e}")
            # Fallback to showing the original segmented image
            self.display_segmented_image(self.original_segmented_image or self.segmented_image)

    def post_segmentation_update(self, segmented_images):
        """Updates the UI after segmentation is complete."""
        print("DEBUG: post_segmentation_update called")
        if segmented_images:
            # Reset segment-related state for resegmentation
            self.original_segmented_image = None
            self.current_single_segment = None

            # Clear the segment image cache for this image
            if hasattr(self, 'segment_image_cache') and hasattr(self, 'current_image_path'):
                if not hasattr(self, 'segment_image_cache'):
                    self.segment_image_cache = {}
                if self.current_image_path in self.segment_image_cache:
                    print(f"DEBUG: Clearing segment image cache for {self.current_image_path} after resegmentation")
                    self.segment_image_cache[self.current_image_path] = {}

            # Reset label percentages to force recalculation
            if hasattr(self, 'label_percentages'):
                print("DEBUG: Resetting label percentages after resegmentation")
                self.label_percentages = {}

            # Update the segmented image
            self.segmented_image = segmented_images[-1]
            self.segmented_image_is_rgb = True # Assuming worker output is RGB
            print(f"DEBUG: New segmented image shape: {self.segmented_image.shape}, is_rgb: {self.segmented_image_is_rgb}")

            # Ensure segmented image matches the common_size to maintain consistency
            if hasattr(self, 'common_size') and self.common_size:
                expected_height, expected_width = self.common_size[1], self.common_size[0]
                actual_height, actual_width = self.segmented_image.shape[:2]
                
                # Only resize if dimensions don't match
                if actual_height != expected_height or actual_width != expected_width:
                    print(f"DEBUG: Resizing segmented image from {(actual_width, actual_height)} to {self.common_size}")
                    self.segmented_image = resize_image(self.segmented_image, self.common_size)
                    if self.segmented_image is None:
                        print("ERROR: Failed to resize segmented image")
                        return
            
            # Update original image to match if needed
            if hasattr(self, 'image') and self.image is not None:
                if not hasattr(self, 'common_size') or not self.common_size:
                    self.common_size = (self.segmented_image.shape[1], self.segmented_image.shape[0])
                
                # Ensure original image is resized to match common_size
                self.image_resized = resize_image(self.image, self.common_size)
                if self.image_resized is not None:
                    self.display_original_image(self.image_resized)

            # Initialize new_colors if it doesn't exist
            if not hasattr(self, 'new_colors'):
                self.new_colors = {}

            # Reset segment names for the new segmentation
            self.segment_names = {}

            # Get unique colors in the segmented image
            unique_colors = set()
            for y in range(self.segmented_image.shape[0]):
                for x in range(self.segmented_image.shape[1]):
                    unique_colors.add(tuple(self.segmented_image[y, x]))
            print(f"DEBUG: Found {len(unique_colors)} unique colors in new segmentation")

            # Add default segment names if they don't exist
            for i, color in enumerate(unique_colors):
                if color not in self.segment_names:
                    self.segment_names[color] = f"Segment {i+1}"

            # Ensure segmented image is properly resized before display
            if hasattr(self, 'common_size') and self.common_size:
                segmented_image_for_display = resize_image(self.segmented_image, self.common_size)
                if segmented_image_for_display is not None:
                    self.display_segmented_image(segmented_image_for_display)
                else:
                    print("ERROR: Failed to resize segmented image for display")
                    self.display_segmented_image(self.segmented_image)
            else:
                self.display_segmented_image(self.segmented_image)

            # Calculate percentages for the new segmentation
            print("DEBUG: Calculating label percentages for new segmentation")
            self.calculate_label_percentages(update_ui=True)
            self.update_color_correspondence()
            # update_merge_listbox will use the already calculated percentages
            self.update_merge_listbox()

            self.epoch_slider.setEnabled(True)
            self.epoch_slider.setMaximum(len(self.segmented_images))
            self.epoch_slider.setValue(len(self.segmented_images))
            self.epoch_label.setText(f"Epoch: {len(self.segmented_images)}")

            # Save the state after segmentation is complete
            self.save_segmentation_state()

            # Automatically display top segments in grid with the new segmentation
            print("DEBUG: Displaying top segments from new segmentation")
            self.display_multiple_segments()

        self.progress.setValue(0)
        self.enable_buttons(training=False)
        if self.worker and self.worker.stop_event.is_set():
              QMessageBox.information(self, "Training Stopped", "Training was stopped by the user.")

    def update_segmented_image(self, value):
        """Updates the displayed segmented image based on the epoch slider value."""
        if value > 0 and value <= len(self.segmented_images):
            self.segmented_image = self.segmented_images[value - 1]
            self.display_segmented_image(self.segmented_image)
            self.epoch_label.setText(f"Epoch: {value}")
            # Calculate percentages but don't update UI yet (will be done in update_merge_listbox)
            self.calculate_label_percentages(update_ui=True)
            self.update_color_correspondence()
            # update_merge_listbox will use the already calculated percentages
            self.update_merge_listbox()

    def calculate_label_percentages(self, update_ui=True):
        """Calculates and displays the percentage of each label in the segmented image.

        Args:
            update_ui: Whether to update the UI with the calculated percentages
        """
        if self.segmented_image is None:
            return

        # Check if we already have calculated percentages and UI update is not needed
        if hasattr(self, 'label_percentages') and self.label_percentages and not update_ui:
            return self.label_percentages

        # We'll update the merge layout instead of the info frame
        # The actual UI update happens in update_merge_listbox() which is called after this method

        # Calculate total pixels
        total_pixels = self.segmented_image.shape[0] * self.segmented_image.shape[1]
        print(f"DEBUG: Calculating percentages for {total_pixels} total pixels")

        # Find unique colors using vectorized operations
        # This is much faster than pixel-by-pixel counting
        try:
            # Reshape the image to a 2D array of pixels
            pixels = self.segmented_image.reshape(-1, 3)
            # Convert to tuples for hashability
            pixel_tuples = [tuple(p) for p in pixels]
            # Count occurrences using Counter
            from collections import Counter
            color_counts = Counter(pixel_tuples)
            unique_colors = dict(color_counts)
            print(f"DEBUG: Found {len(unique_colors)} unique colors in segmentation")
        except Exception as e:
            print(f"DEBUG: Error calculating unique colors: {e}")
            # Fallback to slower method
            unique_colors = {}
            for y in range(self.segmented_image.shape[0]):
                for x in range(self.segmented_image.shape[1]):
                    color = tuple(self.segmented_image[y, x])
                    if color not in unique_colors:
                        unique_colors[color] = 0
                    unique_colors[color] += 1
            print(f"DEBUG: Found {len(unique_colors)} unique colors using fallback method")

        # Sort colors by percentage (descending)
        sorted_colors = sorted(unique_colors.items(), key=lambda x: x[1], reverse=True)

        # Store percentages for later use
        self.label_percentages = {}

        # Calculate percentages
        for i, (color, count) in enumerate(sorted_colors):
            percentage = (count / total_pixels) * 100
            self.label_percentages[color] = percentage

            # Skip UI updates if not needed
            if not update_ui:
                continue

            # Create a horizontal layout for this color
            color_layout = QHBoxLayout()

            # Color box
            color_box = QLabel()
            color_box.setFixedSize(20, 20)
            color_box.setStyleSheet(f"background-color: rgb({color[0]}, {color[1]}, {color[2]}); border: 1px solid black;")
            color_layout.addWidget(color_box)

            # Normalize the color tuple
            normalized_color = self.normalize_color_tuple(color)

            # Get segment name if available, otherwise use default
            segment_name = self.segment_names.get(normalized_color, None)

            # If not found with normalized color, try with the original color
            if segment_name is None:
                segment_name = self.segment_names.get(color, f"Label {i+1}")
                # Add it to the segment_names dictionary for future use
                if segment_name != f"Label {i+1}":
                    self.segment_names[normalized_color] = segment_name

            # We don't need to update the UI here anymore
            # The update_merge_listbox method will handle the UI update

        return self.label_percentages

    def update_color_correspondence(self):
        """Updates the color correspondence dictionary."""
        if self.segmented_image is None:
            return

        # Find unique colors
        unique_colors = set()
        for y in range(self.segmented_image.shape[0]):
            for x in range(self.segmented_image.shape[1]):
                unique_colors.add(tuple(self.segmented_image[y, x]))

        # Update new_colors dictionary with any new colors
        for color in unique_colors:
            if color not in self.new_colors:
                self.new_colors[color] = color

    def update_merge_listbox(self):
        """Updates the merge listbox with current segment colors."""
        if self.segmented_image is None:
            return

        # Clear existing checkboxes and layouts completely
        while self.merge_inner_layout.count():
            item = self.merge_inner_layout.takeAt(0)
            if item.widget():
                item.widget().deleteLater()
            elif item.layout():
                # Clear the nested layout
                while item.layout().count():
                    child = item.layout().takeAt(0)
                    if child.widget():
                        child.widget().deleteLater()

        # Clear merge_vars dictionary
        self.merge_vars = {}

        # Find unique colors
        unique_colors = set()
        for y in range(self.segmented_image.shape[0]):
            for x in range(self.segmented_image.shape[1]):
                unique_colors.add(tuple(self.segmented_image[y, x]))

        # Sort colors by their percentage (if available)
        if hasattr(self, 'label_percentages') and self.label_percentages:
            sorted_colors = sorted(unique_colors, key=lambda x: self.label_percentages.get(x, 0), reverse=True)
        else:
            sorted_colors = list(unique_colors)

        # Create checkboxes for each color
        for i, color in enumerate(sorted_colors):
            # Create a horizontal layout for this color
            color_layout = QHBoxLayout()
            color_layout.setContentsMargins(0, 2, 0, 2)  # Add some vertical spacing between items

            # Color box
            color_box = QLabel()
            color_box.setFixedSize(20, 20)
            color_box.setStyleSheet(f"background-color: rgb({color[0]}, {color[1]}, {color[2]}); border: 1px solid black;")
            color_layout.addWidget(color_box)

            # Normalize the color tuple
            normalized_color = self.normalize_color_tuple(color)

            # Get segment name if available, otherwise use default
            segment_name = self.segment_names.get(normalized_color, None)

            # If not found with normalized color, try with the original color
            if segment_name is None:
                segment_name = self.segment_names.get(color, f"Label {i+1}")
                # Add it to the segment_names dictionary for future use
                if segment_name != f"Label {i+1}":
                    self.segment_names[normalized_color] = segment_name

            # Checkbox with percentage if available
            percentage = self.label_percentages.get(color, 0)
            checkbox_text = f"{segment_name} ({percentage:.1f}%)" if percentage > 0 else segment_name
            checkbox = QCheckBox(checkbox_text)
            checkbox.setStyleSheet("padding: 2px;")  # Add padding to make checkboxes easier to click
            color_layout.addWidget(checkbox)

            # Store checkbox in merge_vars dictionary
            self.merge_vars[color] = checkbox

            # Add to merge inner layout
            self.merge_inner_layout.addLayout(color_layout)

        # Make sure we have a spacer at the end to push all items to the top
        # First remove any existing spacers
        for i in range(self.merge_inner_layout.count() - 1, -1, -1):
            item = self.merge_inner_layout.itemAt(i)
            if item and item.spacerItem():
                self.merge_inner_layout.removeItem(item)

        # Add a stretch at the end to push all items to the top
        self.merge_inner_layout.addStretch()

        # Force the frame to update its layout
        self.merge_inner_frame.adjustSize()

        # Update the merge button text to show number of segments
        segment_count = len(self.merge_vars)
        self.merge_button.setText(f"Merge Selected Segments ({segment_count} total)")

    def merge_selected_segments(self):
        """Merges the selected segments in the segmented image."""
        print("DEBUG: merge_selected_segments called")
        selected_labels = [label for label, checkbox in self.merge_vars.items() if checkbox.isChecked()]
        if len(selected_labels) < 2:
            QMessageBox.warning(self, "Warning", "Please select at least two segments to merge.")
            return

        print(f"DEBUG: Merging {len(selected_labels)} segments")
        merged_color = selected_labels[0]
        label_mapping = {label: merged_color for label in selected_labels if label != merged_color}

        # Reset the original segmented image and current single segment
        # This ensures we're working with the full segmentation
        self.original_segmented_image = None
        self.current_single_segment = None

        # Clear the segment image cache for this image
        if hasattr(self, 'segment_image_cache') and hasattr(self, 'current_image_path') and self.current_image_path in self.segment_image_cache:
            print(f"DEBUG: Clearing segment image cache for {self.current_image_path}")
            self.segment_image_cache[self.current_image_path] = {}

        # Perform the merge operation
        for old_label, new_label in label_mapping.items():
            mask = np.all(self.segmented_image == old_label, axis=-1)
            self.segmented_image[mask] = new_label
            print(f"DEBUG: Merged segment {old_label} into {new_label}")

        # Update the UI
        self.display_segmented_image(self.segmented_image)
        self.calculate_label_percentages(update_ui=True)
        self.update_color_correspondence()
        self.update_merge_listbox()

        # Save the state to ensure changes are preserved
        self.save_segmentation_state()

        # Update the segment grid after merging
        print("DEBUG: Updating segment grid after merging")
        self.display_multiple_segments()

        QMessageBox.information(self, "Success", f"Merged {len(selected_labels)} segments.")

    def change_colors(self):
        """Changes the colors of the segmented image based on selected palette."""
        print("DEBUG: change_colors called")
        if self.segmented_image is None:
            QMessageBox.warning(self, "Warning", "No segmented image available.")
            return

        # Reset segment-related state for color change
        self.original_segmented_image = None
        self.current_single_segment = None

        # Clear the segment image cache for this image
        if hasattr(self, 'segment_image_cache') and hasattr(self, 'current_image_path'):
            if not hasattr(self, 'segment_image_cache'):
                self.segment_image_cache = {}
            if self.current_image_path in self.segment_image_cache:
                print(f"DEBUG: Clearing segment image cache for {self.current_image_path} before color change")
                self.segment_image_cache[self.current_image_path] = {}

        # Get unique colors from the segmented image
        unique_colors = set()
        color_counts = {}

        for y in range(self.segmented_image.shape[0]):
            for x in range(self.segmented_image.shape[1]):
                color = tuple(self.segmented_image[y, x])
                unique_colors.add(color)
                color_counts[color] = color_counts.get(color, 0) + 1

        print(f"DEBUG: Found {len(unique_colors)} unique colors in segmentation")

        # Sort colors by percentage (descending)
        sorted_colors = sorted(color_counts.items(), key=lambda x: x[1], reverse=True)
        unique_colors = [color for color, _ in sorted_colors]

        # Reset the color mapping dictionary
        self.new_colors = {}

        # Get the current palette name
        palette_name = self.color_palette_combo.currentText()
        print(f"DEBUG: Using color palette: {palette_name}")

        # Check if this is a custom palette
        if palette_name.startswith("Custom: "):
            # Extract the actual palette name
            custom_palette_name = palette_name[8:]  # Remove "Custom: " prefix
            print(f"DEBUG: Applying custom palette: {custom_palette_name}")
            # Apply the custom palette
            self.apply_custom_palette(custom_palette_name)
            return

        # Handle predefined matplotlib palettes
        if palette_name in self.predefined_palettes:
            # Get the colormap
            cmap = self.predefined_palettes[palette_name]
            # Generate colors based on the number of segments
            num_colors = len(unique_colors)
            if num_colors > 0:
                # Generate evenly spaced values between 0 and 1
                color_values = np.linspace(0, 1, num_colors)
                # Get RGB colors from colormap
                for i, old_color in enumerate(unique_colors):
                    rgb_mpl = cmap(color_values[i])[:3]  # Get RGB values (exclude alpha)
                    # Convert from 0-1 range to 0-255 range
                    new_color_rgb = tuple(int(val * 255) for val in rgb_mpl)
                    self.new_colors[old_color] = new_color_rgb
        else:
            # Use custom color generator for non-matplotlib palettes
            color_generator = self.color_palettes.get(palette_name, self.color_palettes['Default'])
            for color in unique_colors:
                new_color_rgb = color_generator() # Assuming generator provides RGB
                self.new_colors[color] = new_color_rgb

        print(f"DEBUG: Created {len(self.new_colors)} new RGB color mappings")

        # Create a mapping to preserve segment names
        old_to_new_color = {}
        old_names = self.segment_names.copy()

        # Apply new colors
        new_image = np.zeros_like(self.segmented_image)
        for y in range(self.segmented_image.shape[0]):
            for x in range(self.segmented_image.shape[1]):
                old_color = tuple(self.segmented_image[y, x])
                new_color = self.new_colors[old_color]
                new_image[y, x] = new_color

                # Track the mapping from old to new colors
                old_to_new_color[old_color] = new_color

        # Update the segmented image
        self.segmented_image = new_image
        self.segmented_image_is_rgb = True # Ensure flag is set

        # Preserve segment names by mapping them to the new colors
        new_segment_names = {}
        for old_color, name in old_names.items():
            if old_color in old_to_new_color:
                new_bgr_color = old_to_new_color[old_color] # This is the new BGR color
                new_segment_names[new_bgr_color] = name # Keyed by new BGR color
                print(f"DEBUG: Mapped segment name '{name}' from old color {old_color} to new BGR color {new_bgr_color}")

        # Update segment names
        self.segment_names = new_segment_names

        # Update the UI
        print("DEBUG: Updating UI after color change")
        self.display_segmented_image(self.segmented_image)

        # Reset label percentages to force recalculation
        if hasattr(self, 'label_percentages'):
            print("DEBUG: Resetting label percentages after color change")
            self.label_percentages = {}

        # Calculate new percentages
        self.calculate_label_percentages(update_ui=True)
        self.update_color_correspondence()
        self.update_merge_listbox()

        # Update the segment grid with the new colors
        print("DEBUG: Updating segment grid after color change")
        self.display_multiple_segments()

        # Save the state to ensure segment names are preserved
        self.save_segmentation_state()

    def pick_colors(self):
        """Allows the user to pick colors and names for each segment."""
        print("DEBUG: pick_colors called")
        if self.segmented_image is None:
            QMessageBox.warning(self, "Warning", "No segmented image available.")
            return

        # Reset segment-related state for color change
        self.original_segmented_image = None
        self.current_single_segment = None

        # Clear the segment image cache for this image
        if hasattr(self, 'segment_image_cache') and hasattr(self, 'current_image_path'):
            if not hasattr(self, 'segment_image_cache'):
                self.segment_image_cache = {}
            if self.current_image_path in self.segment_image_cache:
                print(f"DEBUG: Clearing segment image cache for {self.current_image_path} before color picking")
                self.segment_image_cache[self.current_image_path] = {}

        # Get unique colors and sort them by percentage if available
        unique_colors = set()
        for y in range(self.segmented_image.shape[0]):
            for x in range(self.segmented_image.shape[1]):
                unique_colors.add(tuple(self.segmented_image[y, x]))

        print(f"DEBUG: Found {len(unique_colors)} unique colors in segmentation")

        # Sort colors by their percentage (if available)
        if hasattr(self, 'label_percentages') and self.label_percentages:
            sorted_colors = sorted(unique_colors, key=lambda x: self.label_percentages.get(x, 0), reverse=True)
        else:
            sorted_colors = list(unique_colors)

        # Create a dictionary mapping segment labels to their current colors and names
        segment_colors = {}
        segment_names = {}
        for i, color in enumerate(sorted_colors):
            segment_id = str(i + 1)
            segment_colors[segment_id] = QColor(color[0], color[1], color[2]).name()

            # Use existing name if available, otherwise use default
            if color in self.segment_names:
                segment_names[segment_id] = self.segment_names[color]
            else:
                segment_names[segment_id] = f"Segment {segment_id}"

        # Create and show the color picker dialog
        dialog = ColorPickerDialog(segment_colors, self, segment_names)
        if dialog.exec():
            # Get the new colors and names from the dialog
            new_colors_hex = dialog.get_colors() # Renamed to avoid confusion
            new_names = dialog.get_names()
            self.new_colors = {}

            # Create a mapping to preserve segment names
            old_to_new_color_map = {} # Renamed for clarity

            # Convert the new colors to BGR tuples and store names
            for i, color in enumerate(sorted_colors): # color is the old color (likely BGR from initial segmentation)
                segment_id = str(i + 1)
                new_q_color = QColor(new_colors_hex[segment_id]) # New color from dialog (hex)
                # Convert QColor (RGB) to BGR tuple for internal storage
                rgb_tuple = (new_q_color.red(), new_q_color.green(), new_q_color.blue())
                self.new_colors[color] = rgb_tuple # Map old color to new BGR color
                old_to_new_color_map[color] = rgb_tuple
                print(f"DEBUG: Mapping old color {color} to new RGB color {rgb_tuple} with name '{new_names[segment_id]}'")

            # Apply new colors
            new_image = np.zeros_like(self.segmented_image)
            for y in range(self.segmented_image.shape[0]):
                for x in range(self.segmented_image.shape[1]):
                    color = tuple(self.segmented_image[y, x])
                    new_image[y, x] = self.new_colors[color]

            # Update the segmented image
            self.segmented_image = new_image
            self.segmented_image_is_rgb = True # Ensure flag is set

            # Update segment names with the new colors and names from the dialog
            new_segment_names = {}
            for i, old_color in enumerate(sorted_colors):
                segment_id = str(i + 1)
                new_rgb_color = old_to_new_color_map[old_color] # Get the new BGR color
                new_segment_names[new_rgb_color] = new_names[segment_id] # Keyed by new BGR color
                print(f"DEBUG: Setting name '{new_names[segment_id]}' for new RGB color {new_rgb_color}")

            # Update segment names
            self.segment_names = new_segment_names
            self.segmented_image_is_rgb = True # Ensure flag is set

            # Update the UI
            print("DEBUG: Updating UI after color picking")
            self.display_segmented_image(self.segmented_image)

            # Reset label percentages to force recalculation
            if hasattr(self, 'label_percentages'):
                print("DEBUG: Resetting label percentages after color picking")
                self.label_percentages = {}

            # Calculate new percentages
            self.calculate_label_percentages(update_ui=True)
            self.update_color_correspondence()
            self.update_merge_listbox()

            # Update the segment grid with the new colors
            print("DEBUG: Updating segment grid after color picking")
            self.display_multiple_segments()

            # Save the state to ensure segment names are preserved
            self.save_segmentation_state()

    def save_segment_as_png(self, selected_color):
        """Saves a single segment as a PNG file.

        Args:
            selected_color: The color of the segment to save
        """
        if not hasattr(self, 'segmented_image') or self.segmented_image is None:
            QMessageBox.warning(self, "Warning", "No segmented image available.")
            return

        if not selected_color:
            QMessageBox.warning(self, "Warning", "No segment selected.")
            return

        # Create a segment image
        segment_image = self.create_segment_image(selected_color)
        if segment_image is None:
            QMessageBox.warning(self, "Warning", "Failed to create segment image.")
            return

        # Normalize the color tuple
        normalized_color = self.normalize_color_tuple(selected_color)

        # Get segment name
        segment_name = self.segment_names.get(normalized_color, None)

        # If not found with normalized color, try with the original color
        if segment_name is None:
            segment_name = self.segment_names.get(selected_color, "Unknown Segment")
            # Add it to the segment_names dictionary for future use
            if segment_name != "Unknown Segment":
                self.segment_names[normalized_color] = segment_name

        # Open file dialog to select save location
        default_name = f"{segment_name}.png" if segment_name else "segment.png"
        file_path, _ = QFileDialog.getSaveFileName(
            self, "Save Segment as PNG", default_name,
            "PNG Files (*.png);;All Files (*)"
        )

        if not file_path:
            return

        # Ensure the file has .png extension
        if not file_path.endswith('.png'):
            file_path += '.png'

        # Save the image
        try:
            # Convert from RGB to BGR for OpenCV
            image_bgr = cv2.cvtColor(segment_image, cv2.COLOR_RGB2BGR)
            cv2.imwrite(file_path, image_bgr)
            QMessageBox.information(self, "Success", f"Segment saved successfully to {file_path}")
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to save image: {str(e)}")

    def export_segment_as_annotations(self, selected_color):
        """Exports a single segment as annotations for trainable segmentation.

        Args:
            selected_color: The color of the segment to export
        """
        if not hasattr(self, 'segmented_image') or self.segmented_image is None:
            QMessageBox.warning(self, "Warning", "No segmented image available.")
            return

        if not selected_color:
            QMessageBox.warning(self, "Warning", "No segment selected.")
            return

        # Create a binary mask for the selected segment
        mask = np.zeros(self.segmented_image.shape[:2], dtype=np.uint8)
        for y in range(self.segmented_image.shape[0]):
            for x in range(self.segmented_image.shape[1]):
                if tuple(self.segmented_image[y, x]) == selected_color:
                    mask[y, x] = 1

        # Get the original image
        original_image = None
        if hasattr(self, 'image') and self.image is not None:
            original_image = self.image.copy()
        else:
            QMessageBox.warning(self, "Warning", "Original image not found.")
            return

        # Get segment name
        segment_name = self.segment_names.get(selected_color, "Unknown Segment")

        # Open file dialog to select save location
        default_name = f"{segment_name}.npz" if segment_name else "segment.npz"
        file_path, _ = QFileDialog.getSaveFileName(
            self, "Export Segment as Annotations", default_name,
            "NumPy Files (*.npz);;All Files (*)"
        )

        if not file_path:
            return

        # Ensure the file has .npz extension
        if not file_path.endswith('.npz'):
            file_path += '.npz'

        # Create dictionaries for segment names and colors
        segment_names = {1: segment_name}

        # Process the color to ensure it's in the correct format
        color = list(selected_color)  # Convert to list

        # If the image is in BGR format (OpenCV default), convert to RGB
        if hasattr(self, 'segmented_image_is_rgb') and not self.segmented_image_is_rgb:
            color = [color[2], color[1], color[0]]  # BGR to RGB
            print(f"DEBUG: Converting BGR color {selected_color} to RGB {color}")

        # Ensure all color values are integers
        color = [int(c) if isinstance(c, (float, str)) else c for c in color]

        # Ensure color values are in valid range (0-255)
        color = [max(0, min(255, c)) for c in color]

        segment_colors = {1: color}
        print(f"DEBUG: Using color {color} for segment {segment_name}")

        # Save annotations, original image, segment names, and segment colors
        try:
            # Convert dictionaries to JSON strings for reliable storage
            import json
            segment_names_json = json.dumps(segment_names)

            # Convert segment_colors to a JSON string
            # Convert tuples to lists for JSON serialization
            segment_colors_serializable = {}
            for key, value in segment_colors.items():
                if isinstance(value, tuple):
                    value = list(value)
                segment_colors_serializable[str(key)] = value
            segment_colors_json = json.dumps(segment_colors_serializable)

            # Extract just the filename from the full path if available
            image_filename = ""
            if hasattr(self, 'current_image_path') and self.current_image_path:
                image_filename = os.path.basename(self.current_image_path)
                print(f"DEBUG: Using image filename '{image_filename}' instead of full path for export")

            np.savez_compressed(
                file_path,
                annotations=mask,
                image=original_image,
                segment_names=segment_names_json,
                segment_colors=segment_colors_json,
                image_path=image_filename,  # Save just the filename, not the full path
                format="trainable_segmentation"
            )

            QMessageBox.information(self, "Success", f"Segment exported successfully to {file_path}")
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to export segment: {str(e)}")

    def export_segments_as_annotations(self):
        """Exports the segmented image as annotations for trainable segmentation."""
        print("DEBUG: export_segments_as_annotations method called")
        if self.segmented_image is None:
            print("DEBUG: No segmented image available")
            QMessageBox.warning(self, "Warning", "No segmented image available.")
            return

        if not hasattr(self, 'current_image_path') or not self.current_image_path:
            QMessageBox.warning(self, "Warning", "No image loaded.")
            return

        # Get unique colors in the segmented image
        unique_colors = set()
        for y in range(self.segmented_image.shape[0]):
            for x in range(self.segmented_image.shape[1]):
                unique_colors.add(tuple(self.segmented_image[y, x]))

        # Sort colors by their percentage (if available)
        if hasattr(self, 'label_percentages') and self.label_percentages:
            sorted_colors = sorted(unique_colors, key=lambda x: self.label_percentages.get(x, 0), reverse=True)
        else:
            sorted_colors = list(unique_colors)

        # Create a label map (color -> label index)
        label_map = {}
        label_index = 1  # Start from 1
        for color in sorted_colors:
            # Include all colors, even black
            label_map[color] = label_index
            label_index += 1

        print(f"DEBUG: Created label map with {len(label_map)} labels: {label_map}")

        # Create annotation mask
        annotations = np.zeros(self.segmented_image.shape[:2], dtype=np.uint8)
        for y in range(self.segmented_image.shape[0]):
            for x in range(self.segmented_image.shape[1]):
                color = tuple(self.segmented_image[y, x])
                # Only set the label if the color is in the label map
                if color in label_map:
                    annotations[y, x] = label_map[color]
                # Otherwise, leave it as 0 (background)

        # Get the original image
        original_image = None
        if hasattr(self, 'image') and self.image is not None:
            original_image = self.image.copy()
        else:
            QMessageBox.warning(self, "Warning", "Original image not found.")
            return

        # Open file dialog to select save location
        file_path, _ = QFileDialog.getSaveFileName(
            self, "Export Annotations", "",
            "NumPy Files (*.npz);;All Files (*)"
        )

        if not file_path:
            return

        # Ensure the file has .npz extension
        if not file_path.endswith('.npz'):
            file_path += '.npz'

        # Create dictionaries to store segment names and colors
        segment_names = {}
        segment_colors = {}

        # Debug: Print all segment names in self.segment_names
        print(f"DEBUG: All segment names in self.segment_names: {self.segment_names}")

        # Debug: Print the pick_colors method to see if it's been called
        print(f"DEBUG: Has pick_colors been called? {hasattr(self, 'new_colors') and bool(self.new_colors)}")

        # Debug: Print the label_map
        print(f"DEBUG: Label map: {label_map}")

        # First, create a mapping of colors to custom names if they exist
        custom_names = {}
        for color, name in self.segment_names.items():
            # Always include the name, whether it's custom or default
            custom_names[color] = name
            if name.startswith("Segment "):
                print(f"DEBUG: Found default name '{name}' for color {color}")
            else:
                print(f"DEBUG: Found custom name '{name}' for color {color}")

        for color, label in label_map.items():
            # Get segment name (use stored name or default)
            if color in custom_names:
                name = custom_names[color]
                print(f"DEBUG: Using custom name '{name}' for color {color} (label {label})")
            else:
                name = self.segment_names.get(color, f"Segment {label}")
                print(f"DEBUG: Using default name '{name}' for color {color} (label {label})")

            segment_names[label] = name
            print(f"DEBUG: Mapping color {color} (label {label}) to name '{name}'")

            # Get segment color - ALWAYS use the ACTUAL color from the segmented image
            # This ensures that the colors seen in the segmented image are preserved
            # when loaded into the trainable segmentation page
            # Make sure we're using RGB format (not BGR)
            if len(color) == 3:
                # If the image is in BGR format (OpenCV default), convert to RGB
                if hasattr(self, 'segmented_image_is_rgb') and not self.segmented_image_is_rgb:
                    rgb_color = [color[2], color[1], color[0]]  # BGR to RGB
                    print(f"DEBUG: Converting BGR color {color} to RGB {rgb_color}")
                else:
                    rgb_color = list(color)  # Already RGB
                    print(f"DEBUG: Using RGB color {rgb_color} directly")
            else:
                # Fallback for unexpected color format
                rgb_color = list(color)
                print(f"DEBUG: Using color {rgb_color} as-is (unexpected format)")

            # Ensure all color values are integers
            rgb_color = [int(c) if isinstance(c, (float, str)) else c for c in rgb_color]

            # Ensure color values are in valid range (0-255)
            rgb_color = [max(0, min(255, c)) for c in rgb_color]

            print(f"DEBUG: Using color {rgb_color} for segment {name}")
            segment_colors[label] = rgb_color

        # Debug: Print final segment_names dictionary
        print(f"DEBUG: Final segment_names dictionary: {segment_names}")

        # Save annotations, original image, segment names, and segment colors
        try:
            # Convert segment_names to a JSON string for reliable storage
            import json

            # Convert segment_names to a JSON string
            # Make sure all keys are strings for JSON serialization
            segment_names_serializable = {}
            for key, value in segment_names.items():
                segment_names_serializable[str(key)] = value
            segment_names_json = json.dumps(segment_names_serializable)
            print(f"DEBUG: Saving segment_names as JSON: {segment_names_json}")

            # Convert segment_colors to a JSON string
            # Convert tuples to lists for JSON serialization and ensure all keys are strings
            segment_colors_serializable = {}
            for key, value in segment_colors.items():
                # Convert key to string
                str_key = str(key)
                # Convert value to list if it's a tuple or numpy array
                if isinstance(value, (tuple, np.ndarray)):
                    value = list(value)
                # Make sure all values in the list are integers
                if isinstance(value, list):
                    value = [int(v) if hasattr(v, 'item') else v for v in value]
                segment_colors_serializable[str_key] = value
            segment_colors_json = json.dumps(segment_colors_serializable)
            print(f"DEBUG: Saving segment_colors as JSON: {segment_colors_json}")

            # Extract just the filename from the full path
            image_filename = os.path.basename(self.current_image_path)
            print(f"DEBUG: Using image filename '{image_filename}' instead of full path for export")

            np.savez_compressed(
                file_path,
                annotations=annotations,
                image=original_image,
                segment_names_json=np.array(segment_names_json),  # Save as numpy array of string
                segment_colors_json=np.array(segment_colors_json),  # Save as numpy array of string
                image_path=image_filename  # Save just the filename, not the full path
            )
            QMessageBox.information(self, "Success", f"Annotations exported to {file_path}")
            print(f"DEBUG: Successfully exported annotations with {len(segment_names)} segments and their colors")
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to export annotations: {e}")
            print(f"DEBUG: Error exporting annotations: {e}")

    def enable_buttons(self, training=True):
        """Enables or disables buttons based on the current state."""
        # upload_button removed - images are now only loaded from project hub
        self.segment_button.setEnabled(not training)
        self.stop_button.setEnabled(training)
        self.change_colors_button.setEnabled(not training and self.segmented_image is not None)
        self.pick_colors_button.setEnabled(not training and self.segmented_image is not None)
        self.download_button.setEnabled(not training and self.segmented_image is not None)
        self.merge_button.setEnabled(not training and self.segmented_image is not None)
        self.export_coco_button.setEnabled(not training and self.segmented_image is not None)
        self.export_annotations_button.setEnabled(not training and self.segmented_image is not None)
        if hasattr(self, 'save_palette_button'):
            self.save_palette_button.setEnabled(not training and self.segmented_image is not None)
        if hasattr(self, 'manage_palettes_button'):
            self.manage_palettes_button.setEnabled(not training)

    def load_custom_palettes(self):
        """Loads custom palettes from the JSON file."""
        if os.path.exists(self.custom_palettes_file):
            try:
                with open(self.custom_palettes_file, 'r') as f:
                    self.custom_palettes = json.load(f)
                logger.info(f"Loaded {len(self.custom_palettes)} custom palettes from {self.custom_palettes_file}")
            except Exception as e:
                logger.error(f"Failed to load custom palettes: {e}")
                self.custom_palettes = {}
        else:
            logger.info("No custom palettes file found. Starting with empty custom palettes.")
            self.custom_palettes = {}

    def save_custom_palettes(self):
        """Saves custom palettes to the JSON file."""
        try:
            with open(self.custom_palettes_file, 'w') as f:
                json.dump(self.custom_palettes, f, indent=4)
            logger.info(f"Saved {len(self.custom_palettes)} custom palettes to {self.custom_palettes_file}")
            return True
        except Exception as e:
            logger.error(f"Failed to save custom palettes: {e}")
            return False

    def save_custom_palette(self):
        """Saves the current segment colors and names as a custom palette."""
        if self.segmented_image is None:
            QMessageBox.warning(self, "Warning", "No segmented image available.")
            return

        # Get unique colors and their names
        unique_colors = set()
        for y in range(self.segmented_image.shape[0]):
            for x in range(self.segmented_image.shape[1]):
                unique_colors.add(tuple(self.segmented_image[y, x]))

        # Create a dictionary mapping segment names to their colors
        palette_data = {}
        for color in unique_colors:
            name = self.segment_names.get(color, f"Segment {len(palette_data) + 1}")
            # Convert color tuple to string for JSON serialization
            color_str = f"{color[0]},{color[1]},{color[2]}"
            palette_data[name] = color_str

        # Show the custom palette dialog
        dialog = CustomPaletteDialog(self.segment_names, self.segment_names, self)
        if dialog.exec():
            palette_name = dialog.get_palette_name()

            # Check if palette name already exists
            if palette_name in self.custom_palettes:
                confirm = QMessageBox.question(
                    self, "Confirm Overwrite",
                    f"A palette named '{palette_name}' already exists. Do you want to overwrite it?",
                    QMessageBox.Yes | QMessageBox.No, QMessageBox.No
                )
                if confirm != QMessageBox.Yes:
                    return

            # Save the palette
            self.custom_palettes[palette_name] = palette_data
            self.save_custom_palettes()

            # Update the palette combo box
            self.update_palette_combo()

            QMessageBox.information(self, "Success", f"Custom palette '{palette_name}' saved successfully.")

    def manage_palettes(self):
        """Opens the palette management dialog."""
        dialog = PaletteManagementDialog(self.custom_palettes, self)

        # Connect signals
        dialog.palette_deleted.connect(self.on_palette_deleted)
        dialog.palettes_imported.connect(self.on_palettes_imported)

        if dialog.exec():
            # Get the updated palettes
            self.custom_palettes = dialog.get_custom_palettes()

            # Save the palettes
            self.save_custom_palettes()

            # Update the palette combo box
            self.update_palette_combo()

    def on_palette_deleted(self, palette_name):
        """Handle palette deletion."""
        # If the current palette is the one that was deleted, switch to a default palette
        current_palette = self.color_palette_combo.currentText()
        if current_palette == f"Custom: {palette_name}":
            # Find the first available palette
            if self.color_palette_combo.count() > 1:
                self.color_palette_combo.setCurrentIndex(0)

    def on_palettes_imported(self):
        """Handle palettes import."""
        # Update the palette combo box
        self.update_palette_combo()

    def update_palette_combo(self):
        """Updates the palette combo box with custom palettes."""
        # Store the current selection
        current_selection = self.color_palette_combo.currentText()

        # Clear the combo box
        self.color_palette_combo.clear()

        # Add predefined palettes
        self.color_palette_combo.addItems(list(self.predefined_palettes.keys()))

        # Add custom palettes with a prefix to distinguish them
        for palette_name in sorted(self.custom_palettes.keys()):
            self.color_palette_combo.addItem(f"Custom: {palette_name}")

        # Set the custom palettes in the combo box if it's a PaletteComboBox
        if hasattr(self.color_palette_combo, 'set_custom_palettes'):
            self.color_palette_combo.set_custom_palettes(self.custom_palettes)

        # Restore the previous selection if it still exists
        index = self.color_palette_combo.findText(current_selection)
        if index >= 0:
            self.color_palette_combo.setCurrentIndex(index)

    def apply_custom_palette(self, palette_name):
        """Applies a custom palette to the segmented image."""
        print(f"DEBUG: apply_custom_palette called with palette_name={palette_name}")
        if palette_name not in self.custom_palettes:
            logger.error(f"Custom palette '{palette_name}' not found")
            return False

        if self.segmented_image is None:
            logger.error("No segmented image available")
            return False

        # Reset segment-related state for color change
        self.original_segmented_image = None
        self.current_single_segment = None

        # Clear the segment image cache for this image
        if hasattr(self, 'segment_image_cache') and hasattr(self, 'current_image_path'):
            if not hasattr(self, 'segment_image_cache'):
                self.segment_image_cache = {}
            if self.current_image_path in self.segment_image_cache:
                print(f"DEBUG: Clearing segment image cache for {self.current_image_path} before applying custom palette")
                self.segment_image_cache[self.current_image_path] = {}

        # Get the palette data
        palette_data = self.custom_palettes[palette_name]
        print(f"DEBUG: Palette data contains {len(palette_data)} entries")

        # Get unique colors in the segmented image
        unique_colors = set()
        for y in range(self.segmented_image.shape[0]):
            for x in range(self.segmented_image.shape[1]):
                unique_colors.add(tuple(self.segmented_image[y, x]))

        print(f"DEBUG: Found {len(unique_colors)} unique colors in segmentation")

        # Create a mapping from segment names to colors
        name_to_color = {}
        for color in unique_colors:
            name = self.segment_names.get(color, "")
            if name:
                name_to_color[name] = color

        print(f"DEBUG: Created mapping from {len(name_to_color)} segment names to colors")

        # Create a mapping from old colors to new colors
        self.new_colors = {}

        # For each segment in the palette
        for name, color_str in palette_data.items():
            # If this segment name exists in the current image
            if name in name_to_color:
                # Get the old color
                old_color = name_to_color[name]

                # Parse the new color (assumed RGB from "r,g,b" string)
                r_val, g_val, b_val = map(int, color_str.split(','))
                # Convert to BGR for internal storage
                new_color_bgr = (b_val, g_val, r_val)

                # Add to the color mapping
                self.new_colors[old_color] = new_color_bgr
                print(f"DEBUG: Mapped segment '{name}' from color {old_color} to new BGR color {new_color_bgr}")

        # For any segments not in the palette, assign random colors (BGR)
        for color in unique_colors:
            if color not in self.new_colors:
                # Assuming np.random.randint produces components for RGB order
                r_rand, g_rand, b_rand = np.random.randint(50, 220, 3)
                self.new_colors[color] = (b_rand, g_rand, r_rand) # Store as BGR
                print(f"DEBUG: Assigned random BGR color {self.new_colors[color]} to segment with color {color}")

        # Create a mapping to preserve segment names
        old_to_new_color_map = {} # Renamed for clarity

        # Apply the new colors
        new_image = np.zeros_like(self.segmented_image)
        for y in range(self.segmented_image.shape[0]):
            for x in range(self.segmented_image.shape[1]):
                old_color = tuple(self.segmented_image[y, x])
                new_color = self.new_colors[old_color] # new_color is already BGR
                new_image[y, x] = new_color

                # Track the mapping from old to new colors
                old_to_new_color_map[old_color] = new_color

        # Update the segmented image
        self.segmented_image = new_image
        self.segmented_image_is_rgb = True # Ensure flag is set

        # Preserve segment names by mapping them to the new colors
        new_segment_names = {}
        for old_color, name in self.segment_names.items(): # self.segment_names keys are old colors
            if old_color in old_to_new_color_map:
                new_bgr_color = old_to_new_color_map[old_color] # This is the new BGR color
                new_segment_names[new_bgr_color] = name # Keyed by new BGR color
                print(f"DEBUG: Mapped segment name '{name}' from old color {old_color} to new BGR color {new_bgr_color}")

        # Update segment names
        self.segment_names = new_segment_names

        # Update the UI
        print("DEBUG: Updating UI after applying custom palette")
        self.display_segmented_image(self.segmented_image)

        # Reset label percentages to force recalculation
        if hasattr(self, 'label_percentages'):
            print("DEBUG: Resetting label percentages after applying custom palette")
            self.label_percentages = {}

        # Calculate new percentages
        self.calculate_label_percentages(update_ui=True)
        self.update_color_correspondence()
        self.update_merge_listbox()

        # Update the segment grid with the new colors
        print("DEBUG: Updating segment grid after applying custom palette")
        self.display_multiple_segments()

        # Save the state to ensure segment names are preserved
        self.save_segmentation_state()

        return True

    def disable_buttons(self, training=True):
        """Disables buttons during training.

        Args:
            training: Whether we're disabling for training (True) or other reasons (False)
                     This parameter is used by subclasses.
        """
        # Suppress unused parameter warning
        _ = training

        # upload_button removed - images are now only loaded from project hub
        self.segment_button.setEnabled(False)
        self.stop_button.setEnabled(True)
        self.change_colors_button.setEnabled(False)
        self.pick_colors_button.setEnabled(False)
        self.download_button.setEnabled(False)
        self.merge_button.setEnabled(False)
        self.export_coco_button.setEnabled(False)
        self.export_annotations_button.setEnabled(False)
        if hasattr(self, 'save_palette_button'):
            self.save_palette_button.setEnabled(False)
        if hasattr(self, 'manage_palettes_button'):
            self.manage_palettes_button.setEnabled(False)
