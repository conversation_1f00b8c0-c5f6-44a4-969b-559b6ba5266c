"""
Settings manager for VisionLab Ai application.
This module provides a centralized way to manage application settings.
"""

import os
import json
from PySide6.QtCore import QSettings, QStandardPaths
import logging

logger = logging.getLogger(__name__)

class SettingsManager:
    """Manages application settings using QSettings and JSON files."""

    # Constants
    APP_NAME = "VisionLab_Ai_V4"
    ORGANIZATION = "VisionLab Ai"
    SETTINGS_VERSION = "1.0"

    def __init__(self):
        """Initialize the settings manager."""
        self.qsettings = QSettings(self.ORGANIZATION, self.APP_NAME)

        # Ensure settings version is set
        current_version = self.qsettings.value("settings/version", "")
        if current_version != self.SETTINGS_VERSION:
            self._migrate_settings(current_version)
            self.qsettings.setValue("settings/version", self.SETTINGS_VERSION)
            self.qsettings.sync()

    def _migrate_settings(self, current_version):
        """Migrate settings from older versions."""
        logger.info(f"Migrating settings from version {current_version} to {self.SETTINGS_VERSION}")

        # If this is a new installation (no version)
        if not current_version:
            # Set default values for new installations
            self._set_default_values()
            return

        # Add migration logic for specific version upgrades here
        # Example:
        # if current_version == "0.9":
        #     # Migrate from 0.9 to 1.0
        #     old_value = self.qsettings.value("old_key")
        #     self.qsettings.setValue("new_key", old_value)
        #     self.qsettings.remove("old_key")

    def _set_default_values(self):
        """Set default values for a new installation."""
        # App settings
        self.qsettings.setValue("app/theme", "Dark Theme")
        self.qsettings.setValue("app/color_scheme", "Default")
        self.qsettings.setValue("app/font_family", "Segoe UI")
        self.qsettings.setValue("app/font_size", "normal")
        self.qsettings.setValue("app/language", "English")

        # Segmentation settings
        self.qsettings.setValue("segmentation/default_method", "KMeans")
        self.qsettings.setValue("segmentation/default_epochs", 100)
        self.qsettings.setValue("segmentation/default_min_labels", 3)
        self.qsettings.setValue("segmentation/default_max_labels", 10)
        self.qsettings.setValue("segmentation/default_color_palette", "Default")

        # Trainable segmentation settings
        self.qsettings.setValue("trainable_segmentation/default_intensity_features", True)
        self.qsettings.setValue("trainable_segmentation/default_edge_features", False)
        self.qsettings.setValue("trainable_segmentation/default_texture_features", True)
        self.qsettings.setValue("trainable_segmentation/default_sigma_min", 1)
        self.qsettings.setValue("trainable_segmentation/default_sigma_max", 16)
        self.qsettings.setValue("trainable_segmentation/default_n_estimators", 100)
        self.qsettings.setValue("trainable_segmentation/default_max_depth", 10)
        self.qsettings.setValue("trainable_segmentation/default_max_samples", 0.7)
        self.qsettings.setValue("trainable_segmentation/default_brush_size", 5)

        # Grain analysis settings
        self.qsettings.setValue("grain_analysis/default_input_size", 1024)
        self.qsettings.setValue("grain_analysis/default_iou_threshold", 0.7)
        self.qsettings.setValue("grain_analysis/default_conf_threshold", 0.5)
        self.qsettings.setValue("grain_analysis/default_max_det", 500)


        # Artifact handling settings
        self.qsettings.setValue("grain_analysis/default_artifact_preset", "Balanced")
        self.qsettings.setValue("grain_analysis/default_artifact_sensitivity", 0.5)
        self.qsettings.setValue("grain_analysis/default_duplicate_sensitivity", 0.7)

        # MobileSAM settings
        self.qsettings.setValue("mobilesam/default_points_per_side", 32)
        self.qsettings.setValue("mobilesam/default_pred_iou_thresh", 0.88)
        self.qsettings.setValue("mobilesam/default_stability_score_thresh", 0.95)
        self.qsettings.setValue("mobilesam/default_box_nms_thresh", 0.3)
        self.qsettings.setValue("mobilesam/default_min_mask_area", 0)

        # YOLOv8 settings have been removed

        # Point Counting settings
        self.qsettings.setValue("point_counting/grid_color_r", 255)
        self.qsettings.setValue("point_counting/grid_color_g", 255)
        self.qsettings.setValue("point_counting/grid_color_b", 255)
        self.qsettings.setValue("point_counting/grid_color_a", 180)
        self.qsettings.setValue("point_counting/grid_opacity", 70)

        # Image settings
        self.qsettings.setValue("image/default_width", 750)
        self.qsettings.setValue("image/default_height", 750)
        self.qsettings.setValue("image/auto_resize", True)

        # Export settings
        self.qsettings.setValue("export/directory", "")
        self.qsettings.setValue("export/format", "PNG")

        # Force settings to be written to disk
        self.qsettings.sync()

    def get_value(self, key, default=None):
        """Get a value from QSettings."""
        return self.qsettings.value(key, default)

    def set_value(self, key, value):
        """Set a value in QSettings."""
        self.qsettings.setValue(key, value)
        self.qsettings.sync()  # Force settings to be written to disk

    def get_all_settings(self):
        """Get all settings as a dictionary."""
        settings = {}
        self.qsettings.sync()  # Ensure we're reading the latest values

        # Iterate through all keys
        for key in self.qsettings.allKeys():
            settings[key] = self.qsettings.value(key)

        return settings

    def backup_settings(self, backup_file=None):
        """Backup all settings to a JSON file."""
        if backup_file is None:
            # Use default location in documents folder
            docs_path = QStandardPaths.writableLocation(QStandardPaths.DocumentsLocation)
            backup_file = os.path.join(docs_path, f"{self.APP_NAME}_settings_backup.json")

        try:
            settings = self.get_all_settings()
            with open(backup_file, 'w') as f:
                json.dump(settings, f, indent=4)
            logger.info(f"Settings backed up to {backup_file}")
            return True, backup_file
        except Exception as e:
            logger.error(f"Failed to backup settings: {e}")
            return False, str(e)

    def restore_settings(self, backup_file):
        """Restore settings from a JSON file."""
        if not os.path.exists(backup_file):
            logger.error(f"Backup file not found: {backup_file}")
            return False, "Backup file not found"

        try:
            with open(backup_file, 'r') as f:
                settings = json.load(f)

            # Clear current settings
            self.qsettings.clear()

            # Restore settings
            for key, value in settings.items():
                self.qsettings.setValue(key, value)

            self.qsettings.sync()
            logger.info(f"Settings restored from {backup_file}")
            return True, "Settings restored successfully"
        except Exception as e:
            logger.error(f"Failed to restore settings: {e}")
            return False, str(e)

    def get_project_settings(self, project_dir):
        """Get project-specific settings."""
        settings_file = os.path.join(project_dir, "project_settings.json")
        if os.path.exists(settings_file):
            try:
                with open(settings_file, 'r') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"Failed to load project settings: {e}")

        return {}

    def save_project_settings(self, project_dir, settings):
        """Save project-specific settings."""
        settings_file = os.path.join(project_dir, "project_settings.json")
        try:
            with open(settings_file, 'w') as f:
                json.dump(settings, f, indent=4)
            logger.info(f"Project settings saved to {settings_file}")
            return True
        except Exception as e:
            logger.error(f"Failed to save project settings: {e}")
            return False

    def add_recent_project(self, project_path, project_name=None, project_type="standard"):
        """Add a project to the recent projects list.
        
        Args:
            project_path (str): Full path to the project directory
            project_name (str, optional): Display name for the project
            project_type (str): Type of project ('standard' or 'vlp')
        """
        if not project_name:
            project_name = os.path.basename(project_path)
        
        # Get current recent projects
        recent_projects = self.get_recent_projects()
        
        # Create new project entry
        new_project = {
            'path': project_path,
            'name': project_name,
            'type': project_type,
            'last_opened': self._get_current_timestamp()
        }
        
        # Remove if already exists (to move to top)
        recent_projects = [p for p in recent_projects if p.get('path') != project_path]
        
        # Add to beginning of list
        recent_projects.insert(0, new_project)
        
        # Keep only the most recent 10 projects
        recent_projects = recent_projects[:10]
        
        # Save back to settings
        self.set_value("recent_projects/list", json.dumps(recent_projects))
        logger.info(f"Added recent project: {project_name} at {project_path}")
    
    def get_recent_projects(self):
        """Get the list of recent projects.
        
        Returns:
            list: List of recent project dictionaries
        """
        try:
            recent_projects_json = self.get_value("recent_projects/list", "[]")
            recent_projects = json.loads(recent_projects_json)
            
            # Filter out projects that no longer exist
            valid_projects = []
            for project in recent_projects:
                if os.path.exists(project.get('path', '')):
                    valid_projects.append(project)
                else:
                    logger.debug(f"Removing non-existent recent project: {project.get('path')}")
            
            # Update the list if we removed any invalid projects
            if len(valid_projects) != len(recent_projects):
                self.set_value("recent_projects/list", json.dumps(valid_projects))
            
            return valid_projects
        except (json.JSONDecodeError, Exception) as e:
            logger.error(f"Failed to load recent projects: {e}")
            return []
    
    def clear_recent_projects(self):
        """Clear all recent projects."""
        self.set_value("recent_projects/list", "[]")
        logger.info("Cleared recent projects list")
    
    def remove_recent_project(self, project_path):
        """Remove a specific project from recent projects.
        
        Args:
            project_path (str): Path of the project to remove
        """
        recent_projects = self.get_recent_projects()
        recent_projects = [p for p in recent_projects if p.get('path') != project_path]
        self.set_value("recent_projects/list", json.dumps(recent_projects))
        logger.info(f"Removed recent project: {project_path}")
    
    def _get_current_timestamp(self):
        """Get current timestamp as ISO string."""
        from datetime import datetime
        return datetime.now().isoformat()


# Create a singleton instance
settings_manager = SettingsManager()
