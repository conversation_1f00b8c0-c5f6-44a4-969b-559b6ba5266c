"""
Startup Timer Module for VisionLab_Ai_V4

This module provides utilities for measuring and logging application startup time.
"""

import time
import os
from datetime import datetime
from typing import Dict, List, Optional, Tuple

class StartupTimer:
    """
    A utility class to measure and log application startup time.

    This class tracks the overall startup time as well as the time taken by
    individual components during the application initialization process.
    """

    def __init__(self, log_file: Optional[str] = None):
        """
        Initialize the startup timer.

        Args:
            log_file: Optional path to a log file. If not provided, a default path will be used.
        """
        self.start_time = time.time()
        self.checkpoints: List[Tuple[str, float]] = []
        self.component_times: Dict[str, float] = {}
        self.log_entries: List[str] = []

        # Set up log file
        self._setup_log_file(log_file)

        # Record application start
        self.checkpoint("Application start")
        self._log(f"Application startup began at {datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]}")

    def _setup_log_file(self, log_file: Optional[str] = None):
        """
        Set up the log file.

        Args:
            log_file: Optional path to a log file. If not provided, a default path will be used.
        """
        # Create logs directory if it doesn't exist
        logs_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 'logs')
        os.makedirs(logs_dir, exist_ok=True)

        # Set default log file path if not provided
        if log_file is None:
            self.log_file = os.path.join(logs_dir, 'startup_timing.log')
        else:
            self.log_file = log_file

        # Print the log file path for debugging
        print(f"Startup timing log will be written to: {os.path.abspath(self.log_file)}")

    def _log(self, message: str):
        """
        Log a message to both console and the log file.

        Args:
            message: The message to log.
        """
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]
        formatted_message = f"{timestamp} - INFO - {message}"

        # Print to console
        print(formatted_message)

        # Add to log entries for later writing to file
        self.log_entries.append(formatted_message)

    def checkpoint(self, name: str) -> float:
        """
        Record a checkpoint with the given name.

        Args:
            name: The name of the checkpoint.

        Returns:
            The time elapsed since the start of the application.
        """
        current_time = time.time()
        elapsed = current_time - self.start_time

        # If this is not the first checkpoint, calculate time since last checkpoint
        if self.checkpoints:
            time_since_last = current_time - self.checkpoints[-1][1]
            self._log(f"Checkpoint: {name} - {elapsed:.3f}s total, {time_since_last:.3f}s since last checkpoint")
        else:
            self._log(f"Checkpoint: {name} - {elapsed:.3f}s total")

        self.checkpoints.append((name, current_time))
        return elapsed

    def start_component(self, component_name: str) -> None:
        """
        Start timing a component initialization.

        Args:
            component_name: The name of the component being initialized.
        """
        self.component_times[component_name] = time.time()
        self._log(f"Starting component initialization: {component_name}")

    def end_component(self, component_name: str) -> float:
        """
        End timing a component initialization and return the elapsed time.

        Args:
            component_name: The name of the component that was initialized.

        Returns:
            The time taken to initialize the component.
        """
        if component_name not in self.component_times:
            self._log(f"Warning: Component {component_name} was not started")
            return 0.0

        elapsed = time.time() - self.component_times[component_name]
        self._log(f"Component initialized: {component_name} - {elapsed:.3f}s")

        # Remove the start time to avoid reusing it
        del self.component_times[component_name]

        return elapsed

    def finish(self) -> float:
        """
        Finish timing the application startup and log the results.

        Returns:
            The total time taken for the application to start.
        """
        total_time = time.time() - self.start_time

        self._log(f"Application startup completed in {total_time:.3f} seconds")
        self._log("Startup timing summary:")

        # Log all checkpoints
        last_time = self.start_time
        for name, checkpoint_time in self.checkpoints:
            elapsed = checkpoint_time - last_time
            self._log(f"  {name}: {elapsed:.3f}s")
            last_time = checkpoint_time

        # Write all log entries to file
        try:
            with open(self.log_file, 'w') as f:
                for entry in self.log_entries:
                    f.write(f"{entry}\n")
            print(f"Startup timing log written to {os.path.abspath(self.log_file)}")
        except Exception as e:
            print(f"Error writing startup timing log to {self.log_file}: {e}")

        return total_time
