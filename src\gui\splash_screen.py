"""
Enhanced splash screen for the VisionLab Ai application with animations and modern design.
"""

from PySide6.QtWidgets import (QSplashScreen, QProgressBar, QVBoxLayout, QLabel,
                              QWidget, QGraphicsOpacityEffect, QHBoxLayout, QPushButton)
from PySide6.QtGui import QPixmap, QColor, QPainter, QLinearGradient, QBrush, QPen
from PySide6.QtCore import Qt, QTimer, QPropertyAnimation, QEasingCurve
from pathlib import Path

class AnimatedLabel(QLabel):
    """A label with fade-in animation."""

    def __init__(self, text, parent=None, font_size=32, color="#0078d4", bold=True, delay=0):
        super().__init__(text, parent)

        # Set font and color using a single stylesheet for consistency
        style_parts = [f"color: {color};", f"font-size: {font_size}px;"]
        if bold:
            style_parts.append("font-weight: bold;")
        else:
            style_parts.append("font-weight: normal;")
        self.setStyleSheet(" ".join(style_parts))


        # Set up opacity effect for animation
        self.opacity_effect = QGraphicsOpacityEffect(self)
        self.opacity_effect.setOpacity(0)
        self.setGraphicsEffect(self.opacity_effect)

        # Set up animation
        self.animation = QPropertyAnimation(self.opacity_effect, b"opacity")
        self.animation.setDuration(1000)
        self.animation.setStartValue(0)
        self.animation.setEndValue(1)
        self.animation.setEasingCurve(QEasingCurve.InOutQuad)

        # Start animation after delay
        if delay > 0:
            QTimer.singleShot(delay, self.animation.start)
        else:
            self.animation.start()


class ModernProgressBar(QProgressBar):
    """A modern-looking progress bar with animations."""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setTextVisible(False)
        self.setRange(0, 100)
        self.setValue(0)
        self.setFixedHeight(6)
        self.setStyleSheet("""
            QProgressBar {
                background-color: #1e1e1e;
                border: none;
                border-radius: 5px;
            }
            QProgressBar::chunk {
                background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:0,
                                                 stop:0 #00c6ff, stop:1 #0072ff);
                border-radius: 5px;
            }
        """)

        # Set up animation
        self.animation = QPropertyAnimation(self, b"value")
        self.animation.setDuration(500)
        self.animation.setEasingCurve(QEasingCurve.InOutQuad)

    def animateValue(self, value):
        """Animate the progress bar to the given value."""
        self.animation.setStartValue(self.value())
        self.animation.setEndValue(value)
        self.animation.start()


class AnimatedLogoWidget(QWidget):
    """A custom widget that displays an image logo with shimmer."""

    def __init__(self, image_path, parent=None):
        super().__init__(parent)
        self.shimmer_pos = 0
        self.timer = QTimer(self)
        self.timer.timeout.connect(self.update_shimmer)
        self.timer.start(30)  # 30 ms for smooth animation

        self.image_label = QLabel(self)
        pixmap = QPixmap(image_path)
        if not pixmap.isNull():
            # Scale pixmap to fit the widget size (80x80 as set later)
            scaled_pixmap = pixmap.scaled(80, 80, Qt.KeepAspectRatio, Qt.SmoothTransformation)
            self.image_label.setPixmap(scaled_pixmap)
            self.image_label.setAlignment(Qt.AlignCenter)
        else:
            self.image_label.setText("Logo not found") # Fallback text
            self.image_label.setAlignment(Qt.AlignCenter)
            self.image_label.setStyleSheet("color: red;")

        # Layout to center the image label
        layout = QVBoxLayout(self)
        layout.addWidget(self.image_label)
        layout.setContentsMargins(0, 0, 0, 0)
        self.setLayout(layout)

    def update_shimmer(self):
        # Shimmer effect is applied over the widget, not the image itself
        self.shimmer_pos = (self.shimmer_pos + 4) % (self.width() + 40) # Adjust shimmer range based on widget width
        self.update()

    def paintEvent(self, event):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)

        # Draw shimmer effect over the image label
        gradient = QLinearGradient(self.shimmer_pos - 40, 0, self.shimmer_pos, self.height())
        gradient.setColorAt(0, QColor(255, 255, 255, 0))
        gradient.setColorAt(0.5, QColor(255, 255, 255, 80))
        gradient.setColorAt(1, QColor(255, 255, 255, 0))
        painter.setBrush(QBrush(gradient))
        painter.setPen(Qt.NoPen)
        painter.drawRect(self.rect())

class VisionLabAiSplashScreen(QSplashScreen):
    """Modern splash screen with animations for VisionLab Ai application."""

    def __init__(self):
        # Create a pixmap for the splash screen
        pixmap = QPixmap(600, 400)
        pixmap.fill(QColor(30, 30, 30))  # Dark background

        super().__init__(pixmap)

        # Create a widget to hold the layout
        self.splash_widget = QWidget(self)
        self.splash_widget.setGeometry(0, 0, 600, 400)

        # Variables for dragging the window
        self.dragging = False
        self.drag_position = None

        # Create a layout for the splash screen
        main_layout = QVBoxLayout(self.splash_widget)
        main_layout.setContentsMargins(40, 40, 40, 40)
        main_layout.setSpacing(10)

        # Add logo/header section
        header_layout = QHBoxLayout()

        # Create a custom logo widget using the provided image path
        logo_image_path = Path(__file__).parent / "icons" / "logo.png"
        self.logo_widget = AnimatedLogoWidget(str(logo_image_path))
        self.logo_widget.setFixedSize(80, 80)
        header_layout.addWidget(self.logo_widget)

        # Add title section
        title_layout = QVBoxLayout()
        title_layout.setSpacing(0)

        # Add title label with animation
        self.title_label = AnimatedLabel("VisionLab Ai", font_size=28, color="#0078d4", delay=0)
        title_layout.addWidget(self.title_label)

        # Add subtitle label with animation and improved styling
        self.subtitle_label = AnimatedLabel(
            "Advanced Image Analysis",
            font_size=20,
            color="#e0e0e0",
            bold=True,
            delay=0
        )
        title_layout.addWidget(self.subtitle_label)

        header_layout.addLayout(title_layout)
        header_layout.addStretch()

        main_layout.addLayout(header_layout)

        # Add spacer
        main_layout.addSpacing(28)

        # Add features section with animations
        features_layout = QVBoxLayout()
        features_layout.setSpacing(25)

        feature_items = [
            "🔬 Advanced Image Analysis",
            "🧠 AI-Powered Segmentation",
            "🎯 Unsupervised Segmentation",
            "🔄 Object detection and segmentation"
        ]

        self.feature_labels = []
        for i, feature in enumerate(feature_items):
            label = AnimatedLabel(feature, font_size=12, color="#cccccc",
                                 bold=False, delay=1000 + i*300)
            # Add vertical padding and min-height for better separation
            label.setStyleSheet("color: #cccccc; padding: 6px 0; min-height: 28px;")
            features_layout.addWidget(label)
            self.feature_labels.append(label)

        main_layout.addLayout(features_layout)

        # Add spacer
        main_layout.addSpacing(20)

        # Add status section
        status_layout = QVBoxLayout()

        # Add status label
        self.status_label = QLabel("Initializing...")
        self.status_label.setStyleSheet("color: #a0a0a0; font-size: 12px;")
        status_layout.addWidget(self.status_label)

        # Add progress bar
        self.progress_bar = ModernProgressBar()
        status_layout.addWidget(self.progress_bar)

        main_layout.addLayout(status_layout)

        # Add version label and control buttons
        bottom_layout = QHBoxLayout()

        # Add version label
        self.version_label = QLabel("Version 1.0")
        self.version_label.setStyleSheet("color: #707070; font-size: 10px;")
        bottom_layout.addWidget(self.version_label)

        # Add spacer to push buttons to the right
        bottom_layout.addStretch()

        # Add minimize button
        self.minimize_button = QPushButton("_")
        self.minimize_button.setFixedSize(24, 24)
        self.minimize_button.setStyleSheet("""
            QPushButton {
                background-color: #2d2d2d;
                color: #a0a0a0;
                border: none;
                border-radius: 3px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #3d3d3d;
                color: #ffffff;
            }
            QPushButton:pressed {
                background-color: #0078d4;
            }
        """)
        self.minimize_button.clicked.connect(self.showMinimized)
        bottom_layout.addWidget(self.minimize_button)

        # Add close button
        self.close_button = QPushButton("×")
        self.close_button.setFixedSize(24, 24)
        self.close_button.setStyleSheet("""
            QPushButton {
                background-color: #2d2d2d;
                color: #a0a0a0;
                border: none;
                border-radius: 3px;
                font-weight: bold;
                font-size: 16px;
            }
            QPushButton:hover {
                background-color: #e81123;
                color: #ffffff;
            }
            QPushButton:pressed {
                background-color: #f1707a;
            }
        """)
        self.close_button.clicked.connect(self.handle_close)
        bottom_layout.addWidget(self.close_button)

        main_layout.addLayout(bottom_layout)

        # Set window flags - remove WindowStaysOnTopHint to prevent blocking
        self.setWindowFlags(Qt.FramelessWindowHint)

        # Add drop shadow effect to the entire splash screen
        self.setStyleSheet("""
            QSplashScreen {
                background-color: #1e1e1e;
                border: 1px solid #3a3a3a;
                border-radius: 10px;
            }
        """)

        # Start the animation sequence
        QTimer.singleShot(100, self.start_animations)

    def start_animations(self):
        """Start the initial animations."""
        # Animate the progress bar to 10% to show initial progress
        self.progress_bar.animateValue(10)

    def update_progress(self, value, message):
        """Update the progress bar and status message."""
        self.progress_bar.animateValue(value)
        self.status_label.setStyleSheet("font-size: 12pt; font-weight: bold; color: #00c6ff;")
        self.status_label.setText(message)
        self.repaint()  # Force repaint to update the splash screen

    def handle_close(self):
        """Handle the close button click."""
        # Hide the splash screen but don't destroy it
        # This allows the application to continue loading
        self.hide()

        # Reset dragging state to prevent issues
        self.dragging = False
        self.drag_position = None

    def paintEvent(self, event):
        """Override paint event to add custom drawing."""
        super().paintEvent(event)

        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)

        # Draw a subtle gradient overlay
        gradient = QLinearGradient(0, 0, self.width(), self.height())
        gradient.setColorAt(0, QColor(30, 30, 30, 0))
        gradient.setColorAt(1, QColor(0, 120, 212, 30))
        painter.fillRect(self.rect(), QBrush(gradient))

        # Draw a highlight line at the top
        pen = QPen(QColor(0, 120, 212, 100))
        pen.setWidth(2)
        painter.setPen(pen)
        painter.drawLine(0, 0, self.width(), 0)

    def mousePressEvent(self, event):
        """Handle mouse press events for dragging."""
        # Call the parent class implementation first to maintain normal behavior
        super().mousePressEvent(event)

        # Then handle our custom dragging logic
        if event.button() == Qt.LeftButton:
            # Only enable dragging if we're not clicking on a button
            widget_at_pos = self.childAt(event.position().toPoint())
            if not isinstance(widget_at_pos, QPushButton):
                self.dragging = True
                self.drag_position = event.globalPosition().toPoint() - self.frameGeometry().topLeft()
                event.accept()

    def mouseMoveEvent(self, event):
        """Handle mouse move events for dragging."""
        # Call the parent class implementation first
        super().mouseMoveEvent(event)

        # Then handle our custom dragging logic
        if self.dragging and event.buttons() & Qt.LeftButton:
            self.move(event.globalPosition().toPoint() - self.drag_position)
            event.accept()

    def mouseReleaseEvent(self, event):
        """Handle mouse release events for dragging."""
        # Call the parent class implementation first
        super().mouseReleaseEvent(event)

        # Then handle our custom dragging logic
        if event.button() == Qt.LeftButton:
            self.dragging = False
            event.accept()
