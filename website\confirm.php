<?php
// Email confirmation page for waitlist signups

// Database configuration
$db_config = [
    'host' => 'localhost',
    'dbname' => 'visionlab_waitlist',
    'username' => 'your_db_username',
    'password' => 'your_db_password'
];

$message = '';
$status = 'error';
$title = 'Email Confirmation';

try {
    // Get token from URL
    $token = $_GET['token'] ?? '';
    
    if (empty($token)) {
        throw new Exception('No confirmation token provided.');
    }
    
    // Connect to database
    $pdo = new PDO(
        "mysql:host={$db_config['host']};dbname={$db_config['dbname']};charset=utf8mb4",
        $db_config['username'],
        $db_config['password'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false
        ]
    );
    
    // Check if token exists and is not already confirmed
    $stmt = $pdo->prepare('SELECT id, first_name, email, confirmed FROM waitlist WHERE token = ?');
    $stmt->execute([$token]);
    $user = $stmt->fetch();
    
    if (!$user) {
        throw new Exception('Invalid confirmation token.');
    }
    
    if ($user['confirmed']) {
        $status = 'already_confirmed';
        $message = 'Your email has already been confirmed. Thank you!';
        $title = 'Already Confirmed';
    } else {
        // Confirm the email
        $stmt = $pdo->prepare('UPDATE waitlist SET confirmed = TRUE, confirmed_at = NOW() WHERE token = ?');
        $stmt->execute([$token]);
        
        $status = 'success';
        $message = 'Thank you, ' . htmlspecialchars($user['first_name']) . '! Your email has been confirmed successfully.';
        $title = 'Email Confirmed';
        
        // Log the confirmation
        try {
            $stmt = $pdo->prepare('INSERT INTO email_logs (recipient_email, email_type, subject, status, related_id) VALUES (?, ?, ?, ?, ?)');
            $stmt->execute([$user['email'], 'waitlist_confirmation', 'Email Confirmed', 'sent', $user['id']]);
        } catch (Exception $e) {
            // Don't fail if logging fails
            error_log('Failed to log email confirmation: ' . $e->getMessage());
        }
    }
    
} catch (PDOException $e) {
    error_log('Database error in confirmation: ' . $e->getMessage());
    $message = 'A database error occurred. Please try again later.';
} catch (Exception $e) {
    $message = $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($title); ?> - VisionLab AI</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            max-width: 500px;
            width: 100%;
            padding: 3rem 2rem;
            text-align: center;
            animation: slideIn 0.5s ease-out;
        }
        
        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .icon {
            font-size: 4rem;
            margin-bottom: 1.5rem;
        }
        
        .icon.success {
            color: #10b981;
        }
        
        .icon.already-confirmed {
            color: #3b82f6;
        }
        
        .icon.error {
            color: #ef4444;
        }
        
        h1 {
            font-size: 1.8rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #1e293b;
        }
        
        .message {
            font-size: 1.1rem;
            color: #64748b;
            margin-bottom: 2rem;
            line-height: 1.6;
        }
        
        .actions {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }
        
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 12px 24px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            font-size: 1rem;
        }
        
        .btn i {
            margin-right: 8px;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #2563eb, #3b82f6);
            color: white;
            box-shadow: 0 4px 15px rgba(37, 99, 235, 0.3);
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(37, 99, 235, 0.4);
        }
        
        .btn-secondary {
            background: white;
            color: #2563eb;
            border: 2px solid #2563eb;
        }
        
        .btn-secondary:hover {
            background: #2563eb;
            color: white;
            transform: translateY(-2px);
        }
        
        .footer {
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid #e2e8f0;
            font-size: 0.9rem;
            color: #64748b;
        }
        
        .logo {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 2rem;
            font-size: 1.5rem;
            font-weight: 700;
            color: #2563eb;
        }
        
        .logo i {
            margin-right: 10px;
            font-size: 1.8rem;
        }
        
        @media (max-width: 480px) {
            .container {
                padding: 2rem 1.5rem;
            }
            
            h1 {
                font-size: 1.5rem;
            }
            
            .message {
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">
            <i class="fas fa-eye"></i>
            <span>VisionLab AI</span>
        </div>
        
        <?php if ($status === 'success'): ?>
            <div class="icon success">
                <i class="fas fa-check-circle"></i>
            </div>
            <h1>Email Confirmed!</h1>
            <p class="message"><?php echo htmlspecialchars($message); ?></p>
            <div class="actions">
                <a href="index.html" class="btn btn-primary">
                    <i class="fas fa-home"></i>
                    Return to Homepage
                </a>
                <a href="index.html#features" class="btn btn-secondary">
                    <i class="fas fa-star"></i>
                    Explore Features
                </a>
            </div>
            <div class="footer">
                <p>You're now confirmed on our waitlist! We'll notify you as soon as VisionLab AI becomes available.</p>
            </div>
            
        <?php elseif ($status === 'already_confirmed'): ?>
            <div class="icon already-confirmed">
                <i class="fas fa-info-circle"></i>
            </div>
            <h1>Already Confirmed</h1>
            <p class="message"><?php echo htmlspecialchars($message); ?></p>
            <div class="actions">
                <a href="index.html" class="btn btn-primary">
                    <i class="fas fa-home"></i>
                    Return to Homepage
                </a>
                <a href="index.html#timeline" class="btn btn-secondary">
                    <i class="fas fa-calendar"></i>
                    View Timeline
                </a>
            </div>
            <div class="footer">
                <p>You're already on our confirmed waitlist. We'll keep you updated on our progress!</p>
            </div>
            
        <?php else: ?>
            <div class="icon error">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <h1>Confirmation Error</h1>
            <p class="message"><?php echo htmlspecialchars($message); ?></p>
            <div class="actions">
                <a href="index.html" class="btn btn-primary">
                    <i class="fas fa-home"></i>
                    Return to Homepage
                </a>
                <a href="index.html#waitlist" class="btn btn-secondary">
                    <i class="fas fa-redo"></i>
                    Try Again
                </a>
            </div>
            <div class="footer">
                <p>If you continue to have issues, please contact us at <a href="mailto:<EMAIL>" style="color: #2563eb;"><EMAIL></a></p>
            </div>
        <?php endif; ?>
    </div>
    
    <script>
        // Add some interactive effects
        document.addEventListener('DOMContentLoaded', function() {
            const container = document.querySelector('.container');
            const buttons = document.querySelectorAll('.btn');
            
            // Add hover effects to buttons
            buttons.forEach(button => {
                button.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px)';
                });
                
                button.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });
            
            // Add click tracking (optional)
            buttons.forEach(button => {
                button.addEventListener('click', function() {
                    // Track button clicks if analytics are implemented
                    if (typeof gtag !== 'undefined') {
                        gtag('event', 'click', {
                            'event_category': 'confirmation_page',
                            'event_label': this.textContent.trim()
                        });
                    }
                });
            });
        });
    </script>
</body>
</html>