#!/usr/bin/env python3
"""
Test script to verify that the state restoration fix works correctly.
This script tests that annotations are properly loaded when displaying images.
"""

import sys
import os
import tempfile
import shutil
from pathlib import Path

# Add the src directory to the Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from core.grainsight_project import VisionLabProject
from core.optimized_state_manager import OptimizedGrainStateManager
import numpy as np
import pandas as pd

def test_state_restoration():
    """Test that state restoration properly loads annotations."""
    print("Testing state restoration fix...")
    
    # Create a temporary project
    with tempfile.TemporaryDirectory() as temp_dir:
        project_file = os.path.join(temp_dir, "test_project.vlp")
        project = VisionLabProject()
        
        # Create some test data
        test_image_id = "test_image_001"
        test_annotations = [np.random.rand(100, 100) for _ in range(5)]  # 5 random masks
        test_df = pd.DataFrame({
            'grain_id': range(5),
            'area': [100, 200, 150, 300, 250],
            'perimeter': [40, 60, 50, 80, 70]
        })
        
        test_state = {
            'annotations': test_annotations,
            'df': test_df,
            'scale_value': 1.5,
            'scale_unit': 'μm',
            'has_results': True
        }
        
        # Save the state
        print(f"Saving test state with {len(test_annotations)} annotations...")
        success = project.save_grain_analysis_state(test_image_id, test_state)
        if not success:
            print("❌ Failed to save test state")
            return False
        
        # Test loading without annotations (lazy loading)
        print("Testing lazy loading (load_annotations=False)...")
        loaded_state_lazy = project.load_grain_analysis_state(test_image_id, load_annotations=False)
        
        if not loaded_state_lazy:
            print("❌ Failed to load state with lazy loading")
            return False
            
        # Check that annotations_path exists but annotations don't
        if project.use_optimized_state_management:
            if 'annotations_path' not in loaded_state_lazy:
                print("❌ annotations_path not found in lazy-loaded state")
                return False
            if 'annotations' in loaded_state_lazy:
                print("❌ annotations should not be loaded in lazy mode")
                return False
            print("✅ Lazy loading working correctly - annotations_path present, annotations not loaded")
        else:
            print("ℹ️ Using legacy state management")
        
        # Test loading with annotations (eager loading)
        print("Testing eager loading (load_annotations=True)...")
        loaded_state_eager = project.load_grain_analysis_state(test_image_id, load_annotations=True)
        
        if not loaded_state_eager:
            print("❌ Failed to load state with eager loading")
            return False
            
        # Check that annotations are loaded
        if 'annotations' not in loaded_state_eager:
            print("❌ annotations not found in eager-loaded state")
            return False
            
        if len(loaded_state_eager['annotations']) != len(test_annotations):
            print(f"❌ Wrong number of annotations loaded: expected {len(test_annotations)}, got {len(loaded_state_eager['annotations'])}")
            return False
            
        print(f"✅ Eager loading working correctly - {len(loaded_state_eager['annotations'])} annotations loaded")
        
        # Test that DataFrame is loaded correctly in both cases
        for state_name, state in [("lazy", loaded_state_lazy), ("eager", loaded_state_eager)]:
            if 'df' not in state or state['df'] is None:
                print(f"❌ DataFrame not found in {state_name}-loaded state")
                return False
            if len(state['df']) != len(test_df):
                print(f"❌ Wrong DataFrame size in {state_name}-loaded state: expected {len(test_df)}, got {len(state['df'])}")
                return False
            print(f"✅ DataFrame loaded correctly in {state_name} mode - {len(state['df'])} rows")
        
        print("\n🎉 All state restoration tests passed!")
        print("\nThe fix ensures that:")
        print("1. load_grain_analysis_state_from_project() now calls load_annotations=True")
        print("2. Annotations are properly loaded when displaying images")
        print("3. Lazy loading still works for performance when load_annotations=False")
        print("4. toggle_grain_visualization() can load annotations on demand")
        
        return True

if __name__ == "__main__":
    try:
        success = test_state_restoration()
        if success:
            print("\n✅ State restoration fix verification completed successfully!")
        else:
            print("\n❌ State restoration fix verification failed!")
            sys.exit(1)
    except Exception as e:
        print(f"\n❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
    finally:
        # Clean up any test files
        pass