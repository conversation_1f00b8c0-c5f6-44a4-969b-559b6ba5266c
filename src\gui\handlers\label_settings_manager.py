import os
import json
import numpy as np

class LabelSettingsManager:
    """
    Class to manage label settings (names and colors) for trainable segmentation.
    Handles saving and loading settings to/from a JSON file per project.
    """

    def __init__(self):
        self.label_names = {}
        self.label_colors = {}
        self.settings_file_path = None

    def set_project(self, project):
        """Set the project and initialize the settings file path."""
        if project:
            try:
                project_file = project.project_file
                project_dir = os.path.dirname(project_file)
                project_name = project.name

                print(f"DEBUG: Setting up label settings for project: {project_name}")
                print(f"DEBUG: Project file: {project_file}")
                print(f"DEBUG: Project directory: {project_dir}")

                # Create the settings directory if it doesn't exist
                settings_dir = os.path.join(project_dir, f"{project_name}_data", "state", "trainable_segmentation")
                try:
                    os.makedirs(settings_dir, exist_ok=True)
                    print(f"DEBUG: Created settings directory: {settings_dir}")
                except Exception as e:
                    print(f"ERROR: Failed to create settings directory: {e}")

                    # Try an alternative approach
                    try:
                        # Create each directory level separately
                        data_dir = os.path.join(project_dir, f"{project_name}_data")
                        if not os.path.exists(data_dir):
                            os.mkdir(data_dir)
                            print(f"DEBUG: Created data directory: {data_dir}")

                        state_dir = os.path.join(data_dir, "state")
                        if not os.path.exists(state_dir):
                            os.mkdir(state_dir)
                            print(f"DEBUG: Created state directory: {state_dir}")

                        trainable_dir = os.path.join(state_dir, "trainable_segmentation")
                        if not os.path.exists(trainable_dir):
                            os.mkdir(trainable_dir)
                            print(f"DEBUG: Created trainable_segmentation directory: {trainable_dir}")
                    except Exception as e2:
                        print(f"ERROR: Failed to create directories step by step: {e2}")

                # Set the settings file path
                self.settings_file_path = os.path.join(settings_dir, "label_settings.json")
                print(f"DEBUG: Label settings file path set to {self.settings_file_path}")

                # Load settings if the file exists
                self.load_settings()
            except Exception as e:
                print(f"ERROR: Failed to set project for label settings manager: {e}")
                import traceback
                traceback.print_exc()

    def update_label_names(self, label_names):
        """Update label names and save to file."""
        # Convert to dictionary if it's a list
        if isinstance(label_names, list):
            self.label_names = {i+1: name for i, name in enumerate(label_names)}
        else:
            self.label_names = label_names

        print(f"DEBUG: Updated label names: {self.label_names}")
        self.save_settings()

    def update_label_colors(self, label_colors):
        """Update label colors and save to file."""
        # Convert numpy array to list of lists for JSON serialization
        if isinstance(label_colors, np.ndarray):
            # Create a dictionary with 1-indexed keys
            colors_dict = {}
            for i, color in enumerate(label_colors):
                # Convert numpy array to list
                if isinstance(color, np.ndarray):
                    color = color.tolist()
                colors_dict[i+1] = color
            self.label_colors = colors_dict
        elif isinstance(label_colors, list):
            # Create a dictionary with 1-indexed keys
            colors_dict = {}
            for i, color in enumerate(label_colors):
                # Convert numpy array to list
                if isinstance(color, np.ndarray):
                    color = color.tolist()
                colors_dict[i+1] = color
            self.label_colors = colors_dict
        else:
            # Ensure all values are lists, not numpy arrays
            colors_dict = {}
            for key, color in label_colors.items():
                if isinstance(color, np.ndarray):
                    color = color.tolist()
                colors_dict[key] = color
            self.label_colors = colors_dict

        print(f"DEBUG: Updated label colors: {self.label_colors}")
        self.save_settings()

    def update_label(self, label_index, name=None, color=None):
        """Update a specific label's name and/or color."""
        if name is not None:
            self.label_names[label_index] = name
            print(f"DEBUG: Updated label {label_index} name to {name}")

        if color is not None:
            # Convert numpy array to list for JSON serialization
            if isinstance(color, np.ndarray):
                color = color.tolist()
            self.label_colors[label_index] = color
            print(f"DEBUG: Updated label {label_index} color to {color}")

        self.save_settings()

    def add_label(self, label_index, name, color):
        """Add a new label with name and color."""
        # Convert numpy array to list for JSON serialization
        if isinstance(color, np.ndarray):
            color = color.tolist()

        self.label_names[label_index] = name
        self.label_colors[label_index] = color
        print(f"DEBUG: Added new label {label_index}: {name} with color {color}")

        self.save_settings()

    def remove_label(self, label_index):
        """Remove a label."""
        if label_index in self.label_names:
            del self.label_names[label_index]
            print(f"DEBUG: Removed label {label_index} name")

        if label_index in self.label_colors:
            del self.label_colors[label_index]
            print(f"DEBUG: Removed label {label_index} color")

        self.save_settings()

    def get_label_names(self):
        """Get the label names."""
        return self.label_names

    def get_label_colors(self):
        """Get the label colors."""
        return self.label_colors

    def save_settings(self):
        """
        Save settings to JSON file.

        Note: This method is intentionally disabled to ensure label settings
        are not persisted between sessions.
        """
        print(f"DEBUG: Label settings persistence is disabled. Settings not saved.")
        return False

    def load_settings(self):
        """
        Load settings from JSON file.

        Note: This method is intentionally disabled to ensure label settings
        are not persisted between sessions. Default label settings will be used instead.
        """
        print(f"DEBUG: Label settings persistence is disabled. Using default settings.")
        return False
