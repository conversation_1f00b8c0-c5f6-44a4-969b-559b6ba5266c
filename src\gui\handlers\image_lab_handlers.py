# src/gui/handlers/image_lab_handlers.py

import os
import cv2
import numpy as np
import matplotlib.pyplot as plt
import logging
import datetime
from PySide6.QtWidgets import QMessageBox, QFileDialog, QInputDialog
from PySide6.QtGui import QPixmap, QImage
from PySide6.QtCore import Qt, Slot
import tempfile
import uuid

from src.utils.image_utils import resize_image, convert_cvimage_to_qpixmap, convert_cvimage_to_qimage
from src.gui.crop_dialog import CropDialog
from src.gui.template_dialog import TemplateDialog
from src.image_lab.template_manager import TemplateManager
from src.gui.widgets.batch_progress_dialog import BatchProgressDialog

logger = logging.getLogger(__name__)

class ImageLabHandlers:
    """Handlers for the Image Lab page functionality."""

    def __init__(self, ui, project=None):
        """Initialize the Image Lab handlers.

        Args:
            ui: The UI instance for the Image Lab page
            project: The current project (optional)
        """
        self.ui = ui
        self.project = project

        # Initialize independent image variables
        self.original_images = {}  # Dictionary to store original images by path
        self.processed_images = {}  # Dictionary to store processed images by path
        self.current_image_path = None
        self.common_size = (750, 750)  # Default display size

        # Initialize image history stack for undo functionality
        self.image_history = {}  # Dictionary to store history for each image
        self.max_history_size = 10  # Maximum number of history states to keep

        # Initialize operation templates
        self.current_operations = []  # List to store current operations

        # Track which templates have been applied to which images
        self.applied_templates = {}  # Dictionary to store {image_path: [template_name1, template_name2, ...]}

        # Initialize template manager
        self._init_template_manager(project)

        # Connect UI signals to handlers
        self._connect_signals()

    def _init_template_manager(self, project=None):
        """Initialize the template manager with the appropriate state directory.

        Args:
            project: The current project (optional)
        """
        state_dir = None
        if project:
            # Get project directory
            if hasattr(project, 'project_dir'):
                # Regular Project
                project_dir = project.project_dir
            elif hasattr(project, 'project_file'):
                # GrainSightProject
                project_dir = os.path.dirname(project.project_file)
            else:
                # Fallback to a default directory
                project_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))),
                                         'state')
                logger.warning(f"Could not determine project directory. Using default: {project_dir}")

            # Create state directory path
            state_dir = os.path.join(project_dir, 'state', 'image_lab_templates')

            # Ensure the directory exists
            try:
                os.makedirs(state_dir, exist_ok=True)
                logger.info(f"Created template directory: {state_dir}")
            except Exception as e:
                logger.error(f"Error creating template directory: {str(e)}")
                state_dir = None

        # Initialize template manager
        self.template_manager = TemplateManager(state_dir)
        logger.info(f"Template manager initialized with directory: {self.template_manager.state_dir}")

    def _connect_signals(self):
        """Connect UI signals to their respective handlers."""
        # Gallery signals
        self.ui.image_lab_gallery.image_clicked.connect(self.on_gallery_image_clicked)
        self.ui.image_lab_gallery.remove_clicked.connect(self.on_image_lab_gallery_remove_clicked)
        
        # Clear gallery button
        if hasattr(self.ui, 'clear_image_lab_gallery_button'):
            self.ui.clear_image_lab_gallery_button.clicked.connect(self.clear_image_lab_gallery)

        # Enhancement buttons
        self.ui.hist_equal_btn.clicked.connect(self.apply_histogram_equalization)
        self.ui.auto_contrast_btn.clicked.connect(self.apply_auto_contrast)
        self.ui.apply_gamma_btn.clicked.connect(self.apply_gamma_correction)
        self.ui.reset_image_btn.clicked.connect(self.reset_image)

        # Brightness and contrast sliders
        self.ui.brightness_slider.valueChanged.connect(self.update_brightness_contrast)
        self.ui.contrast_slider.valueChanged.connect(self.update_brightness_contrast)

        # Morphological operations
        self.ui.apply_morph_btn.clicked.connect(self.apply_morphological_operation)

        # Filters
        self.ui.apply_filter_btn.clicked.connect(self.apply_filter)

        # Thresholding
        self.ui.threshold_value.valueChanged.connect(self.update_threshold_value_label)
        self.ui.apply_threshold_btn.clicked.connect(self.apply_threshold)

        # Color Manipulation
        self.ui.apply_color_space_btn.clicked.connect(self.apply_color_space_conversion)
        self.ui.apply_channel_op_btn.clicked.connect(self.apply_channel_operation)

        # Edge Detection
        self.ui.canny_threshold1.valueChanged.connect(self.update_canny_threshold_labels)
        self.ui.canny_threshold2.valueChanged.connect(self.update_canny_threshold_labels)
        self.ui.apply_edge_btn.clicked.connect(self.apply_edge_detection)

        # Batch Processing
        self.ui.save_template_btn.clicked.connect(self.save_operations_template)
        self.ui.load_template_btn.clicked.connect(self.load_operations_template)
        self.ui.manage_templates_btn.clicked.connect(self.manage_templates)
        self.ui.apply_selected_btn.clicked.connect(self.apply_to_selected)
        self.ui.apply_all_btn.clicked.connect(self.apply_to_all)
        self.ui.export_all_btn.clicked.connect(self.export_all_processed_images)
        self.ui.template_combo.currentIndexChanged.connect(self.on_template_selected)

        # Crop and save
        self.ui.crop_button.clicked.connect(self.open_crop_dialog)
        self.ui.apply_edits_button.clicked.connect(self.apply_edits)
        self.ui.undo_button.clicked.connect(self.undo_last_operation)
        
        # Rotation and flip operations
        self.ui.rotate_left_btn.clicked.connect(self.rotate_left)
        self.ui.rotate_right_btn.clicked.connect(self.rotate_right)
        self.ui.flip_horizontal_btn.clicked.connect(self.flip_horizontal)
        self.ui.flip_vertical_btn.clicked.connect(self.flip_vertical)

        # Connect all section-specific undo buttons to the same undo method
        self.ui.enhance_undo_btn.clicked.connect(self.undo_last_operation)
        self.ui.morph_undo_btn.clicked.connect(self.undo_last_operation)
        self.ui.filter_undo_btn.clicked.connect(self.undo_last_operation)
        self.ui.threshold_undo_btn.clicked.connect(self.undo_last_operation)
        self.ui.color_undo_btn.clicked.connect(self.undo_last_operation)
        self.ui.edge_undo_btn.clicked.connect(self.undo_last_operation)

    def set_project(self, project):
        """Set the current project.

        Args:
            project: The project to set
        """
        self.project = project

        # Update template manager with project state directory
        self._init_template_manager(project)

        # Update template combo box
        self._update_template_combo()

        logger.info(f"Set project for Image Lab page: {project.name if project else 'None'}")

    def load_images(self, image_paths, image_infos=None):
        """Load images into the Image Lab page.

        Args:
            image_paths: List of image paths to load
            image_infos: List of image info objects (optional)
        """
        if not image_paths:
            logger.warning("No image paths provided to load")
            return

        # Check for existing images in the gallery
        existing_paths = set(self.ui.image_lab_gallery.file_paths) if hasattr(self.ui.image_lab_gallery, 'file_paths') else set()

        # Add new images, skipping duplicates
        added_count = 0
        for i, image_path in enumerate(image_paths):
            if image_path in existing_paths:
                logger.info(f"Skipping duplicate image: {image_path}")
                continue

            try:
                # Load image
                image = cv2.imdecode(np.fromfile(image_path, dtype=np.uint8), cv2.IMREAD_COLOR)
                if image is None:
                    logger.warning(f"Failed to load image: {image_path}")
                    continue

                # Convert to RGB
                image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

                # Store original image
                self.original_images[image_path] = image.copy()

                # Initialize processed image as a copy of the original
                self.processed_images[image_path] = image.copy()

                # Initialize history for this image
                self.image_history[image_path] = []

                # Create thumbnail and add to gallery
                thumbnail = convert_cvimage_to_qpixmap(image, already_rgb=True)
                filename = os.path.basename(image_path)
                # The correct order for add_image is: image, filename, file_path
                self.ui.image_lab_gallery.add_image(thumbnail, filename, image_path)

                added_count += 1
                logger.info(f"Added image to Image Lab gallery: {filename}")

            except Exception as e:
                logger.error(f"Error loading image {image_path}: {str(e)}")

        # Select first image if we just loaded the gallery
        if added_count > 0 and len(self.ui.image_lab_gallery.images) == added_count:
            self.ui.image_lab_gallery.select_image(0)
            self.on_gallery_image_clicked(0)

        logger.info(f"Loaded {added_count} new images into Image Lab gallery")

    def on_gallery_image_clicked(self, index):
        """Handle image selection in the gallery.

        Args:
            index: Index of the selected image
        """
        if index < 0 or index >= len(self.ui.image_lab_gallery.images):
            logger.warning(f"Invalid image index: {index}")
            return

        # Get the image path
        image_path = self.ui.image_lab_gallery.file_paths[index]
        self.current_image_path = image_path

        # Display the original and processed images
        self._update_image_display()

        # Update image info
        self._update_image_info()

        # Clear operations list when switching images
        self._clear_operations()

        logger.info(f"Selected image: {os.path.basename(image_path)}")

    @Slot(int)
    def on_image_lab_gallery_remove_clicked(self, index):
        """Handler for when a thumbnail X button is clicked in image lab gallery."""
        if not hasattr(self, 'ui') or not hasattr(self.ui, 'image_lab_gallery') or not (0 <= index < len(self.ui.image_lab_gallery.file_paths)):
            return
        
        image_path = self.ui.image_lab_gallery.file_paths[index]
        filename = os.path.basename(image_path)
        
        # Confirm removal
        from PySide6.QtWidgets import QMessageBox
        reply = QMessageBox.question(
            self.ui, "Confirm Removal",
            f"Remove this image from the image lab gallery?\n{filename}",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            # Manually remove from gallery UI without triggering the signal again
            if 0 <= index < len(self.ui.image_lab_gallery.images):
                # Store the current selection state
                was_selected = (self.ui.image_lab_gallery.selected_index == index)
                
                # Remove the thumbnail widget
                if index < len(self.ui.image_lab_gallery.thumbnails):
                    thumbnail = self.ui.image_lab_gallery.thumbnails.pop(index)
                    thumbnail.deleteLater()
                
                # Remove the image data
                self.ui.image_lab_gallery.images.pop(index)
                self.ui.image_lab_gallery.filenames.pop(index)
                self.ui.image_lab_gallery.file_paths.pop(index)
                
                # Update the selected index
                if was_selected:
                    if len(self.ui.image_lab_gallery.images) > 0:
                        new_index = min(index, len(self.ui.image_lab_gallery.images) - 1)
                        self.ui.image_lab_gallery.selected_index = new_index
                    else:
                        self.ui.image_lab_gallery.selected_index = -1
                elif self.ui.image_lab_gallery.selected_index > index:
                    self.ui.image_lab_gallery.selected_index -= 1
                
                # Update all thumbnail indices
                for i, thumbnail in enumerate(self.ui.image_lab_gallery.thumbnails):
                    thumbnail.index = i
                
                # Update thumbnail styling
                for i, thumbnail in enumerate(self.ui.image_lab_gallery.thumbnails):
                    thumbnail.set_selected(i == self.ui.image_lab_gallery.selected_index)
                
                # Update the count label
                self.ui.image_lab_gallery.update_count_label()
                
                # Update image display/preview after removal
                if self.ui.image_lab_gallery.selected_index >= 0 and len(self.ui.image_lab_gallery.images) > 0:
                    # Load the newly selected image
                    self.on_gallery_image_clicked(self.ui.image_lab_gallery.selected_index)
                else:
                    # No images left, clear the display
                    self.current_image_path = None
                    if hasattr(self, 'original_images'):
                        self.original_images.clear()
                    if hasattr(self, 'processed_images'):
                        self.processed_images.clear()
                    # Clear the image views
                    if hasattr(self.ui, 'original_image_view') and self.ui.original_image_view:
                        self.ui.original_image_view.clear()
                    if hasattr(self.ui, 'processed_image_view') and self.ui.processed_image_view:
                        self.ui.processed_image_view.clear()
            
            logger.info(f"Successfully removed image from image lab gallery: {filename}")

    def _update_image_display(self):
        """Update the image displays with the current image."""
        if not self.current_image_path:
            logger.warning("No current image to display")
            return

        # Get the original and processed images
        original_image = self.original_images.get(self.current_image_path)
        processed_image = self.processed_images.get(self.current_image_path)

        if original_image is None or processed_image is None:
            logger.warning(f"Missing original or processed image for {self.current_image_path}")
            return

        # Resize images for display
        original_resized = resize_image(original_image, self.common_size)
        processed_resized = resize_image(processed_image, self.common_size)

        # Update original image view
        original_pixmap = convert_cvimage_to_qpixmap(original_resized, already_rgb=True)
        self.ui.original_view.setPixmap(original_pixmap)

        # Update processed image views
        processed_pixmap = convert_cvimage_to_qpixmap(processed_resized, already_rgb=True)
        self.ui.processed_view.setPixmap(processed_pixmap)
        self.ui.full_processed_view.setPixmap(processed_pixmap)

    def _update_image_info(self):
        """Update the image information display."""
        if not self.current_image_path or self.current_image_path not in self.processed_images:
            self.ui.image_info_label.setText("No image loaded")
            return

        # Get the processed image
        processed_image = self.processed_images[self.current_image_path]

        # Get image dimensions and type
        height, width = processed_image.shape[:2]
        channels = processed_image.shape[2] if len(processed_image.shape) > 2 else 1
        dtype = processed_image.dtype

        # Calculate min, max, mean values
        min_val = np.min(processed_image)
        max_val = np.max(processed_image)
        mean_val = np.mean(processed_image)

        # Update info label
        info_text = f"Dimensions: {width} x {height}\n"
        info_text += f"Channels: {channels}\n"
        info_text += f"Type: {dtype}\n"
        info_text += f"Min: {min_val:.2f}, Max: {max_val:.2f}, Mean: {mean_val:.2f}"

        self.ui.image_info_label.setText(info_text)

        # Update histogram
        self._update_histogram(processed_image)

    def _update_histogram(self, image):
        """Update the histogram display for the given image.

        Args:
            image: The image to generate a histogram for
        """
        try:
            # Update the interactive histogram widget
            self.ui.histogram_widget.set_histogram_data(image)
        except Exception as e:
            logger.error(f"Error updating histogram: {str(e)}")

    def _save_to_history(self):
        """Save the current state to history for undo functionality."""
        if not self.current_image_path:
            return

        # Get the current processed image
        current_image = self.processed_images.get(self.current_image_path)
        if current_image is None:
            return

        # Create a deep copy of the current image
        history_entry = current_image.copy()

        # Add to history stack
        if self.current_image_path not in self.image_history:
            self.image_history[self.current_image_path] = []

        self.image_history[self.current_image_path].append(history_entry)

        # Limit history size
        if len(self.image_history[self.current_image_path]) > self.max_history_size:
            self.image_history[self.current_image_path].pop(0)

    def undo_last_operation(self):
        """Undo the last image processing operation."""
        if not self.current_image_path:
            QMessageBox.warning(self.ui, "Warning", "No image selected.")
            return

        # Check if we have history for this image
        if (self.current_image_path not in self.image_history or
            not self.image_history[self.current_image_path]):
            QMessageBox.information(self.ui, "Information", "Nothing to undo.")
            return

        # Get the last history entry
        last_image = self.image_history[self.current_image_path].pop()

        # Restore the image
        self.processed_images[self.current_image_path] = last_image

        # Update display
        self._update_image_display()
        self._update_image_info()

        logger.info("Undid last operation")

    def reset_image(self):
        """Reset the processed image to the original."""
        if not self.current_image_path:
            QMessageBox.warning(self.ui, "Warning", "No image selected.")
            return

        # Save current state to history
        self._save_to_history()

        # Reset to original
        original = self.original_images.get(self.current_image_path)
        if original is not None:
            self.processed_images[self.current_image_path] = original.copy()

            # Reset UI controls
            self.ui.brightness_slider.setValue(0)
            self.ui.contrast_slider.setValue(0)
            self.ui.gamma_spinbox.setValue(1.0)

            # Update display
            self._update_image_display()
            self._update_image_info()

            # Clear operations list since we're starting fresh
            self._clear_operations()

            # Clear applied templates for this image
            if self.current_image_path in self.applied_templates:
                self.applied_templates[self.current_image_path] = []
                logger.debug(f"Cleared applied templates for {self.current_image_path}")

            logger.info("Reset image to original")
        else:
            logger.warning(f"Original image not found for {self.current_image_path}")

    def apply_histogram_equalization(self):
        """Apply histogram equalization to the current image."""
        if not self.current_image_path:
            QMessageBox.warning(self.ui, "Warning", "No image selected.")
            return

        # Get the current processed image
        image = self.processed_images.get(self.current_image_path)
        if image is None:
            return

        # Save current state to history
        self._save_to_history()

        # Apply histogram equalization
        if len(image.shape) == 3:
            # Color image - convert to LAB and equalize L channel
            lab = cv2.cvtColor(image, cv2.COLOR_RGB2LAB)
            l, a, b = cv2.split(lab)
            l_eq = cv2.equalizeHist(l)
            lab_eq = cv2.merge((l_eq, a, b))
            result = cv2.cvtColor(lab_eq, cv2.COLOR_LAB2RGB)
        else:
            # Grayscale image
            result = cv2.equalizeHist(image)

        # Update processed image
        self.processed_images[self.current_image_path] = result

        # Update display
        self._update_image_display()
        self._update_image_info()

        # Record operation for template
        self._record_operation('histogram_equalization')

        logger.info("Applied histogram equalization")

    def apply_auto_contrast(self):
        """Apply automatic contrast stretching to the current image."""
        if not self.current_image_path:
            QMessageBox.warning(self.ui, "Warning", "No image selected.")
            return

        # Get the current processed image
        image = self.processed_images.get(self.current_image_path)
        if image is None:
            return

        # Save current state to history
        self._save_to_history()

        # Apply auto contrast
        if len(image.shape) == 3:
            # Color image - process each channel
            result = np.zeros_like(image)
            for i in range(3):
                channel = image[:, :, i]
                min_val = np.min(channel)
                max_val = np.max(channel)
                if max_val > min_val:
                    result[:, :, i] = np.uint8(255 * ((channel - min_val) / (max_val - min_val)))
                else:
                    result[:, :, i] = channel
        else:
            # Grayscale image
            min_val = np.min(image)
            max_val = np.max(image)
            if max_val > min_val:
                result = np.uint8(255 * ((image - min_val) / (max_val - min_val)))
            else:
                result = image

        # Update processed image
        self.processed_images[self.current_image_path] = result

        # Update display
        self._update_image_display()
        self._update_image_info()

        # Record operation for template
        self._record_operation('auto_contrast')

        logger.info("Applied auto contrast")

    def update_brightness_contrast(self):
        """Update brightness and contrast based on slider values."""
        if not self.current_image_path:
            return

        # Get the current processed image
        image = self.original_images.get(self.current_image_path)
        if image is None:
            return

        # Get slider values
        brightness = self.ui.brightness_slider.value()
        contrast = self.ui.contrast_slider.value()

        # Update value labels
        self.ui.brightness_value.setText(str(brightness))
        self.ui.contrast_value.setText(str(contrast))

        # Apply brightness and contrast
        # Convert to float32 for processing
        adjusted = np.float32(image)

        # Apply contrast (before brightness)
        if contrast != 0:
            factor = (259 * (contrast + 255)) / (255 * (259 - contrast))
            adjusted = np.clip(factor * (adjusted - 128) + 128, 0, 255)

        # Apply brightness
        if brightness != 0:
            adjusted = np.clip(adjusted + brightness, 0, 255)

        # Convert back to uint8
        result = np.uint8(adjusted)

        # Update processed image (without saving to history for slider changes)
        self.processed_images[self.current_image_path] = result

        # Update display
        self._update_image_display()
        self._update_image_info()

    def apply_gamma_correction(self):
        """Apply gamma correction to the current image."""
        if not self.current_image_path:
            QMessageBox.warning(self.ui, "Warning", "No image selected.")
            return

        # Get the current processed image
        image = self.processed_images.get(self.current_image_path)
        if image is None:
            return

        # Save current state to history
        self._save_to_history()

        # Get gamma value
        gamma = self.ui.gamma_spinbox.value()

        # Apply gamma correction
        # Convert to float32 for processing
        adjusted = np.float32(image) / 255.0

        # Apply gamma
        adjusted = np.power(adjusted, 1.0 / gamma)

        # Convert back to uint8
        result = np.uint8(adjusted * 255)

        # Update processed image
        self.processed_images[self.current_image_path] = result

        # Update display
        self._update_image_display()
        self._update_image_info()

        # Record operation for template
        self._record_operation('gamma_correction', {'gamma': gamma})

        logger.info(f"Applied gamma correction with gamma={gamma}")

    def apply_morphological_operation(self):
        """Apply morphological operation to the current image."""
        if not self.current_image_path:
            QMessageBox.warning(self.ui, "Warning", "No image selected.")
            return

        # Get the current processed image
        image = self.processed_images.get(self.current_image_path)
        if image is None:
            return

        # Save current state to history
        self._save_to_history()

        # Get parameters
        operation = self.ui.morph_type.currentText()
        kernel_size = self.ui.kernel_size.value()
        iterations = self.ui.iterations.value()

        # Create kernel
        kernel = np.ones((kernel_size, kernel_size), np.uint8)

        # Apply morphological operation
        result = None
        if operation == "Erosion":
            result = cv2.erode(image, kernel, iterations=iterations)
        elif operation == "Dilation":
            result = cv2.dilate(image, kernel, iterations=iterations)
        elif operation == "Opening":
            result = cv2.morphologyEx(image, cv2.MORPH_OPEN, kernel, iterations=iterations)
        elif operation == "Closing":
            result = cv2.morphologyEx(image, cv2.MORPH_CLOSE, kernel, iterations=iterations)
        elif operation == "Gradient":
            result = cv2.morphologyEx(image, cv2.MORPH_GRADIENT, kernel, iterations=iterations)
        elif operation == "Top Hat":
            result = cv2.morphologyEx(image, cv2.MORPH_TOPHAT, kernel, iterations=iterations)
        elif operation == "Black Hat":
            result = cv2.morphologyEx(image, cv2.MORPH_BLACKHAT, kernel, iterations=iterations)

        if result is not None:
            # Update processed image
            self.processed_images[self.current_image_path] = result

            # Update display
            self._update_image_display()
            self._update_image_info()

            # Record operation for template
            self._record_operation('morphological', {
                'type': operation,
                'kernel_size': kernel_size,
                'iterations': iterations
            })

            logger.info(f"Applied {operation} with kernel size {kernel_size} and {iterations} iterations")

    def apply_filter(self):
        """Apply filter to the current image."""
        if not self.current_image_path:
            QMessageBox.warning(self.ui, "Warning", "No image selected.")
            return

        # Get the current processed image
        image = self.processed_images.get(self.current_image_path)
        if image is None:
            return

        # Save current state to history
        self._save_to_history()

        # Get parameters
        filter_type = self.ui.filter_type.currentText()
        filter_size = self.ui.filter_size.value()

        # Make sure filter size is odd
        if filter_size % 2 == 0:
            filter_size += 1

        # Apply filter
        result = None
        if filter_type == "Gaussian Blur":
            result = cv2.GaussianBlur(image, (filter_size, filter_size), 0)
        elif filter_type == "Median Blur":
            result = cv2.medianBlur(image, filter_size)
        elif filter_type == "Bilateral Filter":
            result = cv2.bilateralFilter(image, filter_size, 75, 75)
        elif filter_type == "Box Filter":
            result = cv2.boxFilter(image, -1, (filter_size, filter_size))
        elif filter_type == "Sharpen":
            # Create sharpening kernel
            kernel = np.array([[-1, -1, -1],
                              [-1,  9, -1],
                              [-1, -1, -1]])
            result = cv2.filter2D(image, -1, kernel)

        if result is not None:
            # Update processed image
            self.processed_images[self.current_image_path] = result

            # Update display
            self._update_image_display()
            self._update_image_info()

            # Record operation for template
            self._record_operation('filter', {
                'type': filter_type,
                'size': filter_size
            })

            logger.info(f"Applied {filter_type} with size {filter_size}")

    def open_crop_dialog(self):
        """Open the crop dialog for interactive cropping."""
        if not self.current_image_path:
            QMessageBox.warning(self.ui, "Warning", "No image selected.")
            return

        # Get the current processed image
        image = self.processed_images.get(self.current_image_path)
        if image is None:
            return

        # Save current state to history
        self._save_to_history()

        # Open crop dialog
        dialog = CropDialog(image, parent=self.ui)
        if dialog.exec():
            # Get cropped image
            cropped_image = dialog.get_cropped_image()

            # Update processed image
            self.processed_images[self.current_image_path] = cropped_image

            # Update display
            self._update_image_display()
            self._update_image_info()

            logger.info("Applied crop operation")

    def rotate_left(self):
        """Rotate the current image 90 degrees counter-clockwise."""
        if not self.current_image_path:
            QMessageBox.warning(self.ui, "Warning", "No image selected.")
            return

        # Get the current processed image
        image = self.processed_images.get(self.current_image_path)
        if image is None:
            return

        # Save current state to history
        self._save_to_history()

        # Apply rotation
        rotated_image = cv2.rotate(image, cv2.ROTATE_90_COUNTERCLOCKWISE)

        # Update processed image
        self.processed_images[self.current_image_path] = rotated_image

        # Update display
        self._update_image_display()
        self._update_image_info()

        logger.info("Applied rotate left operation")

    def rotate_right(self):
        """Rotate the current image 90 degrees clockwise."""
        if not self.current_image_path:
            QMessageBox.warning(self.ui, "Warning", "No image selected.")
            return

        # Get the current processed image
        image = self.processed_images.get(self.current_image_path)
        if image is None:
            return

        # Save current state to history
        self._save_to_history()

        # Apply rotation
        rotated_image = cv2.rotate(image, cv2.ROTATE_90_CLOCKWISE)

        # Update processed image
        self.processed_images[self.current_image_path] = rotated_image

        # Update display
        self._update_image_display()
        self._update_image_info()

        logger.info("Applied rotate right operation")

    def flip_horizontal(self):
        """Flip the current image horizontally."""
        if not self.current_image_path:
            QMessageBox.warning(self.ui, "Warning", "No image selected.")
            return

        # Get the current processed image
        image = self.processed_images.get(self.current_image_path)
        if image is None:
            return

        # Save current state to history
        self._save_to_history()

        # Apply horizontal flip
        flipped_image = cv2.flip(image, 1)  # 1 for horizontal flip

        # Update processed image
        self.processed_images[self.current_image_path] = flipped_image

        # Update display
        self._update_image_display()
        self._update_image_info()

        logger.info("Applied horizontal flip operation")

    def flip_vertical(self):
        """Flip the current image vertically."""
        if not self.current_image_path:
            QMessageBox.warning(self.ui, "Warning", "No image selected.")
            return

        # Get the current processed image
        image = self.processed_images.get(self.current_image_path)
        if image is None:
            return

        # Save current state to history
        self._save_to_history()

        # Apply vertical flip
        flipped_image = cv2.flip(image, 0)  # 0 for vertical flip

        # Update processed image
        self.processed_images[self.current_image_path] = flipped_image

        # Update display
        self._update_image_display()
        self._update_image_info()

        logger.info("Applied vertical flip operation")

    def apply_edits(self):
        """Apply the edited image to the project."""
        if not self.current_image_path:
            QMessageBox.warning(self.ui, "Warning", "No image selected.")
            return

        # Get the current processed image
        image = self.processed_images.get(self.current_image_path)
        if image is None:
            return

        # Check if we have a project
        if not self.project:
            QMessageBox.warning(self.ui, "Warning", "No project loaded.")
            return

        # Save the processed image to a temporary file
        temp_dir = tempfile.gettempdir()
        base_name = os.path.basename(self.current_image_path)
        name, ext = os.path.splitext(base_name)
        temp_filename = f"{name}_edited_{uuid.uuid4().hex[:8]}{ext}"
        temp_path = os.path.join(temp_dir, temp_filename)

        # Convert RGB to BGR for OpenCV
        image_bgr = cv2.cvtColor(image, cv2.COLOR_RGB2BGR)

        # Save the image
        cv2.imwrite(temp_path, image_bgr)

        # Add the image to the project
        if os.path.exists(temp_path):
            new_info = self.project.add_image(temp_path)
            if new_info:
                logger.info(f"Added edited image to project: {new_info.filename}")

                # Save the project to ensure the new image is persisted
                try:
                    self.project.save()
                    logger.info(f"Project saved after adding new image: {new_info.filename}")
                except Exception as e:
                    logger.error(f"Failed to save project after adding new image: {e}")

                # Update the Project Hub page's gallery display
                self._update_project_hub_gallery()

                # Clean up the temporary file
                try:
                    os.remove(temp_path)
                except Exception as e:
                    logger.warning(f"Failed to remove temporary file: {e}")

                QMessageBox.information(self.ui, "Success", f"New image '{new_info.filename}' has been added to the Project Hub.")
                return True
            else:
                QMessageBox.warning(self.ui, "Error", "Failed to add edited image to Project Hub.")
                return False
        else:
            QMessageBox.warning(self.ui, "Error", "Failed to save temporary image file.")
            return False

    def _update_project_hub_gallery(self):
        """Update the Project Hub page's gallery display."""
        try:
            # Find the main application instance
            main_app = self._find_main_app()
            if main_app and hasattr(main_app, 'project_hub_page'):
                # Update the Project Hub page's gallery display
                if hasattr(main_app.project_hub_page, '_update_gallery_display'):
                    main_app.project_hub_page._update_gallery_display()
                    logger.info("Updated Project Hub gallery display")
                else:
                    logger.warning("Project Hub page does not have _update_gallery_display method")
            else:
                logger.warning("Could not find main application or project_hub_page")
        except Exception as e:
            logger.error(f"Error updating Project Hub gallery display: {e}")

    def _find_main_app(self):
        """Find the main application instance."""
        # Try to find the main app through parent hierarchy
        parent = self.ui
        while parent:
            # Check if this is the main app (VisionLabAiApp)
            if hasattr(parent, 'project_hub_page'):
                return parent

            # Move up the parent hierarchy
            if hasattr(parent, 'parent') and callable(getattr(parent, 'parent', None)):
                parent = parent.parent()
            else:
                break

        return None

    def clear(self):
        """Clear all images and reset the page state."""
        # Clear gallery
        self.ui.image_lab_gallery.clear_images()

        # Clear image variables
        self.original_images = {}
        self.processed_images = {}
        self.image_history = {}
        self.current_image_path = None

        # Clear applied templates tracking
        self.applied_templates = {}
        logger.debug("Cleared all applied templates tracking")

        # Clear displays
        self.ui.original_view.clear()
        self.ui.processed_view.clear()
        self.ui.full_processed_view.clear()

        # Reset info
        self.ui.image_info_label.setText("No image loaded")
        self.ui.histogram_label.setText("Histogram will appear here")

        # Reset UI controls
        self.ui.brightness_slider.setValue(0)
        self.ui.contrast_slider.setValue(0)
        self.ui.gamma_spinbox.setValue(1.0)

        logger.info("Cleared Image Lab page")

    def update_threshold_value_label(self):
        """Update the threshold value label."""
        value = self.ui.threshold_value.value()
        self.ui.threshold_value_label.setText(str(value))

    def apply_threshold(self):
        """Apply thresholding to the current image."""
        if not self.current_image_path:
            QMessageBox.warning(self.ui, "Warning", "No image selected.")
            return

        # Get the current processed image
        image = self.processed_images.get(self.current_image_path)
        if image is None:
            return

        # Save current state to history
        self._save_to_history()

        # Get threshold parameters
        threshold_type = self.ui.threshold_type.currentText()
        threshold_value = self.ui.threshold_value.value()
        block_size = self.ui.block_size.value()
        c_value = self.ui.c_value.value()

        # Ensure block size is odd
        if block_size % 2 == 0:
            block_size += 1

        # Convert to grayscale if needed
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
        else:
            gray = image.copy()

        # Apply thresholding
        if threshold_type == "Binary":
            _, result = cv2.threshold(gray, threshold_value, 255, cv2.THRESH_BINARY)
        elif threshold_type == "Binary Inverted":
            _, result = cv2.threshold(gray, threshold_value, 255, cv2.THRESH_BINARY_INV)
        elif threshold_type == "Truncate":
            _, result = cv2.threshold(gray, threshold_value, 255, cv2.THRESH_TRUNC)
        elif threshold_type == "To Zero":
            _, result = cv2.threshold(gray, threshold_value, 255, cv2.THRESH_TOZERO)
        elif threshold_type == "To Zero Inverted":
            _, result = cv2.threshold(gray, threshold_value, 255, cv2.THRESH_TOZERO_INV)
        elif threshold_type == "Otsu":
            _, result = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        elif threshold_type == "Adaptive Mean":
            result = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_MEAN_C,
                                          cv2.THRESH_BINARY, block_size, c_value)
        elif threshold_type == "Adaptive Gaussian":
            result = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                          cv2.THRESH_BINARY, block_size, c_value)
        else:
            QMessageBox.warning(self.ui, "Warning", f"Unknown threshold type: {threshold_type}")
            return

        # Convert back to RGB if original was RGB
        if len(image.shape) == 3:
            result = cv2.cvtColor(result, cv2.COLOR_GRAY2RGB)

        # Update processed image
        self.processed_images[self.current_image_path] = result

        # Update display
        self._update_image_display()
        self._update_image_info()

        # Record operation for template
        self._record_operation('threshold', {
            'type': threshold_type,
            'value': threshold_value,
            'block_size': block_size,
            'c_value': c_value
        })

        logger.info(f"Applied {threshold_type} thresholding with value {threshold_value}")

    def apply_color_space_conversion(self):
        """Apply color space conversion to the current image."""
        if not self.current_image_path:
            QMessageBox.warning(self.ui, "Warning", "No image selected.")
            return

        # Get the current processed image
        image = self.processed_images.get(self.current_image_path)
        if image is None:
            return

        # Save current state to history
        self._save_to_history()

        # Get color space conversion type
        conversion_type = self.ui.color_space.currentText()

        # Apply color space conversion
        if conversion_type == "RGB to Grayscale":
            if len(image.shape) == 3:
                result = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
                # Convert back to RGB for display consistency
                result = cv2.cvtColor(result, cv2.COLOR_GRAY2RGB)
            else:
                result = image.copy()
        elif conversion_type == "RGB to HSV":
            if len(image.shape) == 3:
                result = cv2.cvtColor(image, cv2.COLOR_RGB2HSV)
            else:
                QMessageBox.warning(self.ui, "Warning", "Cannot convert grayscale to HSV.")
                return
        elif conversion_type == "RGB to LAB":
            if len(image.shape) == 3:
                result = cv2.cvtColor(image, cv2.COLOR_RGB2LAB)
            else:
                QMessageBox.warning(self.ui, "Warning", "Cannot convert grayscale to LAB.")
                return
        elif conversion_type == "HSV to RGB":
            if len(image.shape) == 3:
                try:
                    result = cv2.cvtColor(image, cv2.COLOR_HSV2RGB)
                except cv2.error:
                    QMessageBox.warning(self.ui, "Warning", "Invalid HSV image.")
                    return
            else:
                QMessageBox.warning(self.ui, "Warning", "Cannot convert grayscale to RGB.")
                return
        elif conversion_type == "LAB to RGB":
            if len(image.shape) == 3:
                try:
                    result = cv2.cvtColor(image, cv2.COLOR_LAB2RGB)
                except cv2.error:
                    QMessageBox.warning(self.ui, "Warning", "Invalid LAB image.")
                    return
            else:
                QMessageBox.warning(self.ui, "Warning", "Cannot convert grayscale to RGB.")
                return
        else:
            QMessageBox.warning(self.ui, "Warning", f"Unknown conversion type: {conversion_type}")
            return

        # Update processed image
        self.processed_images[self.current_image_path] = result

        # Update display
        self._update_image_display()
        self._update_image_info()

        logger.info(f"Applied color space conversion: {conversion_type}")

    def apply_channel_operation(self):
        """Apply channel operation to the current image."""
        if not self.current_image_path:
            QMessageBox.warning(self.ui, "Warning", "No image selected.")
            return

        # Get the current processed image
        image = self.processed_images.get(self.current_image_path)
        if image is None:
            return

        # Save current state to history
        self._save_to_history()

        # Get channel operation type
        operation_type = self.ui.channel_operation.currentText()

        # Check if image is color
        if len(image.shape) < 3:
            QMessageBox.warning(self.ui, "Warning", "Channel operations require a color image.")
            return

        # Apply channel operation
        if operation_type == "Extract Red Channel":
            result = image.copy()
            result[:, :, 1] = 0  # Zero out green
            result[:, :, 2] = 0  # Zero out blue
        elif operation_type == "Extract Green Channel":
            result = image.copy()
            result[:, :, 0] = 0  # Zero out red
            result[:, :, 2] = 0  # Zero out blue
        elif operation_type == "Extract Blue Channel":
            result = image.copy()
            result[:, :, 0] = 0  # Zero out red
            result[:, :, 1] = 0  # Zero out green
        elif operation_type == "Extract Hue Channel":
            hsv = cv2.cvtColor(image, cv2.COLOR_RGB2HSV)
            h, _, _ = cv2.split(hsv)
            # Normalize hue to 0-255 for better visualization
            h = cv2.normalize(h, None, 0, 255, cv2.NORM_MINMAX)
            result = cv2.cvtColor(h, cv2.COLOR_GRAY2RGB)
        elif operation_type == "Extract Saturation Channel":
            hsv = cv2.cvtColor(image, cv2.COLOR_RGB2HSV)
            _, s, _ = cv2.split(hsv)
            result = cv2.cvtColor(s, cv2.COLOR_GRAY2RGB)
        elif operation_type == "Extract Value Channel":
            hsv = cv2.cvtColor(image, cv2.COLOR_RGB2HSV)
            _, _, v = cv2.split(hsv)
            result = cv2.cvtColor(v, cv2.COLOR_GRAY2RGB)
        elif operation_type == "Swap Red-Blue":
            result = image.copy()
            result[:, :, [0, 2]] = result[:, :, [2, 0]]  # Swap red and blue channels
        elif operation_type == "Invert Colors":
            result = 255 - image
        else:
            QMessageBox.warning(self.ui, "Warning", f"Unknown operation type: {operation_type}")
            return

        # Update processed image
        self.processed_images[self.current_image_path] = result

        # Update display
        self._update_image_display()
        self._update_image_info()

        logger.info(f"Applied channel operation: {operation_type}")

    def update_canny_threshold_labels(self):
        """Update the Canny threshold labels."""
        threshold1 = self.ui.canny_threshold1.value()
        threshold2 = self.ui.canny_threshold2.value()
        self.ui.canny_threshold1_label.setText(str(threshold1))
        self.ui.canny_threshold2_label.setText(str(threshold2))

    def apply_edge_detection(self):
        """Apply edge detection to the current image."""
        if not self.current_image_path:
            QMessageBox.warning(self.ui, "Warning", "No image selected.")
            return

        # Get the current processed image
        image = self.processed_images.get(self.current_image_path)
        if image is None:
            return

        # Save current state to history
        self._save_to_history()

        # Get edge detection parameters
        edge_method = self.ui.edge_method.currentText()

        # Convert to grayscale if needed
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
        else:
            gray = image.copy()

        # Apply edge detection
        if edge_method == "Sobel":
            # Compute Sobel gradients
            sobelx = cv2.Sobel(gray, cv2.CV_64F, 1, 0, ksize=3)
            sobely = cv2.Sobel(gray, cv2.CV_64F, 0, 1, ksize=3)

            # Compute gradient magnitude
            magnitude = np.sqrt(sobelx**2 + sobely**2)

            # Normalize to 0-255
            result = cv2.normalize(magnitude, None, 0, 255, cv2.NORM_MINMAX).astype(np.uint8)
        elif edge_method == "Scharr":
            # Compute Scharr gradients
            scharrx = cv2.Scharr(gray, cv2.CV_64F, 1, 0)
            scharry = cv2.Scharr(gray, cv2.CV_64F, 0, 1)

            # Compute gradient magnitude
            magnitude = np.sqrt(scharrx**2 + scharry**2)

            # Normalize to 0-255
            result = cv2.normalize(magnitude, None, 0, 255, cv2.NORM_MINMAX).astype(np.uint8)
        elif edge_method == "Laplacian":
            # Apply Laplacian
            laplacian = cv2.Laplacian(gray, cv2.CV_64F)

            # Normalize to 0-255
            result = cv2.normalize(np.abs(laplacian), None, 0, 255, cv2.NORM_MINMAX).astype(np.uint8)
        elif edge_method == "Canny":
            # Get Canny thresholds
            threshold1 = self.ui.canny_threshold1.value()
            threshold2 = self.ui.canny_threshold2.value()

            # Apply Canny edge detection
            result = cv2.Canny(gray, threshold1, threshold2)
        else:
            QMessageBox.warning(self.ui, "Warning", f"Unknown edge detection method: {edge_method}")
            return

        # Convert back to RGB if original was RGB
        if len(image.shape) == 3:
            result = cv2.cvtColor(result, cv2.COLOR_GRAY2RGB)

        # Update processed image
        self.processed_images[self.current_image_path] = result

        # Update display
        self._update_image_display()
        self._update_image_info()

        # Record operation for template
        params = {'method': edge_method}
        if edge_method == "Canny":
            params['threshold1'] = threshold1
            params['threshold2'] = threshold2
        self._record_operation('edge_detection', params)

        logger.info(f"Applied edge detection: {edge_method}")

    def _update_template_combo(self):
        """Update the template combo box with available templates."""
        try:
            # Block signals to prevent triggering selection events
            self.ui.template_combo.blockSignals(True)

            # Clear current items
            self.ui.template_combo.clear()

            # Get template names from manager
            template_names = self.template_manager.get_template_names()

            # Add templates
            for template_name in template_names:
                self.ui.template_combo.addItem(template_name)

            # Re-enable signals
            self.ui.template_combo.blockSignals(False)

            logger.info(f"Updated template combo with {len(template_names)} templates")
        except Exception as e:
            logger.error(f"Error updating template combo: {str(e)}")

    def manage_templates(self):
        """Open the template management dialog."""
        try:
            # Create and show the template dialog with the current template manager
            dialog = TemplateDialog(self.ui, template_manager=self.template_manager)

            # Execute the dialog
            if dialog.exec():
                # Get selected template if any
                selected_template = dialog.get_selected_template()
                if selected_template:
                    # Update the template combo box
                    self._update_template_combo()

                    # Select the template
                    index = self.ui.template_combo.findText(selected_template)
                    if index >= 0:
                        self.ui.template_combo.setCurrentIndex(index)
                        self.ui.template_name_input.setText(selected_template)
                else:
                    # Just update the combo box
                    self._update_template_combo()

                logger.info("Template management dialog closed with OK")
            else:
                logger.info("Template management dialog canceled")
        except Exception as e:
            logger.error(f"Error opening template management dialog: {str(e)}")

    def _record_operation(self, operation_type, params=None):
        """Record an operation for potential template saving.

        Args:
            operation_type: Type of operation (e.g., 'histogram_equalization')
            params: Dictionary of parameters for the operation (optional)
        """
        # Create operation record
        operation = {
            'type': operation_type,
            'params': params or {}
        }

        # Safeguard against excessive operation recording
        # Limit to 100 operations to prevent memory issues
        if len(self.current_operations) >= 100:
            # Keep only the most recent 50 operations
            self.current_operations = self.current_operations[-50:]
            logger.warning(f"Operation list exceeded 100 items. Trimmed to last 50 operations.")

        # Add to current operations list
        self.current_operations.append(operation)
        logger.debug(f"Recorded operation: {operation_type}")

    def _clear_operations(self):
        """Clear the current operations list."""
        self.current_operations = []
        logger.debug("Cleared operations list")

    def save_operations_template(self):
        """Save the current operations as a template."""
        if not self.current_operations:
            QMessageBox.warning(self.ui, "Warning", "No operations to save.")
            return

        # Get template name
        template_name = self.ui.template_name_input.text().strip()
        if not template_name:
            QMessageBox.warning(self.ui, "Warning", "Please enter a template name.")
            return

        # Save template using template manager
        if self.template_manager.save_template(template_name, self.current_operations):
            # Update template combo without triggering selection events
            self.ui.template_combo.blockSignals(True)
            self._update_template_combo()

            # Select the new template without triggering the selection event
            index = self.ui.template_combo.findText(template_name)
            if index >= 0:
                self.ui.template_combo.setCurrentIndex(index)

            # Re-enable signals
            self.ui.template_combo.blockSignals(False)

            # Get operation count before clearing
            operation_count = len(self.current_operations)

            # Mark this image as having this template applied
            if self.current_image_path:
                if self.current_image_path not in self.applied_templates:
                    self.applied_templates[self.current_image_path] = []

                # Add template to the list if not already there
                if template_name not in self.applied_templates[self.current_image_path]:
                    self.applied_templates[self.current_image_path].append(template_name)
                    logger.debug(f"Marked image {self.current_image_path} as having template '{template_name}' applied")

            # Clear operations after saving to prevent duplicates
            self._clear_operations()

            QMessageBox.information(
                self.ui,
                "Template Saved",
                f"Template '{template_name}' with {operation_count} operations saved successfully."
            )

            logger.info(f"Saved template '{template_name}' with {operation_count} operations")
        else:
            QMessageBox.warning(self.ui, "Error", f"Failed to save template: {template_name}")
            logger.error(f"Error saving template: {template_name}")

    def load_operations_template(self):
        """Load a template from the combo box."""
        template_name = self.ui.template_combo.currentText()
        if not template_name:
            # Show template management dialog to select a template
            self.manage_templates()
            return

        self._apply_template(template_name)

    def on_template_selected(self, index):
        """Handle template selection in the combo box."""
        if index < 0:
            return

        template_name = self.ui.template_combo.itemText(index)
        if not template_name:
            return

        # Update template name input only
        self.ui.template_name_input.setText(template_name)

        # Do NOT automatically apply the template when selected
        # This prevents infinite loops when templates are selected

    def _apply_template(self, template_name, update_display=True):
        """Apply a template to the current image.

        Args:
            template_name: Name of the template to apply
            update_display: Whether to update the display after applying the template
        """
        if not self.current_image_path:
            QMessageBox.warning(self.ui, "Warning", "No image selected.")
            return

        # Load template from manager
        template = self.template_manager.load_template(template_name)
        if not template:
            QMessageBox.warning(self.ui, "Warning", f"Template '{template_name}' not found.")
            return

        operations = template.get('operations', [])

        if not operations:
            QMessageBox.warning(self.ui, "Warning", f"Template '{template_name}' has no operations.")
            return

        # Apply operations
        try:
            # Save current state to history
            self._save_to_history()

            for operation in operations:
                operation_type = operation.get('type')
                params = operation.get('params', {})

                if operation_type == 'histogram_equalization':
                    self.apply_histogram_equalization()
                elif operation_type == 'auto_contrast':
                    self.apply_auto_contrast()
                elif operation_type == 'gamma_correction':
                    gamma = params.get('gamma', 1.0)
                    self.ui.gamma_spinbox.setValue(gamma)
                    self.apply_gamma_correction()
                elif operation_type == 'brightness_contrast':
                    brightness = params.get('brightness', 0)
                    contrast = params.get('contrast', 0)
                    self.ui.brightness_slider.setValue(brightness)
                    self.ui.contrast_slider.setValue(contrast)
                    self.update_brightness_contrast()
                elif operation_type == 'morphological':
                    morph_type = params.get('type', 'Erosion')
                    kernel_size = params.get('kernel_size', 3)
                    iterations = params.get('iterations', 1)
                    self.ui.morph_type.setCurrentText(morph_type)
                    self.ui.kernel_size.setValue(kernel_size)
                    self.ui.iterations.setValue(iterations)
                    self.apply_morphological_operation()
                elif operation_type == 'filter':
                    filter_type = params.get('type', 'Gaussian Blur')
                    filter_size = params.get('size', 5)
                    self.ui.filter_type.setCurrentText(filter_type)
                    self.ui.filter_size.setValue(filter_size)
                    self.apply_filter()
                elif operation_type == 'threshold':
                    threshold_type = params.get('type', 'Binary')
                    threshold_value = params.get('value', 127)
                    block_size = params.get('block_size', 11)
                    c_value = params.get('c_value', 2)
                    self.ui.threshold_type.setCurrentText(threshold_type)
                    self.ui.threshold_value.setValue(threshold_value)
                    self.ui.block_size.setValue(block_size)
                    self.ui.c_value.setValue(c_value)
                    self.apply_threshold()
                elif operation_type == 'color_space':
                    conversion_type = params.get('conversion', 'RGB to Grayscale')
                    self.ui.color_space.setCurrentText(conversion_type)
                    self.apply_color_space_conversion()
                elif operation_type == 'channel_operation':
                    operation_name = params.get('operation', 'Extract Red Channel')
                    self.ui.channel_operation.setCurrentText(operation_name)
                    self.apply_channel_operation()
                elif operation_type == 'edge_detection':
                    edge_method = params.get('method', 'Sobel')
                    threshold1 = params.get('threshold1', 100)
                    threshold2 = params.get('threshold2', 200)
                    self.ui.edge_method.setCurrentText(edge_method)
                    self.ui.canny_threshold1.setValue(threshold1)
                    self.ui.canny_threshold2.setValue(threshold2)
                    self.apply_edge_detection()

            # Record that this template has been applied to this image
            if self.current_image_path not in self.applied_templates:
                self.applied_templates[self.current_image_path] = []

            # Add template to the list if not already there
            if template_name not in self.applied_templates[self.current_image_path]:
                self.applied_templates[self.current_image_path].append(template_name)

            # Only show message and update display if requested
            if update_display:
                # Update the display
                self._update_image_display()
                self._update_image_info()

                # Show message
                QMessageBox.information(
                    self.ui,
                    "Template Applied",
                    f"Applied template '{template_name}' with {len(operations)} operations."
                )

            logger.info(f"Applied template '{template_name}' with {len(operations)} operations")
        except Exception as e:
            QMessageBox.warning(self.ui, "Error", f"Failed to apply template: {str(e)}")
            logger.error(f"Error applying template: {str(e)}")

    def apply_to_selected(self):
        """Apply the selected template to the selected image."""
        template_name = self.ui.template_combo.currentText()
        if not template_name:
            QMessageBox.warning(self.ui, "Warning", "No template selected.")
            return

        self._apply_template(template_name)

    def apply_to_all(self):
        """Apply the selected template to all images in the gallery."""
        if not self.ui.image_lab_gallery.images:
            QMessageBox.warning(self.ui, "Warning", "No images loaded.")
            return

        template_name = self.ui.template_combo.currentText()
        if not template_name:
            QMessageBox.warning(self.ui, "Warning", "No template selected.")
            return

        # Confirm with user
        reply = QMessageBox.question(
            self.ui,
            "Confirm Batch Operation",
            f"Apply template '{template_name}' to all {len(self.ui.image_lab_gallery.images)} images?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply != QMessageBox.Yes:
            return

        # Get list of images to process
        image_paths = [path for path in self.original_images.keys() if path in self.processed_images]

        # Create progress dialog
        progress_dialog = BatchProgressDialog(
            parent=self.ui,
            total_items=len(image_paths),
            title=f"Applying Template: {template_name}"
        )

        # Save current image path
        current_path = self.current_image_path

        # Process all images
        processed_count = 0
        skipped_count = 0

        # Show the dialog
        progress_dialog.show()

        # Process each image
        for i, image_path in enumerate(image_paths):
            # Check if operation was canceled
            if progress_dialog.is_canceled():
                logger.info("Batch operation canceled by user")
                break

            # Check if template has already been applied to this image
            if (image_path in self.applied_templates and
                template_name in self.applied_templates[image_path]):
                # Skip this image
                progress_dialog.progress_updated.emit(
                    i,
                    f"Skipping: {os.path.basename(image_path)} (template already applied)"
                )
                skipped_count += 1
                continue

            # Update progress
            progress_dialog.progress_updated.emit(
                i,
                f"Processing: {os.path.basename(image_path)}"
            )

            # Set current image path to process
            self.current_image_path = image_path

            # Apply template
            try:
                self._apply_template(template_name, update_display=False)
                processed_count += 1
            except Exception as e:
                logger.error(f"Error applying template to {image_path}: {str(e)}")

        # Complete the progress
        progress_dialog.progress_updated.emit(
            len(image_paths),
            "Processing complete"
        )

        # Close the dialog
        progress_dialog.operation_complete.emit()

        # Restore current image path
        self.current_image_path = current_path

        # Update the display for the current image
        if self.current_image_path:
            self._update_image_display()
            self._update_image_info()

        # Show completion message
        if not progress_dialog.is_canceled():
            message = f"Successfully processed {processed_count} images with template '{template_name}'."
            if skipped_count > 0:
                message += f"\nSkipped {skipped_count} images that already had this template applied."

            QMessageBox.information(
                self.ui,
                "Batch Processing Complete",
                message
            )

        logger.info(f"Applied template '{template_name}' to {processed_count} images (skipped {skipped_count})")

    def export_all_processed_images(self):
        """Export all processed images to a directory."""
        if not self.ui.image_lab_gallery.images:
            QMessageBox.warning(self.ui, "Warning", "No images loaded.")
            return

        # Ask for export directory
        export_dir = QFileDialog.getExistingDirectory(
            self.ui,
            "Select Export Directory",
            os.path.expanduser("~"),
            QFileDialog.ShowDirsOnly
        )

        if not export_dir:
            return

        # Get list of images to export
        image_paths = list(self.processed_images.keys())

        # Create progress dialog
        progress_dialog = BatchProgressDialog(
            parent=self.ui,
            total_items=len(image_paths),
            title="Exporting Processed Images"
        )

        # Show the dialog
        progress_dialog.show()

        # Export all processed images
        exported_count = 0

        # Process each image
        for i, image_path in enumerate(image_paths):
            # Check if operation was canceled
            if progress_dialog.is_canceled():
                logger.info("Export operation canceled by user")
                break

            # Update progress
            progress_dialog.progress_updated.emit(
                i,
                f"Exporting: {os.path.basename(image_path)}"
            )

            try:
                # Get the processed image
                processed_image = self.processed_images[image_path]

                # Get the filename
                filename = os.path.basename(image_path)
                name, ext = os.path.splitext(filename)

                # Create export path
                export_path = os.path.join(export_dir, f"{name}_processed{ext}")

                # Convert RGB to BGR for OpenCV
                if len(processed_image.shape) == 3:
                    export_image = cv2.cvtColor(processed_image, cv2.COLOR_RGB2BGR)
                else:
                    export_image = processed_image

                # Save the image
                cv2.imwrite(export_path, export_image)

                exported_count += 1
            except Exception as e:
                logger.error(f"Error exporting {image_path}: {str(e)}")

        # Complete the progress
        progress_dialog.progress_updated.emit(
            len(image_paths),
            "Export complete"
        )

        # Close the dialog
        progress_dialog.operation_complete.emit()

        # Show completion message if not canceled
        if not progress_dialog.is_canceled():
            QMessageBox.information(
                self.ui,
                "Export Complete",
                f"Successfully exported {exported_count} images to {export_dir}."
            )

        logger.info(f"Exported {exported_count} processed images to {export_dir}")

    def clear_image_lab_gallery(self):
        """Clears all images from the image lab gallery."""
        if hasattr(self.ui, 'image_lab_gallery'):
            self.ui.image_lab_gallery.clear_images()
            logger.info("Image lab gallery cleared successfully")
        
        # Clear the image lab image views as well
        try:
            if hasattr(self.ui, 'original_view') and self.ui.original_view:
                self.ui.original_view.clear()
                logger.info("Image lab original view cleared")
            if hasattr(self.ui, 'processed_view') and self.ui.processed_view:
                self.ui.processed_view.clear()
                logger.info("Image lab processed view cleared")
            if hasattr(self.ui, 'full_processed_view') and self.ui.full_processed_view:
                self.ui.full_processed_view.clear()
                logger.info("Image lab full processed view cleared")
        except Exception as e:
            logger.error(f"Error clearing image lab image views: {e}")
