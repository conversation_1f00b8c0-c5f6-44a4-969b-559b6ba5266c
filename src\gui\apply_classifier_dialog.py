# src/gui/apply_classifier_dialog.py

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
    QProgressBar, QMessageBox, QSplitter, QGroupBox, QFileDialog,
    QTextEdit, QFrame
)
from PySide6.QtCore import Qt, Signal, QThread, QObject
from PySide6.QtGui import QPixmap, QFont
import cv2
import numpy as np
import os
from functools import partial
from skimage import feature
from src.widgets.zoomable_graphics_view import ZoomableGraphicsView
import logging

logger = logging.getLogger(__name__)

class ClassifierWorker(QObject):
    """Worker thread for applying classifier to avoid UI freezing."""
    
    progress_updated = Signal(int)
    result_ready = Signal(np.ndarray, np.ndarray)  # original_image, segmented_result
    error_occurred = Signal(str)
    
    def __init__(self, image_path, classifier, feature_params, label_mapping=None):
        super().__init__()
        self.image_path = image_path
        self.classifier = classifier
        self.feature_params = feature_params
        self.label_mapping = label_mapping
        
    def run(self):
        """Apply classifier to the image."""
        try:
            # Load the image
            self.progress_updated.emit(10)
            image = cv2.imread(self.image_path)
            if image is None:
                self.error_occurred.emit(f"Failed to load image: {self.image_path}")
                return
                
            image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            self.progress_updated.emit(30)
            
            # Extract features
            features_func = partial(
                feature.multiscale_basic_features,
                **self.feature_params
            )
            features = features_func(image)
            self.progress_updated.emit(60)
            
            # Apply classifier
            probabilities = self.classifier.predict_proba(features.reshape(-1, features.shape[-1]))
            predicted_classes = np.argmax(probabilities, axis=1)
            self.progress_updated.emit(80)
            
            # Apply label mapping if available
            if self.label_mapping:
                reverse_mapping = {v: k for k, v in self.label_mapping.items()}
                original_classes = np.zeros_like(predicted_classes)
                for remapped, original in reverse_mapping.items():
                    original_classes[predicted_classes == remapped] = original + 1
                result = original_classes.reshape(image.shape[:2])
            else:
                result = predicted_classes.reshape(image.shape[:2]) + 1
                
            self.progress_updated.emit(100)
            self.result_ready.emit(image, result)
            
        except Exception as e:
            self.error_occurred.emit(f"Error applying classifier: {str(e)}")

class ApplyClassifierDialog(QDialog):
    """Dialog for applying trained classifier to new images."""
    
    def __init__(self, classifier, feature_params, label_mapping=None, mask_colors=None, parent=None):
        """Initialize the dialog.
        
        Args:
            classifier: Trained classifier object
            feature_params: Dictionary of feature extraction parameters
            label_mapping: Optional label mapping dictionary
            mask_colors: Optional mask colors array
            parent: Parent widget
        """
        super().__init__(parent)
        self.classifier = classifier
        self.feature_params = feature_params
        self.label_mapping = label_mapping
        self.mask_colors = mask_colors
        
        self.current_image = None
        self.current_result = None
        self.worker_thread = None
        
        self.setup_ui()
        self.setup_connections()
        
    def setup_ui(self):
        """Set up the dialog UI."""
        self.setWindowTitle("Apply Classifier to New Image")
        self.setMinimumSize(1000, 700)
        self.resize(1200, 800)
        
        # Main layout
        main_layout = QVBoxLayout(self)
        
        # Header
        header_layout = QHBoxLayout()
        title_label = QLabel("Apply Trained Classifier")
        title_font = QFont()
        title_font.setPointSize(14)
        title_font.setBold(True)
        title_label.setFont(title_font)
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        
        # Load Image button
        self.load_image_button = QPushButton("Load Image")
        self.load_image_button.setMinimumHeight(35)
        header_layout.addWidget(self.load_image_button)
        
        main_layout.addLayout(header_layout)
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        main_layout.addWidget(self.progress_bar)
        
        # Content area
        content_splitter = QSplitter(Qt.Orientation.Horizontal)
        main_layout.addWidget(content_splitter)
        
        # Left panel - Original Image
        left_group = QGroupBox("Original Image")
        left_layout = QVBoxLayout(left_group)
        left_layout.setContentsMargins(5, 5, 5, 5)  # Reduce margins
        left_layout.setSpacing(3)  # Reduce spacing between widgets
        
        self.original_view = ZoomableGraphicsView()
        self.original_view.setMinimumSize(400, 300)
        left_layout.addWidget(self.original_view, 1)  # Give stretch factor of 1
        
        # Image info - more compact
        self.image_info_label = QLabel("No image loaded")
        self.image_info_label.setWordWrap(True)
        self.image_info_label.setMaximumHeight(80)  # Limit height
        self.image_info_label.setStyleSheet(
            "padding: 3px; background-color: #f8f8f8; border: 1px solid #ddd; "
            "font-size: 11px; color: #666;"
        )
        left_layout.addWidget(self.image_info_label, 0)  # No stretch
        
        content_splitter.addWidget(left_group)
        
        # Right panel - Segmentation Result
        right_group = QGroupBox("Segmentation Result")
        right_layout = QVBoxLayout(right_group)
        right_layout.setContentsMargins(5, 5, 5, 5)  # Reduce margins
        right_layout.setSpacing(3)  # Reduce spacing between widgets
        
        self.result_view = ZoomableGraphicsView()
        self.result_view.setMinimumSize(400, 300)
        right_layout.addWidget(self.result_view, 1)  # Give stretch factor of 1
        
        # Result info - more compact
        self.result_info_label = QLabel("No segmentation result")
        self.result_info_label.setWordWrap(True)
        self.result_info_label.setMaximumHeight(80)  # Limit height
        self.result_info_label.setStyleSheet(
            "padding: 3px; background-color: #f8f8f8; border: 1px solid #ddd; "
            "font-size: 11px; color: #666;"
        )
        right_layout.addWidget(self.result_info_label, 0)  # No stretch
        
        content_splitter.addWidget(right_group)
        
        # Set equal sizes for both panels
        content_splitter.setSizes([500, 500])
        
        # Bottom panel - Controls
        controls_layout = QHBoxLayout()
        
        # Apply button
        self.apply_button = QPushButton("Apply Classifier")
        self.apply_button.setEnabled(False)
        self.apply_button.setMinimumHeight(35)
        controls_layout.addWidget(self.apply_button)
        
        # Save result button
        self.save_result_button = QPushButton("Save Result")
        self.save_result_button.setEnabled(False)
        self.save_result_button.setMinimumHeight(35)
        controls_layout.addWidget(self.save_result_button)
        
        controls_layout.addStretch()
        
        # Close button
        self.close_button = QPushButton("Close")
        self.close_button.setMinimumHeight(35)
        controls_layout.addWidget(self.close_button)
        
        main_layout.addLayout(controls_layout)
        
    def setup_connections(self):
        """Set up signal connections."""
        self.load_image_button.clicked.connect(self.load_image)
        self.apply_button.clicked.connect(self.apply_classifier)
        self.save_result_button.clicked.connect(self.save_result)
        self.close_button.clicked.connect(self.close)
        
        # Sync zoom and pan between views
        self.original_view.zoomChanged.connect(self.sync_zoom)
        self.result_view.zoomChanged.connect(self.sync_zoom)
        
        # Connect scroll bars for pan synchronization
        self.original_view.horizontalScrollBar().valueChanged.connect(self.sync_horizontal_scroll)
        self.original_view.verticalScrollBar().valueChanged.connect(self.sync_vertical_scroll)
        self.result_view.horizontalScrollBar().valueChanged.connect(self.sync_horizontal_scroll)
        self.result_view.verticalScrollBar().valueChanged.connect(self.sync_vertical_scroll)
        
    def sync_zoom(self, zoom_factor):
        """Synchronize zoom between original and result views."""
        sender = self.sender()
        if sender == self.original_view:
            # Disconnect to avoid infinite loop
            self.result_view.zoomChanged.disconnect(self.sync_zoom)
            self.result_view.zoom_level = zoom_factor
            self.result_view.resetTransform()
            self.result_view.scale(zoom_factor, zoom_factor)
            # Reconnect
            self.result_view.zoomChanged.connect(self.sync_zoom)
        elif sender == self.result_view:
            # Disconnect to avoid infinite loop
            self.original_view.zoomChanged.disconnect(self.sync_zoom)
            self.original_view.zoom_level = zoom_factor
            self.original_view.resetTransform()
            self.original_view.scale(zoom_factor, zoom_factor)
            # Reconnect
            self.original_view.zoomChanged.connect(self.sync_zoom)
    
    def sync_horizontal_scroll(self, value):
        """Synchronize horizontal scrolling between views."""
        sender = self.sender()
        if sender == self.original_view.horizontalScrollBar():
            # Disconnect to avoid infinite loop
            self.result_view.horizontalScrollBar().valueChanged.disconnect(self.sync_horizontal_scroll)
            self.result_view.horizontalScrollBar().setValue(value)
            # Reconnect
            self.result_view.horizontalScrollBar().valueChanged.connect(self.sync_horizontal_scroll)
        elif sender == self.result_view.horizontalScrollBar():
            # Disconnect to avoid infinite loop
            self.original_view.horizontalScrollBar().valueChanged.disconnect(self.sync_horizontal_scroll)
            self.original_view.horizontalScrollBar().setValue(value)
            # Reconnect
            self.original_view.horizontalScrollBar().valueChanged.connect(self.sync_horizontal_scroll)
    
    def sync_vertical_scroll(self, value):
        """Synchronize vertical scrolling between views."""
        sender = self.sender()
        if sender == self.original_view.verticalScrollBar():
            # Disconnect to avoid infinite loop
            self.result_view.verticalScrollBar().valueChanged.disconnect(self.sync_vertical_scroll)
            self.result_view.verticalScrollBar().setValue(value)
            # Reconnect
            self.result_view.verticalScrollBar().valueChanged.connect(self.sync_vertical_scroll)
        elif sender == self.result_view.verticalScrollBar():
            # Disconnect to avoid infinite loop
            self.original_view.verticalScrollBar().valueChanged.disconnect(self.sync_vertical_scroll)
            self.original_view.verticalScrollBar().setValue(value)
            # Reconnect
            self.original_view.verticalScrollBar().valueChanged.connect(self.sync_vertical_scroll)
        
    def load_image(self):
        """Load a new image for classification."""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Select Image for Classification",
            "",
            "Image Files (*.png *.jpg *.jpeg *.bmp *.tiff *.tif);;All Files (*)"
        )
        
        if not file_path:
            return
            
        try:
            # Load and display the image
            image = cv2.imread(file_path)
            if image is None:
                QMessageBox.critical(self, "Error", f"Failed to load image: {file_path}")
                return
                
            image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            self.current_image = image_rgb
            self.current_image_path = file_path
            
            # Display the image
            self.display_image(self.original_view, image_rgb)
            
            # Update image info
            h, w, c = image_rgb.shape
            file_size = os.path.getsize(file_path) / (1024 * 1024)  # MB
            info_text = f"File: {os.path.basename(file_path)}\n"
            info_text += f"Size: {w} x {h} pixels\n"
            info_text += f"Channels: {c}\n"
            info_text += f"File Size: {file_size:.2f} MB"
            self.image_info_label.setText(info_text)
            
            # Enable apply button
            self.apply_button.setEnabled(True)
            
            # Clear previous result
            self.current_result = None
            self.result_view.clear()
            self.result_info_label.setText("No segmentation result")
            self.save_result_button.setEnabled(False)
            
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Error loading image: {str(e)}")
            
    def display_image(self, view, image):
        """Display an image in the specified view."""
        if image is None:
            return
            
        # Convert to QPixmap
        h, w, ch = image.shape
        bytes_per_line = ch * w
        
        if ch == 3:
            from PySide6.QtGui import QImage
            q_image = QImage(image.data, w, h, bytes_per_line, QImage.Format.Format_RGB888)
        else:
            # Handle grayscale or other formats
            q_image = QImage(image.data, w, h, bytes_per_line, QImage.Format.Format_Grayscale8)
            
        pixmap = QPixmap.fromImage(q_image)
        # Use KeepAspectRatioByExpanding to minimize white space
        view.set_image(pixmap, Qt.AspectRatioMode.KeepAspectRatioByExpanding)
        
    def apply_classifier(self):
        """Apply the classifier to the current image."""
        if self.current_image is None:
            QMessageBox.warning(self, "Warning", "Please load an image first.")
            return
            
        # Show progress bar
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        
        # Disable buttons during processing
        self.apply_button.setEnabled(False)
        self.load_image_button.setEnabled(False)
        
        # Create worker thread
        self.worker_thread = QThread()
        self.worker = ClassifierWorker(
            self.current_image_path,
            self.classifier,
            self.feature_params,
            self.label_mapping
        )
        self.worker.moveToThread(self.worker_thread)
        
        # Connect signals
        self.worker_thread.started.connect(self.worker.run)
        self.worker.progress_updated.connect(self.progress_bar.setValue)
        self.worker.result_ready.connect(self.on_result_ready)
        self.worker.error_occurred.connect(self.on_error)
        self.worker_thread.finished.connect(self.worker_thread.deleteLater)
        
        # Start processing
        self.worker_thread.start()
        
    def on_result_ready(self, original_image, result):
        """Handle the segmentation result."""
        self.current_result = result
        
        # Create colored segmentation image
        colored_result = self.create_colored_segmentation(result)
        
        # Display the result
        self.display_image(self.result_view, colored_result)
        
        # Update result info
        unique_labels = np.unique(result)
        unique_labels = unique_labels[unique_labels > 0]  # Exclude background
        info_text = f"Segmentation completed\n"
        info_text += f"Unique segments: {len(unique_labels)}\n"
        info_text += f"Labels: {', '.join(map(str, unique_labels))}"
        self.result_info_label.setText(info_text)
        
        # Enable save button
        self.save_result_button.setEnabled(True)
        
        # Clean up
        self.cleanup_worker()
        
    def on_error(self, error_message):
        """Handle processing errors."""
        QMessageBox.critical(self, "Error", error_message)
        self.cleanup_worker()
        
    def cleanup_worker(self):
        """Clean up worker thread and re-enable UI."""
        if self.worker_thread:
            self.worker_thread.quit()
            self.worker_thread.wait()
            self.worker_thread = None
            
        # Hide progress bar and re-enable buttons
        self.progress_bar.setVisible(False)
        self.apply_button.setEnabled(True)
        self.load_image_button.setEnabled(True)
        
    def create_colored_segmentation(self, result):
        """Create a colored segmentation image."""
        if result is None:
            return None
            
        # Create RGB image
        colored = np.zeros((*result.shape, 3), dtype=np.uint8)
        
        # Use mask colors if available, otherwise generate colors
        if self.mask_colors is not None:
            for label in np.unique(result):
                if label > 0 and label <= len(self.mask_colors):
                    mask = result == label
                    colored[mask] = self.mask_colors[label - 1]  # mask_colors is 0-indexed
        else:
            # Generate colors for each label
            unique_labels = np.unique(result)
            unique_labels = unique_labels[unique_labels > 0]  # Exclude background
            
            for i, label in enumerate(unique_labels):
                # Generate a color based on the label
                hue = (i * 137.5) % 360  # Golden angle for good color distribution
                color = self.hsv_to_rgb(hue, 0.7, 0.9)
                mask = result == label
                colored[mask] = color
                
        return colored
        
    def hsv_to_rgb(self, h, s, v):
        """Convert HSV to RGB color."""
        import colorsys
        r, g, b = colorsys.hsv_to_rgb(h/360, s, v)
        return [int(r*255), int(g*255), int(b*255)]
        
    def save_result(self):
        """Save the segmentation result as PNG image."""
        if self.current_result is None:
            QMessageBox.warning(self, "Warning", "No segmentation result to save.")
            return
            
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "Save Segmentation Result",
            "",
            "PNG Files (*.png)"
        )
        
        if not file_path:
            return
            
        try:
            # Ensure .png extension
            if not file_path.endswith('.png'):
                file_path += '.png'
                
            # Save as colored image
            colored_result = self.create_colored_segmentation(self.current_result)
            colored_bgr = cv2.cvtColor(colored_result, cv2.COLOR_RGB2BGR)
            cv2.imwrite(file_path, colored_bgr)
                
            QMessageBox.information(self, "Success", f"Result saved to: {file_path}")
            
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Error saving result: {str(e)}")
            
    def closeEvent(self, event):
        """Handle dialog close event."""
        # Clean up worker thread if running
        if self.worker_thread and self.worker_thread.isRunning():
            self.worker_thread.quit()
            self.worker_thread.wait()
            
        event.accept()