# core/analysis.py

import logging
import math
import locale
import cv2
import numpy as np
import pandas as pd
import torch
from typing import List, Tuple, Union, Callable, Optional, Any # For type hinting

logger = logging.getLogger(__name__)

# --- Constants ---
SOLIDITY_THRESHOLD = 1.1  # Solidity should theoretically be <= 1. Allow a small tolerance.

# --- Locale Setup ---
# Try setting locale once, maybe at app startup or keep it simple here
try:
    locale.setlocale(locale.LC_ALL, '')
except locale.Error:
    try:
        locale.setlocale(locale.LC_ALL, 'en_US.UTF-8')
    except locale.Error:
        try:
            locale.setlocale(locale.LC_ALL, 'C')
        except locale.Error:
             logger.warning("Failed to set locale. Formatting might use default.")

# --- Utility Functions ---
def calculate_pixel_length(start_point: Tuple[float, float], end_point: Tuple[float, float]) -> float:
    """Calculates the Euclidean distance between two points."""
    # Note: Original used QPointF. This uses simple tuples.
    return math.dist(start_point, end_point)

def compute_length_width_feret(contour: np.ndarray) -> Tuple[float, float]:
    """
    Computes the Length and Width using Feret diameter (minimum area bounding rectangle).
    Length is the longer side, Width is the shorter side.

    Args:
        contour (np.ndarray): OpenCV contour (Nx1x2 or Nx2).

    Returns:
        Tuple[float, float]: (length, width) in pixels. Returns (0, 0) on error.
    """
    if contour is None or len(contour) < 3:
        logger.warning("Contour too small or None for Feret calculation.")
        return 0, 0

    try:
        # Ensure contour is Nx2
        if contour.shape[-1] != 2:
             contour = contour.reshape(-1, 2)

        # Calculate convex hull - ensure it's also Nx2
        hull = cv2.convexHull(contour)
        if hull is None or len(hull) < 3:
            logger.warning("Convex hull could not be computed or is too small.")
            return 0, 0
        if hull.shape[-1] != 2:
             hull = hull.reshape(-1, 2)

        # Use Minimum Area Rectangle
        min_rect = cv2.minAreaRect(hull) # ((center_x, center_y), (width, height), angle)
        (w, h) = min_rect[1] # width and height of the rectangle

        # Assign longer side to length, shorter to width
        length = max(w, h)
        width = min(w, h)

        if length <= 0 or width <= 0:
             logger.warning(f"MinAreaRect returned non-positive dimensions: Length={length}, Width={width}")
             return 0, 0

        return length, width

    except cv2.error as e:
        logger.error(f"OpenCV error in compute_length_width_feret: {e}")
        logger.debug(f"Contour shape: {contour.shape}, dtype: {contour.dtype}")
        return 0, 0
    except Exception as e:
        logger.exception(f"Unexpected error in compute_length_width_feret: {e}")
        return 0, 0


# --- Main Analysis Function ---
def calculate_parameters(
    annotations: Union[List[torch.Tensor], torch.Tensor],
    scale_factor: float,
    progress_callback: Optional[Callable[[int], None]] = None
) -> Tuple[pd.DataFrame, np.ndarray]:
    # Try to free up GPU memory before processing
    if torch.cuda.is_available():
        try:
            torch.cuda.empty_cache()
            logger.info("Cleared CUDA cache before parameter calculation")
        except Exception as e:
            logger.warning(f"Failed to clear CUDA cache: {e}")
    """
    Calculates morphological parameters for each segmented object mask.

    Args:
        annotations (Union[List[torch.Tensor], torch.Tensor]):
            A list or tensor containing individual binary mask tensors (H, W, dtype=torch.uint8 or bool).
            Masks are expected to be on CPU or GPU, they will be moved to CPU internally.
        scale_factor (float): The scale factor in micrometers per pixel (µm/pixel).
        progress_callback (Optional[Callable[[int], None]]):
            An optional function to report progress (0-100). It will be called with the percentage.

    Returns:
        Tuple[pd.DataFrame, np.ndarray]:
            - pd.DataFrame: DataFrame containing calculated parameters for each *valid* object.
                            Indexed by the original position in the input `annotations` list/tensor.
            - np.ndarray: A boolean mask of the same length as the input `annotations`,
                          where True indicates the annotation was valid and included in the DataFrame.
                          Returns empty DataFrame and empty array if no valid objects are found.
    """
    if isinstance(annotations, torch.Tensor):
        total_objects = annotations.shape[0]
    elif isinstance(annotations, list):
        total_objects = len(annotations)
    else:
        logger.error(f"Invalid annotations type: {type(annotations)}. Expected list or torch.Tensor.")
        return pd.DataFrame(), np.array([], dtype=bool)

    if total_objects == 0:
        logger.info("No annotations provided for parameter calculation.")
        return pd.DataFrame(), np.array([], dtype=bool)

    # Track which original annotations remain valid throughout the process
    valid_mask_tracker = np.ones(total_objects, dtype=bool)
    results_list = [] # Store results dictionaries

    logger.info(f"Calculating parameters for {total_objects} annotations with scale {scale_factor:.4f} µm/pixel.")

    for i in range(total_objects):
        if progress_callback:
            progress_value = int(((i + 1) / total_objects) * 100) # Report 1-100
            try:
                progress_callback(progress_value)
            except Exception as cb_e:
                logger.warning(f"Progress callback failed for step {i}: {cb_e}", exc_info=True) # Log but continue

        try:
            # Get the current mask
            if isinstance(annotations, torch.Tensor):
                mask_tensor = annotations[i]
            else: # Must be a list
                mask_tensor = annotations[i]

            if mask_tensor is None:
                logger.warning(f"Annotation {i} is None, skipping.")
                valid_mask_tracker[i] = False
                continue

            # --- Ensure mask is numpy uint8 on CPU ---
            if not isinstance(mask_tensor, torch.Tensor):
                 logger.warning(f"Annotation {i} is not a tensor ({type(mask_tensor)}), skipping.")
                 valid_mask_tracker[i] = False
                 continue

            # Ensure tensor is 2D (H, W)
            if mask_tensor.ndim != 2:
                logger.warning(f"Annotation {i} has incorrect dimensions ({mask_tensor.ndim}D), expected 2D. Skipping.")
                valid_mask_tracker[i] = False
                continue

            # Move to CPU, convert to numpy uint8
            try:
                # Handle boolean or float tensors from segmentation
                if mask_tensor.dtype == torch.bool:
                    try:
                        # Try direct conversion first
                        binary_mask_np = mask_tensor.cpu().numpy().astype(np.uint8)
                    except RuntimeError as e:
                        # Handle CUDA out-of-memory errors
                        if "CUDA error: out of memory" in str(e):
                            logger.warning(f"CUDA out of memory when converting tensor {i}. Using fallback method.")
                            # Try memory-efficient conversion
                            if mask_tensor.is_cuda:
                                try:
                                    # Create empty numpy array of the same shape
                                    binary_mask_np = np.zeros(mask_tensor.shape, dtype=np.uint8)

                                    # Process in rows to reduce memory usage
                                    chunk_size = 10  # Process 10 rows at a time
                                    height = mask_tensor.shape[0]

                                    for row in range(0, height, chunk_size):
                                        end_row = min(row + chunk_size, height)
                                        # Process a small chunk at a time
                                        chunk = mask_tensor[row:end_row].cpu().numpy()
                                        binary_mask_np[row:end_row] = chunk.astype(np.uint8)

                                    logger.info(f"Successfully converted tensor {i} using chunked approach.")
                                except Exception as chunk_error:
                                    logger.error(f"Chunked conversion failed for tensor {i}: {chunk_error}")
                                    valid_mask_tracker[i] = False
                                    continue
                            else:
                                # If not on CUDA but still OOM, try with clone and detach
                                try:
                                    detached_tensor = mask_tensor.clone().detach()
                                    binary_mask_np = detached_tensor.numpy().astype(np.uint8)
                                    del detached_tensor  # Free up memory
                                except Exception as detach_error:
                                    logger.error(f"Detach method failed for tensor {i}: {detach_error}")
                                    valid_mask_tracker[i] = False
                                    continue
                        else:
                            # If not a CUDA memory error, re-raise
                            raise
                elif mask_tensor.dtype.is_floating_point:
                    logger.debug(f"Annotation {i} is float type, thresholding at 0.5.")
                    try:
                        # Try direct conversion first
                        binary_mask_np = (mask_tensor.cpu().numpy() > 0.5).astype(np.uint8)
                    except RuntimeError as e:
                        # Handle CUDA out-of-memory errors
                        if "CUDA error: out of memory" in str(e):
                            logger.warning(f"CUDA out of memory when converting float tensor {i}. Using fallback method.")
                            # Try memory-efficient conversion
                            if mask_tensor.is_cuda:
                                try:
                                    # Create empty numpy array of the same shape
                                    binary_mask_np = np.zeros(mask_tensor.shape, dtype=np.uint8)

                                    # Process in rows to reduce memory usage
                                    chunk_size = 10  # Process 10 rows at a time
                                    height = mask_tensor.shape[0]

                                    for row in range(0, height, chunk_size):
                                        end_row = min(row + chunk_size, height)
                                        # Process a small chunk at a time
                                        chunk = mask_tensor[row:end_row].cpu().numpy()
                                        binary_mask_np[row:end_row] = (chunk > 0.5).astype(np.uint8)

                                    logger.info(f"Successfully converted float tensor {i} using chunked approach.")
                                except Exception as chunk_error:
                                    logger.error(f"Chunked conversion failed for float tensor {i}: {chunk_error}")
                                    valid_mask_tracker[i] = False
                                    continue
                            else:
                                # If not on CUDA but still OOM, try with clone and detach
                                try:
                                    detached_tensor = mask_tensor.clone().detach()
                                    binary_mask_np = (detached_tensor.numpy() > 0.5).astype(np.uint8)
                                    del detached_tensor  # Free up memory
                                except Exception as detach_error:
                                    logger.error(f"Detach method failed for float tensor {i}: {detach_error}")
                                    valid_mask_tracker[i] = False
                                    continue
                        else:
                            # If not a CUDA memory error, re-raise
                            raise
                elif mask_tensor.dtype == torch.uint8:
                    try:
                        # Try direct conversion first
                        binary_mask_np = mask_tensor.cpu().numpy()
                    except RuntimeError as e:
                        # Handle CUDA out-of-memory errors
                        if "CUDA error: out of memory" in str(e):
                            logger.warning(f"CUDA out of memory when converting uint8 tensor {i}. Using fallback method.")
                            # Try memory-efficient conversion
                            if mask_tensor.is_cuda:
                                try:
                                    # Create empty numpy array of the same shape
                                    binary_mask_np = np.zeros(mask_tensor.shape, dtype=np.uint8)

                                    # Process in rows to reduce memory usage
                                    chunk_size = 10  # Process 10 rows at a time
                                    height = mask_tensor.shape[0]

                                    for row in range(0, height, chunk_size):
                                        end_row = min(row + chunk_size, height)
                                        # Process a small chunk at a time
                                        chunk = mask_tensor[row:end_row].cpu().numpy()
                                        binary_mask_np[row:end_row] = chunk

                                    logger.info(f"Successfully converted uint8 tensor {i} using chunked approach.")
                                except Exception as chunk_error:
                                    logger.error(f"Chunked conversion failed for uint8 tensor {i}: {chunk_error}")
                                    valid_mask_tracker[i] = False
                                    continue
                            else:
                                # If not on CUDA but still OOM, try with clone and detach
                                try:
                                    detached_tensor = mask_tensor.clone().detach()
                                    binary_mask_np = detached_tensor.numpy()
                                    del detached_tensor  # Free up memory
                                except Exception as detach_error:
                                    logger.error(f"Detach method failed for uint8 tensor {i}: {detach_error}")
                                    valid_mask_tracker[i] = False
                                    continue
                        else:
                            # If not a CUDA memory error, re-raise
                            raise
                else:
                    logger.warning(f"Annotation {i} has unsupported dtype ({mask_tensor.dtype}), skipping.")
                    valid_mask_tracker[i] = False
                    continue
            except Exception as conv_e:
                 logger.error(f"Error converting mask {i} to numpy: {conv_e}", exc_info=True)
                 valid_mask_tracker[i] = False
                 continue
            # --- End Mask Conversion ---

            # Find the largest external contour
            contours, _ = cv2.findContours(binary_mask_np, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            if not contours:
                # This mask is empty or invalid after conversion/thresholding
                # logger.debug(f"No contours found for annotation {i}, skipping.") # Often verbose
                valid_mask_tracker[i] = False
                continue

            contour = max(contours, key=cv2.contourArea)

            # Check minimum contour points for ellipse/rect fitting
            if len(contour) < 5: # Needed for fitEllipse, robust for minAreaRect too
                logger.warning(f"Contour for annotation {i} has < 5 points ({len(contour)}), skipping.")
                valid_mask_tracker[i] = False
                continue

            # Ensure contour is Nx2 shape
            contour_squeezed = contour.squeeze()
            if contour_squeezed.ndim != 2 or contour_squeezed.shape[1] != 2:
                 # Attempt reshape if possible, otherwise skip
                 try:
                     contour_squeezed = contour_squeezed.reshape(-1, 2)
                 except ValueError:
                     logger.warning(f"Could not reshape contour {i} to Nx2. Shape: {contour_squeezed.shape}. Skipping.")
                     valid_mask_tracker[i] = False
                     continue

            # 1. Centroid
            M = cv2.moments(contour)
            if M["m00"] <= 1e-6: # Use tolerance for float comparison
                logger.warning(f"Invalid moments m00 ({M['m00']:.2e}) for annotation {i}, skipping.")
                valid_mask_tracker[i] = False
                continue
            center_x = int(M["m10"] / M["m00"])
            center_y = int(M["m01"] / M["m00"])

            # 2. Area (Pixel & Micron) - Use contour area
            area_pixel = cv2.contourArea(contour)
            if area_pixel <= 1e-6:
                 logger.warning(f"Contour area is near zero ({area_pixel:.2e}) for annotation {i}, skipping.")
                 valid_mask_tracker[i] = False
                 continue
            area_micron = area_pixel * (scale_factor ** 2)

            # 3. Perimeter (Pixel & Micron)
            perimeter_pixel = cv2.arcLength(contour, True)
            if perimeter_pixel <= 1e-6:
                 logger.warning(f"Perimeter is near zero ({perimeter_pixel:.2e}) for annotation {i}, skipping.")
                 valid_mask_tracker[i] = False
                 continue
            perimeter_micron = perimeter_pixel * scale_factor

            # 4. Convex Hull and related properties
            hull = cv2.convexHull(contour)
            if hull is None or len(hull) < 3:
                 logger.warning(f"Convex hull computation failed or hull too small for annotation {i}, skipping.")
                 valid_mask_tracker[i] = False
                 continue

            convex_area_pixel = cv2.contourArea(hull)
            if convex_area_pixel <= 1e-6:
                 logger.warning(f"Convex area is near zero ({convex_area_pixel:.2e}) for annotation {i}, skipping.")
                 valid_mask_tracker[i] = False
                 continue

            convex_perimeter_pixel = cv2.arcLength(hull, True)
            convex_perimeter_micron = convex_perimeter_pixel * scale_factor

            # 5. Solidity
            solidity = area_pixel / convex_area_pixel if convex_area_pixel > 1e-6 else 0
            if solidity > SOLIDITY_THRESHOLD or solidity <= 0:
                logger.warning(f"Annotation {i} failed solidity check ({solidity:.3f}), skipping.")
                valid_mask_tracker[i] = False
                continue

            # 6. Convexity
            convexity = convex_perimeter_micron / perimeter_micron if perimeter_micron > 1e-6 else 0

            # 7. Compactness (also known as Circularity by some definitions)
            compactness = (4 * np.pi * area_micron) / (perimeter_micron ** 2) if perimeter_micron > 1e-6 else 0

            # 8. Circle-Equivalent Diameter (ECD)
            equivalent_diameter = np.sqrt(4 * area_micron / np.pi)

            # 9. Length & Width (using MinAreaRect on Convex Hull for robustness)
            # Pass the hull to the helper function
            length_pixel, width_pixel = compute_length_width_feret(hull)
            if length_pixel <= 1e-6 or width_pixel <= 1e-6:
                 logger.warning(f"Invalid length/width ({length_pixel:.2e}, {width_pixel:.2e}) for annotation {i}, skipping.")
                 valid_mask_tracker[i] = False
                 continue
            length_micron = length_pixel * scale_factor
            width_micron = width_pixel * scale_factor

            # Ensure Length >= Width (already handled in compute_length_width_feret)

            # 10. Elongation (Aspect Ratio reciprocal)
            elongation = width_micron / length_micron if length_micron > 1e-6 else 0

            # 11. Roundness (different from Compactness/Circularity, relates area to max Feret diameter)
            roundness = (4 * area_micron) / (np.pi * (length_micron ** 2)) if length_micron > 1e-6 else 0

            # 12. Rectangularity (Area / MinAreaRect Area)
            min_rect_area_micron = length_micron * width_micron
            rectangularity = area_micron / min_rect_area_micron if min_rect_area_micron > 1e-6 else 0

            # 13. Ellipticity (1 - Aspect Ratio)
            ellipticity = (length_micron - width_micron) / length_micron if length_micron > 1e-6 else 0

            # Store results for this valid object, associating with original index 'i'
            results_list.append({
                'Original Index': i, # Store the original index
                'Center_X (px)': center_x,
                'Center_Y (px)': center_y,
                'Area (µm²)': round(area_micron, 3),
                'Perimeter (µm)': round(perimeter_micron, 3),
                'Compactness': round(compactness, 3),
                'Circle-Equivalent Diameter (µm)': round(equivalent_diameter, 3),
                'Length (µm)': round(length_micron, 3),
                'Width (µm)': round(width_micron, 3),
                'Elongation': round(elongation, 3), # Aspect Ratio W/L
                'Ellipticity': round(ellipticity, 3),
                'Rectangularity': round(rectangularity, 3),
                'Solidity': round(solidity, 3),
                'Convexity': round(convexity, 3),
                'Roundness': round(roundness, 3),
            })

        except cv2.error as cv_err:
            logger.error(f"OpenCV error processing annotation {i}: {cv_err}", exc_info=True)
            valid_mask_tracker[i] = False
            continue
        except Exception as e:
            logger.exception(f"Error calculating parameters for annotation {i}: {e}")
            valid_mask_tracker[i] = False
            continue

    # --- Final progress update ---
    if progress_callback:
        try:
            progress_callback(100) # Ensure it finishes at 100%
        except Exception as cb_e:
                logger.warning(f"Final progress callback failed: {cb_e}", exc_info=True)

    # --- Create DataFrame from valid results ---
    if not results_list:
         logger.warning("No valid objects found after parameter calculation.")
         return pd.DataFrame(), np.array([], dtype=bool)

    df = pd.DataFrame(results_list)

    # Set the DataFrame index to the original annotation index
    df.set_index('Original Index', inplace=True)
    df.sort_index(inplace=True) # Optional: Keep original order

    # Add the 'Object ID' column based on the index for user display
    df.insert(0, 'Object ID', [f"Object_{idx}" for idx in df.index])

    logger.info(f"Successfully calculated parameters for {len(df)} objects.")

    # The final boolean mask indicating which *original* inputs were valid
    final_valid_mask = valid_mask_tracker

    # Sanity check: number of rows in df should match number of True values in final_valid_mask
    if len(df) != np.sum(final_valid_mask):
         logger.critical(f"CRITICAL ERROR: Mismatch between DataFrame length ({len(df)}) and valid mask count ({np.sum(final_valid_mask)}).")
         # Decide how to handle: return potentially corrupt data or raise error?
         # For safety, maybe return empty results.
         # return pd.DataFrame(), np.array([], dtype=bool)
         # Or raise an exception
         raise RuntimeError("Internal state mismatch during parameter calculation.")


    return df, final_valid_mask