# src/gui/handlers/gallery_handlers.py

import numpy as np
import logging
import cv2
import os
from PySide6.QtCore import Slot
from PySide6.QtGui import QPixmap, QImage
from src.utils.image_utils import resize_image, convert_cvimage_to_qpixmap

logger = logging.getLogger(__name__)

class GalleryHandlers:
    """Handlers for the independent image galleries in each page."""

    @Slot(int)
    def on_process_gallery_remove_clicked(self, index):
        """Handler for when a thumbnail X button is clicked in process gallery."""
        if not hasattr(self, 'process_gallery') or not (0 <= index < len(self.process_gallery.file_paths)):
            return
        
        image_path = self.process_gallery.file_paths[index]
        filename = os.path.basename(image_path)
        
        # Confirm removal
        from PySide6.QtWidgets import QMessageBox
        reply = QMessageBox.question(
            self, "Confirm Removal",
            f"Remove this image from the process gallery?\n{filename}",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            # Manually remove from gallery UI without triggering the signal again
            if 0 <= index < len(self.process_gallery.images):
                # Store the current selection state
                was_selected = (self.process_gallery.selected_index == index)
                
                # Remove the thumbnail widget
                if index < len(self.process_gallery.thumbnails):
                    thumbnail = self.process_gallery.thumbnails.pop(index)
                    thumbnail.deleteLater()
                
                # Remove the image data
                self.process_gallery.images.pop(index)
                self.process_gallery.filenames.pop(index)
                self.process_gallery.file_paths.pop(index)
                
                # Update the selected index
                if was_selected:
                    if len(self.process_gallery.images) > 0:
                        new_index = min(index, len(self.process_gallery.images) - 1)
                        self.process_gallery.selected_index = new_index
                    else:
                        self.process_gallery.selected_index = -1
                elif self.process_gallery.selected_index > index:
                    self.process_gallery.selected_index -= 1
                
                # Update all thumbnail indices
                for i, thumbnail in enumerate(self.process_gallery.thumbnails):
                    thumbnail.index = i
                
                # Update thumbnail styling
                for i, thumbnail in enumerate(self.process_gallery.thumbnails):
                    thumbnail.set_selected(i == self.process_gallery.selected_index)
                
                # Update the count label
                self.process_gallery.update_count_label()
                
                # Update image display/preview after removal
                if self.process_gallery.selected_index >= 0 and len(self.process_gallery.images) > 0:
                    # Load the newly selected image
                    self.on_process_gallery_image_clicked(self.process_gallery.selected_index)
                else:
                    # No images left, clear any displays if they exist
                    pass  # Process gallery may not have a main image display
            
            logger.info(f"Successfully removed image from process gallery: {filename}")

    @Slot(int)
    def on_analysis_gallery_remove_clicked(self, index):
        """Handler for when a thumbnail X button is clicked in analysis gallery."""
        if not hasattr(self, 'analysis_gallery') or not (0 <= index < len(self.analysis_gallery.file_paths)):
            return
        
        image_path = self.analysis_gallery.file_paths[index]
        filename = os.path.basename(image_path)
        
        # Confirm removal
        from PySide6.QtWidgets import QMessageBox
        reply = QMessageBox.question(
            self, "Confirm Removal",
            f"Remove this image from the analysis gallery?\n{filename}",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            # Manually remove from gallery UI without triggering the signal again
            if 0 <= index < len(self.analysis_gallery.images):
                # Store the current selection state
                was_selected = (self.analysis_gallery.selected_index == index)
                
                # Remove the thumbnail widget
                if index < len(self.analysis_gallery.thumbnails):
                    thumbnail = self.analysis_gallery.thumbnails.pop(index)
                    thumbnail.deleteLater()
                
                # Remove the image data
                self.analysis_gallery.images.pop(index)
                self.analysis_gallery.filenames.pop(index)
                self.analysis_gallery.file_paths.pop(index)
                
                # Update the selected index
                if was_selected:
                    if len(self.analysis_gallery.images) > 0:
                        new_index = min(index, len(self.analysis_gallery.images) - 1)
                        self.analysis_gallery.selected_index = new_index
                    else:
                        self.analysis_gallery.selected_index = -1
                elif self.analysis_gallery.selected_index > index:
                    self.analysis_gallery.selected_index -= 1
                
                # Update all thumbnail indices
                for i, thumbnail in enumerate(self.analysis_gallery.thumbnails):
                    thumbnail.index = i
                
                # Update thumbnail styling
                for i, thumbnail in enumerate(self.analysis_gallery.thumbnails):
                    thumbnail.set_selected(i == self.analysis_gallery.selected_index)
                
                # Update the count label
                self.analysis_gallery.update_count_label()
                
                # Update image display/preview after removal
                if self.analysis_gallery.selected_index >= 0 and len(self.analysis_gallery.images) > 0:
                    # Load the newly selected image
                    self.on_analysis_gallery_image_clicked(self.analysis_gallery.selected_index)
                else:
                    # No images left, clear any displays if they exist
                    pass  # Analysis gallery may not have a main image display
            
            logger.info(f"Successfully removed image from analysis gallery: {filename}")

    @Slot(int)
    def on_process_gallery_image_clicked(self, index):
        """Handler for when an image is clicked in the process gallery."""
        if not hasattr(self, 'process_gallery') or index < 0 or index >= len(self.process_gallery.images):
            return

        # Get the image path from the gallery
        image_path = self.process_gallery.file_paths[index]
        self.image_filename = self.process_gallery.filenames[index]
        self.image_full_path = image_path
        self.current_image_index = index

        print(f"DEBUG: Process gallery image clicked: {self.image_filename} at index {index}")

        # Load the image directly from disk to ensure we're using the correct one
        try:
            loaded_image = cv2.imread(image_path)
            if loaded_image is not None:
                loaded_image = cv2.cvtColor(loaded_image, cv2.COLOR_BGR2RGB)
                # Update the image variable
                self.image = loaded_image
                print(f"DEBUG: Loaded image directly from disk: {image_path}")
            else:
                print(f"DEBUG: Failed to load image from disk: {image_path}")
                # Fall back to the gallery image if loading from disk fails
                self.image = self.process_gallery.images[index]
        except Exception as e:
            print(f"DEBUG: Error loading image from disk: {e}")
            # Fall back to the gallery image if loading from disk fails
            self.image = self.process_gallery.images[index]

        # Use switch_image to properly save current state and load new state
        if hasattr(self, 'switch_image'):
            print(f"DEBUG: Calling switch_image with {self.image_full_path}")
            self.switch_image(self.image_full_path)
        else:
            print(f"DEBUG: switch_image method not found, using fallback")
            # Fallback if switch_image is not available
            # Update the image display
            self.image_resized = resize_image(self.image, self.common_size)
            self.display_original_image(self.image_resized)

            # Reset the processed image
            self.processed_image = self.image.copy()
            # Call the appropriate reset method based on context
            if hasattr(self, 'reset_process_image'):
                self.reset_process_image()
            elif hasattr(self, 'reset_analysis_image'):
                self.reset_analysis_image()

    @Slot(int)
    def on_trainable_gallery_image_clicked(self, index):
        """Handler for when an image is clicked in the trainable gallery."""
        print(f"DEBUG: on_trainable_gallery_image_clicked called with index: {index}")
        if not hasattr(self, 'trainable_gallery') or index < 0 or index >= len(self.trainable_gallery.images):
            print("DEBUG: Invalid gallery index")
            return

        # Save current annotations and segmentation results if they exist
        if hasattr(self, 'multi_image_handler') and \
           hasattr(self.multi_image_handler, 'current_image_path') and self.multi_image_handler.current_image_path:
            # Save annotations
            if hasattr(self, 'training_labels') and self.training_labels is not None:
                print(f"DEBUG: Saving annotations for current image: {self.multi_image_handler.current_image_path}")
                # Make a copy to ensure we don't lose the data
                current_annotations = self.training_labels.copy()
                self.multi_image_handler.set_annotations(self.multi_image_handler.current_image_path, current_annotations)
                print(f"DEBUG: Successfully saved annotations with shape {current_annotations.shape}")

            # Save segmentation results
            if hasattr(self, 'result') and self.result is not None:
                print(f"DEBUG: Saving segmentation result for current image: {self.multi_image_handler.current_image_path}")
                # Make a copy to ensure we don't lose the data
                current_result = self.result.copy()
                self.multi_image_handler.set_segmentation_result(self.multi_image_handler.current_image_path, current_result)
                print(f"DEBUG: Successfully saved segmentation result with shape {current_result.shape}")

        # Get the selected image
        self.trainable_image = self.trainable_gallery.images[index]
        self.image_filename = self.trainable_gallery.filenames[index]
        self.image_full_path = self.trainable_gallery.file_paths[index]
        self.current_image_index = index
        print(f"DEBUG: Selected image: {self.image_filename}, path: {self.image_full_path}")

        # Update the multi_image_handler current path
        if hasattr(self, 'multi_image_handler'):
            self.multi_image_handler.current_image_path = self.image_full_path
            if hasattr(self, 'current_image_path'):
                self.current_image_path = self.image_full_path
            print(f"DEBUG: Updated current_image_path: {self.image_full_path}")

            # State loading is now manual
            print("DEBUG: Automatic state loading is disabled. Use 'Load Annotations' button instead.")

            # Don't clear state immediately - we'll do this after loading the new image's state
            print("DEBUG: Preparing to load new image state (not clearing current state yet)")

            # Save current label settings before loading new annotations
            saved_label_names = None
            saved_label_colors = None

            if hasattr(self, 'label_names'):
                saved_label_names = self.label_names.copy() if isinstance(self.label_names, dict) else self.label_names
                print(f"DEBUG: Saved current label names: {saved_label_names}")

            if hasattr(self, 'mask_colors') and self.mask_colors is not None:
                saved_label_colors = self.mask_colors.copy()
                print(f"DEBUG: Saved current mask colors with shape: {saved_label_colors.shape}")

            # Check if we have an annotation file path for this image
            annotation_file = None
            if hasattr(self, 'annotation_path_manager'):
                annotation_file = self.annotation_path_manager.get_annotation_path(self.image_full_path)
                if annotation_file:
                    print(f"DEBUG: Found annotation file {annotation_file} for image {self.image_full_path}")

                    # Load the annotations from the file
                    try:
                        # We need to temporarily override the open_file_dialog method to return our annotation file
                        original_open_file_dialog = self.open_file_dialog
                        self.open_file_dialog = lambda *args, **kwargs: annotation_file
                        try:
                            print(f"DEBUG: Loading annotations from {annotation_file}")
                            # Pass the annotation file directly and set show_messages=False to suppress messages
                            self.load_exported_annotations(file_path=annotation_file, show_messages=False)
                            print(f"DEBUG: Successfully loaded annotations from {annotation_file}")

                            # We need to restore the saved label settings AFTER loading the annotations
                            # This ensures that our custom colors take precedence over the ones in the annotation file

                            # First, let the load_exported_annotations method complete
                            # Then, restore our saved settings

                            # Restore saved label names
                            if saved_label_names is not None:
                                self.label_names = saved_label_names
                                print(f"DEBUG: Restored label names: {self.label_names}")

                                # Save to label settings manager
                                if hasattr(self, 'label_settings_manager'):
                                    self.label_settings_manager.update_label_names(self.label_names)
                                    print(f"DEBUG: Saved restored label names to label settings manager")

                            # Restore saved label colors - this is the critical part
                            if saved_label_colors is not None:
                                # Ensure we have enough colors for all labels
                                max_label = max(self.label_names.keys()) if isinstance(self.label_names, dict) and self.label_names else 0
                                if max_label > saved_label_colors.shape[0]:
                                    # Need to extend the colors array
                                    new_colors = np.zeros((max_label, 3), dtype=np.uint8)
                                    new_colors[:saved_label_colors.shape[0]] = saved_label_colors

                                    # Generate random colors for new labels
                                    import random
                                    for i in range(saved_label_colors.shape[0], max_label):
                                        new_colors[i] = [random.randint(0, 255), random.randint(0, 255), random.randint(0, 255)]

                                    saved_label_colors = new_colors

                                # Override the mask_colors with our saved colors
                                self.mask_colors = saved_label_colors
                                print(f"DEBUG: Restored mask colors with shape: {self.mask_colors.shape}")

                                # Save to label settings manager
                                if hasattr(self, 'label_settings_manager'):
                                    colors_dict = {}
                                    for i, color in enumerate(self.mask_colors):
                                        if isinstance(color, np.ndarray):
                                            color = color.tolist()
                                        colors_dict[i+1] = color
                                    self.label_settings_manager.update_label_colors(colors_dict)
                                    print(f"DEBUG: Saved restored mask colors to label settings manager")

                                # Also update the multi_image_handler with our saved colors
                                if hasattr(self, 'multi_image_handler'):
                                    self.multi_image_handler.mask_colors = self.mask_colors.copy()
                                    print(f"DEBUG: Updated multi_image_handler mask colors to match saved colors")

                                # Force a refresh of the display to show our custom colors
                                self.display_trainable_image()
                                print(f"DEBUG: Refreshed display to show custom colors")

                            # Update the label selection combo box
                            if hasattr(self, 'update_label_color_indicator'):
                                self.update_label_color_indicator()
                                print(f"DEBUG: Updated label selection combo box")

                            # Check if there are existing segmentation results in the multi_image_handler
                            # CRITICAL: We need to do this even when loading annotations from a file
                            self.result = self.multi_image_handler.get_segmentation_result(self.image_full_path)
                            if self.result is not None:
                                print(f"DEBUG: Found existing segmentation result for {self.image_full_path} after loading annotations from file")
                                # We'll display the result after setting up the image
                            else:
                                print(f"DEBUG: No segmentation result found for {self.image_full_path} after loading annotations from file")
                                self.result = None

                            # Continue with the rest of the method to ensure segmentation results are displayed
                            # DO NOT return early
                        except Exception as e:
                            print(f"DEBUG: Error loading annotations from file: {e}")
                        finally:
                            # Restore the original open_file_dialog method
                            self.open_file_dialog = original_open_file_dialog
                    except Exception as e:
                        print(f"DEBUG: Error loading annotations from file: {e}")

            # Now safely clear the old state and load the new image's state
            print(f"DEBUG: Clearing old state and loading new state for {self.image_full_path}")
            self.training_labels = None
            self.result = None
            
            # Always get fresh annotations from multi_image_handler for the selected image
            print(f"DEBUG: Getting annotations for {self.image_full_path} from multi_image_handler")
            existing_annotations = self.multi_image_handler.get_annotations(self.image_full_path)

            if existing_annotations is not None:
                print(f"DEBUG: Found existing annotations in multi_image_handler for {self.image_full_path}")
                self.training_labels = existing_annotations.copy()  # Make a copy to prevent reference issues
            else:
                print(f"DEBUG: No existing annotations found for {self.image_full_path}, creating empty labels")
                self.training_labels = np.zeros(self.trainable_image.shape[:2], dtype=np.uint8)
                self.multi_image_handler.set_annotations(self.image_full_path, self.training_labels.copy())

            # Restore saved label settings
            if saved_label_names is not None:
                self.label_names = saved_label_names
                print(f"DEBUG: Restored label names: {self.label_names}")

                # Save to label settings manager
                if hasattr(self, 'label_settings_manager'):
                    self.label_settings_manager.update_label_names(self.label_names)
                    print(f"DEBUG: Saved restored label names to label settings manager")

            if saved_label_colors is not None:
                # Ensure we have enough colors for all labels
                if isinstance(self.label_names, dict) and self.label_names:
                    max_label = max(self.label_names.keys())
                    if max_label > saved_label_colors.shape[0]:
                        # Need to extend the colors array
                        new_colors = np.zeros((max_label, 3), dtype=np.uint8)
                        new_colors[:saved_label_colors.shape[0]] = saved_label_colors

                        # Generate random colors for new labels
                        import random
                        for i in range(saved_label_colors.shape[0], max_label):
                            new_colors[i] = [random.randint(0, 255), random.randint(0, 255), random.randint(0, 255)]

                        saved_label_colors = new_colors

                # Override the mask_colors with our saved colors
                self.mask_colors = saved_label_colors
                print(f"DEBUG: Restored mask colors with shape: {self.mask_colors.shape}")

                # Save to label settings manager
                if hasattr(self, 'label_settings_manager'):
                    colors_dict = {}
                    for i, color in enumerate(self.mask_colors):
                        if isinstance(color, np.ndarray):
                            color = color.tolist()
                        colors_dict[i+1] = color
                    self.label_settings_manager.update_label_colors(colors_dict)
                    print(f"DEBUG: Saved restored mask colors to label settings manager")

                # Also update the multi_image_handler with our saved colors
                if hasattr(self, 'multi_image_handler'):
                    self.multi_image_handler.mask_colors = self.mask_colors.copy()
                    print(f"DEBUG: Updated multi_image_handler mask colors to match saved colors")

                # Force a refresh of the display to show our custom colors
                self.display_trainable_image()
                print(f"DEBUG: Refreshed display to show custom colors")

            # Update the label selection combo box
            if hasattr(self, 'update_label_color_indicator'):
                self.update_label_color_indicator()
                print(f"DEBUG: Updated label selection combo box")

            # Check if there are existing segmentation results in the multi_image_handler
            # Use the current_image_path from multi_image_handler to ensure consistency
            current_path = self.multi_image_handler.current_image_path
            print(f"DEBUG: Retrieving segmentation result using current_path: {current_path}")
            self.result = self.multi_image_handler.get_segmentation_result(current_path)
            if self.result is not None:
                print(f"DEBUG: Found existing segmentation result for {current_path}")
                # We'll display the result after setting up the image
            else:
                print(f"DEBUG: No segmentation result found for {current_path}")
                self.result = None

            # Retrieve classifier from multi_image_handler
            print("DEBUG: Retrieving classifier from multi_image_handler")
            classifier, feature_params, label_mapping, mask_colors = self.multi_image_handler.get_classifier()

            # Always update the classifier if it exists in the multi_image_handler
            if classifier is not None:
                print("DEBUG: Found classifier in multi_image_handler, updating instance")
                self.classifier = classifier

                # Update feature parameters if they exist
                if feature_params is not None:
                    print(f"DEBUG: Updating feature parameters: {feature_params}")
                    self.feature_params = feature_params
                else:
                    print("DEBUG: No feature parameters found in multi_image_handler")
                    # Set default feature parameters if not found
                    self.feature_params = {
                        'intensity': True,
                        'edges': True,
                        'texture': True,
                        'sigma_min': 1,
                        'sigma_max': 16,
                        'num_sigma': 10
                    }
                    print(f"DEBUG: Using default feature parameters: {self.feature_params}")

                # Update label mapping if it exists
                if hasattr(self, 'label_mapping') and label_mapping is not None:
                    print(f"DEBUG: Updating label mapping: {label_mapping}")
                    self.label_mapping = label_mapping

                # Update mask colors if they exist
                if mask_colors is not None and hasattr(self, 'mask_colors'):
                    print("DEBUG: Updating mask colors")
                    # Only update if the shape is compatible
                    if isinstance(mask_colors, np.ndarray) and mask_colors.shape[0] <= self.mask_colors.shape[0]:
                        self.mask_colors[:mask_colors.shape[0]] = mask_colors
                    elif isinstance(mask_colors, list) and len(mask_colors) <= self.mask_colors.shape[0]:
                        self.mask_colors[:len(mask_colors)] = np.array(mask_colors)
            else:
                print("DEBUG: No classifier found in multi_image_handler")

            print(f"DEBUG: Classifier status after retrieval: {self.classifier is not None}")

        # Update the image display with annotations
        if hasattr(self, 'display_trainable_image'):
            # Use the specialized display method if available
            self.display_trainable_image()
        else:
            # Fallback to basic display
            self.image_resized = resize_image(self.trainable_image, self.common_size)
            pixmap = convert_cvimage_to_qpixmap(self.image_resized)
            self.trainable_original_view.setPixmap(pixmap)
            # Also update the side-by-side view
            self.trainable_original_sync_view.setPixmap(pixmap)

        # Display segmentation results if available
        if hasattr(self, 'result') and self.result is not None:
            if hasattr(self, 'display_segmentation_result'):
                print("DEBUG: Displaying segmentation result")
                self.display_segmentation_result()
            else:
                print("DEBUG: display_segmentation_result method not found")
        else:
            # Clear result view if no segmentation results
            if hasattr(self, 'trainable_result_view'):
                self.trainable_result_view.clear()
        
        # Always switch to the annotation tab (original image) by default when switching images
        if hasattr(self, 'trainable_tab_widget'):
            self.trainable_tab_widget.setCurrentIndex(0)
            print("DEBUG: Switched to annotation tab (original image) by default")

        # Update image info
        if hasattr(self, 'trainable_image_info_label'):
            h, w, c = self.trainable_image.shape
            # Use 1-based indexing for display to match update_image_counter method
            display_index = self.current_image_index + 1
            total_images = len(self.trainable_gallery.images) if hasattr(self, 'trainable_gallery') else 0
            info_text = f"Image: {self.image_filename} ({display_index} of {total_images})\nSize: {w}x{h}\nChannels: {c}"
            self.trainable_image_info_label.setText(info_text)
            print(f"DEBUG: Updated image info for index {index} to: {info_text.replace(chr(10), ' | ')}")

            # Also update the display to ensure the image is shown with the correct annotations
            if hasattr(self, 'display_trainable_image'):
                self.display_trainable_image()

        # Initialize SAM if available - CRITICAL: This must happen BEFORE any SAM operations
        if hasattr(self, 'sam_handler'):
            success = self.sam_handler.set_image(self.trainable_image)
            print(f"DEBUG: Set image in SAM handler for index {index}, success: {success}")

            # Reset any SAM-related temporary data
            if hasattr(self, 'sam_bbox'):
                self.sam_bbox = None
            if hasattr(self, 'drawing_sam_bbox'):
                self.drawing_sam_bbox = False
            if hasattr(self, 'temp_sam_prediction'):
                self.temp_sam_prediction = None
            if hasattr(self, 'point_prompts'):
                self.point_prompts = []

    @Slot(int)
    def on_analysis_gallery_image_clicked(self, index):
        """Handler for when an image is clicked in the analysis gallery."""
        # Check if we have a valid gallery and index
        if not hasattr(self, 'analysis_gallery'):
            logger.warning("Analysis gallery not found")
            return

        # Check if the gallery has any images
        if len(self.analysis_gallery.images) == 0:
            logger.warning("Analysis gallery is empty")
            return

        # Check if the index is valid
        if index < 0 or index >= len(self.analysis_gallery.images):
            logger.warning(f"Invalid index {index} for analysis gallery with {len(self.analysis_gallery.images)} images")
            return

        # Get the selected image from the gallery
        gallery_image = self.analysis_gallery.images[index]

        # Convert the image to a numpy array if it's not already
        if isinstance(gallery_image, np.ndarray):
            # Already a numpy array, use as is
            numpy_image = gallery_image
        elif isinstance(gallery_image, (QPixmap, QImage)):
            # Convert QPixmap/QImage to numpy array
            if isinstance(gallery_image, QPixmap):
                qimg = gallery_image.toImage()
            else:
                qimg = gallery_image

            # Convert QImage to numpy array
            width = qimg.width()
            height = qimg.height()
            ptr = qimg.constBits()
            ptr.setsize(height * width * 4)  # 4 bytes per pixel (RGBA)

            # Create numpy array from the image data
            arr = np.array(ptr).reshape(height, width, 4)  # RGBA format
            numpy_image = arr[:, :, :3]  # Convert to RGB by taking first 3 channels
        else:
            # Unsupported image type
            logger.error(f"Unsupported image type: {type(gallery_image)}")
            return

        # Store the numpy image in the appropriate variable
        if hasattr(self, 'analysis_image'):
            self.analysis_image = numpy_image
        else:
            # Fallback to shared variables if independent ones don't exist
            self.image = numpy_image

        self.image_filename = self.analysis_gallery.filenames[index]
        self.image_full_path = self.analysis_gallery.file_paths[index]
        self.current_image_index = index

        # Update current_image_info if we have a project
        if hasattr(self, 'current_project') and self.current_project:
            # Try to find the image info for this file path
            # Different project classes might have different ways to access image info
            try:
                # Method 1: Try to access images dictionary directly (GrainSightProject)
                if hasattr(self.current_project, 'images') and isinstance(self.current_project.images, dict):
                    for image_id, image_info in self.current_project.images.items():
                        project_image_path = self.current_project.get_image_path(image_id)
                        if project_image_path == self.image_full_path:
                            self.current_image_info = image_info
                            logger.info(f"Updated current_image_info to {image_info.filename} (from images dict)")
                            break
                # Method 2: Try to use get_all_image_infos method if available
                elif hasattr(self.current_project, 'get_all_image_infos'):
                    for image_info in self.current_project.get_all_image_infos():
                        project_image_path = self.current_project.get_image_path(image_info.id)
                        if project_image_path == self.image_full_path:
                            self.current_image_info = image_info
                            logger.info(f"Updated current_image_info to {image_info.filename} (from get_all_image_infos)")
                            break
                # Method 3: Try to iterate through image IDs and get info for each
                elif hasattr(self.current_project, 'get_image_ids') and hasattr(self.current_project, 'get_image_info'):
                    for image_id in self.current_project.get_image_ids():
                        image_info = self.current_project.get_image_info(image_id)
                        project_image_path = self.current_project.get_image_path(image_id)
                        if project_image_path == self.image_full_path:
                            self.current_image_info = image_info
                            logger.info(f"Updated current_image_info to {image_info.filename} (from get_image_ids)")
                            break
            except Exception as e:
                logger.error(f"Error finding image info for path {self.image_full_path}: {e}")

        # Update the image display using independent variables if available
        source_image = self.analysis_image if hasattr(self, 'analysis_image') and self.analysis_image is not None else self.image

        try:
            if hasattr(self, 'analysis_image_resized'):
                self.analysis_image_resized = resize_image(source_image, self.common_size)
                self.analysis_original_view.setPixmap(convert_cvimage_to_qpixmap(self.analysis_image_resized))
            else:
                # Fallback to shared variables
                self.image_resized = resize_image(source_image, self.common_size)
                self.analysis_original_view.setPixmap(convert_cvimage_to_qpixmap(self.image_resized))
        except Exception as e:
            logger.error(f"Error resizing or displaying image: {e}")

        # Reset the analysis image
        if hasattr(self, 'reset_analysis_image'):
            self.reset_analysis_image()

        # Update image info
        self.update_image_info(source_image)
