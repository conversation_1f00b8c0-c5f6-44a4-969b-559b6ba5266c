#!/usr/bin/env python3
"""
Test script to verify the auto-correction message for deleted grains.

This script simulates the scenario where:
1. A project has optimized grain storage enabled
2. Some grains have been deleted from the optimized storage
3. The DataFrame still contains the deleted grains
4. The auto-correction should detect this and show a message
"""

import sys
import os
import pandas as pd
from unittest.mock import Mock, MagicMock

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_auto_correction_logic():
    """Test the auto-correction logic without running the full GUI."""
    
    print("Testing auto-correction logic...")
    
    # Create mock objects
    mock_project = Mock()
    mock_project.use_optimized_grain_storage = True
    
    # Simulate a scenario where optimized storage has 3 active grains
    # but the loaded DataFrame has 5 grains (2 were deleted)
    active_grain_df = pd.DataFrame({
        'grain_id': ['grain_000000', 'grain_000001', 'grain_000002'],
        'area': [100.5, 200.3, 150.7],
        'perimeter': [50.2, 70.1, 60.8]
    })
    
    loaded_df = pd.DataFrame({
        'grain_id': ['grain_000000', 'grain_000001', 'grain_000002', 'grain_000003', 'grain_000004'],
        'area': [100.5, 200.3, 150.7, 180.2, 120.9],
        'perimeter': [50.2, 70.1, 60.8, 65.4, 55.3]
    })
    
    mock_project.get_active_grain_dataframe.return_value = active_grain_df
    
    # Simulate the auto-correction logic
    image_id = "test_image_001"
    
    print(f"Loaded DataFrame has {len(loaded_df)} grains")
    print(f"Active grains in optimized storage: {len(active_grain_df)} grains")
    
    # Check for deleted grains mismatch
    if len(active_grain_df) < len(loaded_df):
        deleted_count = len(loaded_df) - len(active_grain_df)
        print(f"✓ Detected {deleted_count} deleted grains in loaded dataframe")
        print(f"✓ Auto-correction message would be: 'Auto-corrected dataframe: removed {deleted_count} deleted grains from display.'")
        
        # Verify the corrected DataFrame would be used
        corrected_df = active_grain_df
        print(f"✓ Corrected DataFrame would have {len(corrected_df)} grains")
        
        return True
    else:
        print("✗ No deleted grains detected - this should not happen in our test case")
        return False

def test_no_auto_correction_needed():
    """Test the case where no auto-correction is needed."""
    
    print("\nTesting case where no auto-correction is needed...")
    
    # Create mock objects
    mock_project = Mock()
    mock_project.use_optimized_grain_storage = True
    
    # Simulate a scenario where both have the same number of grains
    active_grain_df = pd.DataFrame({
        'grain_id': ['grain_000000', 'grain_000001', 'grain_000002'],
        'area': [100.5, 200.3, 150.7],
        'perimeter': [50.2, 70.1, 60.8]
    })
    
    loaded_df = pd.DataFrame({
        'grain_id': ['grain_000000', 'grain_000001', 'grain_000002'],
        'area': [100.5, 200.3, 150.7],
        'perimeter': [50.2, 70.1, 60.8]
    })
    
    mock_project.get_active_grain_dataframe.return_value = active_grain_df
    
    print(f"Loaded DataFrame has {len(loaded_df)} grains")
    print(f"Active grains in optimized storage: {len(active_grain_df)} grains")
    
    # Check for deleted grains mismatch
    if len(active_grain_df) < len(loaded_df):
        print("✗ Unexpected: detected deleted grains when there shouldn't be any")
        return False
    else:
        print("✓ No deleted grains detected - using loaded dataframe as-is")
        return True

def test_legacy_storage():
    """Test the case where optimized storage is not enabled."""
    
    print("\nTesting legacy storage (no auto-correction)...")
    
    # Create mock objects
    mock_project = Mock()
    mock_project.use_optimized_grain_storage = False
    
    loaded_df = pd.DataFrame({
        'grain_id': ['grain_000000', 'grain_000001', 'grain_000002'],
        'area': [100.5, 200.3, 150.7],
        'perimeter': [50.2, 70.1, 60.8]
    })
    
    print(f"Loaded DataFrame has {len(loaded_df)} grains")
    print("✓ Using legacy storage - no auto-correction performed")
    
    return True

if __name__ == "__main__":
    print("=" * 60)
    print("Testing Auto-Correction Logic for Deleted Grains")
    print("=" * 60)
    
    test1_passed = test_auto_correction_logic()
    test2_passed = test_no_auto_correction_needed()
    test3_passed = test_legacy_storage()
    
    print("\n" + "=" * 60)
    print("Test Results:")
    print(f"Auto-correction detection: {'PASS' if test1_passed else 'FAIL'}")
    print(f"No correction needed: {'PASS' if test2_passed else 'FAIL'}")
    print(f"Legacy storage: {'PASS' if test3_passed else 'FAIL'}")
    
    if all([test1_passed, test2_passed, test3_passed]):
        print("\n✓ All tests passed! The auto-correction logic should work correctly.")
    else:
        print("\n✗ Some tests failed. Please check the implementation.")
    
    print("=" * 60)