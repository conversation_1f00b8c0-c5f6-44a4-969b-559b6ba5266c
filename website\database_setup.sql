-- VisionLab AI Website Database Setup
-- Run this script to create the necessary database and tables

-- Create database (uncomment if needed)
-- CREATE DATABASE visionlab_waitlist CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
-- USE visionlab_waitlist;

-- Waitlist table
CREATE TABLE IF NOT EXISTS waitlist (
    id INT AUTO_INCREMENT PRIMARY KEY,
    first_name VA<PERSON><PERSON><PERSON>(100) NOT NULL,
    last_name <PERSON><PERSON><PERSON><PERSON>(100) NOT NULL,
    email VARCHAR(255) NOT NULL UNIQUE,
    organization VARCHAR(255) DEFAULT NULL,
    use_case ENUM('geology', 'materials', 'medical', 'quality', 'academic', 'other') NOT NULL,
    token VARCHAR(64) NOT NULL UNIQUE,
    confirmed BOOLEAN DEFAULT FALSE,
    confirmed_at TIMESTAMP NULL DEFAULT NULL,
    download_token VARCHAR(64) DEFAULT NULL,
    download_sent BOOLEAN DEFAULT FALSE,
    download_sent_at TIMESTAMP NULL DEFAULT NULL,
    ip_address VARCHAR(45) DEFAULT NULL,
    user_agent TEXT DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_email (email),
    INDEX idx_token (token),
    INDEX idx_confirmed (confirmed),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Contact messages table
CREATE TABLE IF NOT EXISTS contact_messages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    subject VARCHAR(500) NOT NULL,
    message TEXT NOT NULL,
    status ENUM('new', 'in_progress', 'resolved', 'closed') DEFAULT 'new',
    priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
    assigned_to VARCHAR(255) DEFAULT NULL,
    ip_address VARCHAR(45) DEFAULT NULL,
    user_agent TEXT DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    resolved_at TIMESTAMP NULL DEFAULT NULL,
    
    INDEX idx_email (email),
    INDEX idx_status (status),
    INDEX idx_priority (priority),
    INDEX idx_created_at (created_at),
    INDEX idx_ip_address (ip_address)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Email logs table (optional - for tracking email delivery)
CREATE TABLE IF NOT EXISTS email_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    recipient_email VARCHAR(255) NOT NULL,
    email_type ENUM('waitlist_confirmation', 'download_notification', 'contact_confirmation', 'contact_admin') NOT NULL,
    subject VARCHAR(500) NOT NULL,
    status ENUM('sent', 'failed', 'bounced') DEFAULT 'sent',
    error_message TEXT DEFAULT NULL,
    related_id INT DEFAULT NULL, -- ID from waitlist or contact_messages table
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_recipient (recipient_email),
    INDEX idx_type (email_type),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Analytics table (optional - for tracking website metrics)
CREATE TABLE IF NOT EXISTS analytics (
    id INT AUTO_INCREMENT PRIMARY KEY,
    event_type ENUM('page_view', 'form_submission', 'email_open', 'link_click', 'download') NOT NULL,
    page_url VARCHAR(500) DEFAULT NULL,
    user_agent TEXT DEFAULT NULL,
    ip_address VARCHAR(45) DEFAULT NULL,
    referrer VARCHAR(500) DEFAULT NULL,
    session_id VARCHAR(100) DEFAULT NULL,
    user_id INT DEFAULT NULL, -- Can link to waitlist.id if needed
    metadata JSON DEFAULT NULL, -- Store additional event data
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_event_type (event_type),
    INDEX idx_created_at (created_at),
    INDEX idx_ip_address (ip_address),
    INDEX idx_session_id (session_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Admin users table (optional - for backend management)
CREATE TABLE IF NOT EXISTS admin_users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(100) NOT NULL UNIQUE,
    email VARCHAR(255) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    role ENUM('admin', 'moderator', 'viewer') DEFAULT 'viewer',
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP NULL DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_role (role)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert default admin user (change password immediately!)
INSERT INTO admin_users (username, email, password_hash, role) VALUES 
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin')
ON DUPLICATE KEY UPDATE username = username;
-- Default password is 'password' - CHANGE THIS IMMEDIATELY!

-- Create views for easier data analysis
CREATE OR REPLACE VIEW waitlist_stats AS
SELECT 
    COUNT(*) as total_signups,
    COUNT(CASE WHEN confirmed = TRUE THEN 1 END) as confirmed_signups,
    COUNT(CASE WHEN download_sent = TRUE THEN 1 END) as downloads_sent,
    use_case,
    DATE(created_at) as signup_date
FROM waitlist 
GROUP BY use_case, DATE(created_at)
ORDER BY signup_date DESC;

CREATE OR REPLACE VIEW contact_stats AS
SELECT 
    COUNT(*) as total_messages,
    status,
    priority,
    DATE(created_at) as message_date
FROM contact_messages 
GROUP BY status, priority, DATE(created_at)
ORDER BY message_date DESC;

-- Create stored procedures for common operations
DELIMITER //

-- Procedure to confirm waitlist signup
CREATE PROCEDURE ConfirmWaitlistSignup(
    IN p_token VARCHAR(64)
)
BEGIN
    DECLARE v_count INT DEFAULT 0;
    
    SELECT COUNT(*) INTO v_count 
    FROM waitlist 
    WHERE token = p_token AND confirmed = FALSE;
    
    IF v_count > 0 THEN
        UPDATE waitlist 
        SET confirmed = TRUE, confirmed_at = CURRENT_TIMESTAMP 
        WHERE token = p_token;
        
        SELECT 'success' as status, 'Email confirmed successfully!' as message;
    ELSE
        SELECT 'error' as status, 'Invalid or already confirmed token.' as message;
    END IF;
END //

-- Procedure to generate download tokens for confirmed users
CREATE PROCEDURE GenerateDownloadTokens()
BEGIN
    UPDATE waitlist 
    SET download_token = SHA2(CONCAT(email, UNIX_TIMESTAMP(), RAND()), 256)
    WHERE confirmed = TRUE AND download_token IS NULL;
    
    SELECT COUNT(*) as tokens_generated FROM waitlist WHERE download_token IS NOT NULL;
END //

-- Procedure to get waitlist statistics
CREATE PROCEDURE GetWaitlistStatistics()
BEGIN
    SELECT 
        COUNT(*) as total_signups,
        COUNT(CASE WHEN confirmed = TRUE THEN 1 END) as confirmed_signups,
        COUNT(CASE WHEN download_sent = TRUE THEN 1 END) as downloads_sent,
        COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) as signups_last_7_days,
        COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END) as signups_last_30_days
    FROM waitlist;
    
    SELECT 
        use_case,
        COUNT(*) as count,
        ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM waitlist), 2) as percentage
    FROM waitlist 
    GROUP BY use_case 
    ORDER BY count DESC;
END //

DELIMITER ;

-- Create triggers for logging
DELIMITER //

CREATE TRIGGER waitlist_after_insert
AFTER INSERT ON waitlist
FOR EACH ROW
BEGIN
    INSERT INTO analytics (event_type, user_id, metadata, created_at)
    VALUES ('form_submission', NEW.id, JSON_OBJECT('type', 'waitlist', 'use_case', NEW.use_case), NEW.created_at);
END //

CREATE TRIGGER contact_after_insert
AFTER INSERT ON contact_messages
FOR EACH ROW
BEGIN
    INSERT INTO analytics (event_type, metadata, created_at)
    VALUES ('form_submission', JSON_OBJECT('type', 'contact', 'subject', NEW.subject), NEW.created_at);
END //

DELIMITER ;

-- Sample data for testing (remove in production)
/*
INSERT INTO waitlist (first_name, last_name, email, organization, use_case, token, confirmed) VALUES
('John', 'Doe', '<EMAIL>', 'University Research Lab', 'academic', SHA2(CONCAT('<EMAIL>', UNIX_TIMESTAMP()), 256), TRUE),
('Jane', 'Smith', '<EMAIL>', 'Geological Survey Inc', 'geology', SHA2(CONCAT('<EMAIL>', UNIX_TIMESTAMP()), 256), FALSE),
('Bob', 'Johnson', '<EMAIL>', 'Materials Testing Corp', 'materials', SHA2(CONCAT('<EMAIL>', UNIX_TIMESTAMP()), 256), TRUE);

INSERT INTO contact_messages (name, email, subject, message, status) VALUES
('Alice Brown', '<EMAIL>', 'Question about pricing', 'I would like to know more about your pricing plans for academic institutions.', 'new'),
('Charlie Wilson', '<EMAIL>', 'Feature request', 'Would it be possible to add support for DICOM images?', 'in_progress');
*/

-- Grant permissions (adjust as needed)
-- GRANT SELECT, INSERT, UPDATE ON visionlab_waitlist.* TO 'website_user'@'localhost';
-- GRANT SELECT ON visionlab_waitlist.waitlist_stats TO 'analytics_user'@'localhost';
-- GRANT ALL PRIVILEGES ON visionlab_waitlist.* TO 'admin_user'@'localhost';

-- Show table information
SHOW TABLES;
SELECT 'Database setup completed successfully!' as status;