#!/usr/bin/env python3
"""
Comprehensive test suite for grain deletion performance optimizations.

This test validates:
1. Data consistency after deletions
2. Performance improvements with large datasets
3. UI responsiveness
4. State persistence and project saving
5. Batch processing capabilities
"""

import sys
import os
import time
import unittest
import tempfile
import shutil
from unittest.mock import Mock, patch, MagicMock
import numpy as np
import pandas as pd
import torch

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', 'src'))

try:
    from PySide6.QtWidgets import QApplication, QWidget
    from PySide6.QtCore import QTimer
    from PySide6.QtTest import QTest
except ImportError:
    print("PySide6 not available, skipping GUI tests")
    sys.exit(0)

# Import the grain analysis widget
try:
    from gui.grain_analysis_widget import GrainAnalysisWidget
except ImportError as e:
    print(f"Could not import GrainAnalysisWidget: {e}")
    sys.exit(0)


class TestGrainDeletionOptimization(unittest.TestCase):
    """Test suite for grain deletion performance optimizations."""
    
    @classmethod
    def setUpClass(cls):
        """Set up test environment."""
        cls.app = QApplication.instance()
        if cls.app is None:
            cls.app = QApplication([])
    
    def setUp(self):
        """Set up each test."""
        self.widget = GrainAnalysisWidget()
        self.temp_dir = tempfile.mkdtemp()
        
        # Create mock data for testing
        self.create_mock_grain_data()
    
    def tearDown(self):
        """Clean up after each test."""
        if hasattr(self, 'widget'):
            self.widget.deleteLater()
        if hasattr(self, 'temp_dir'):
            shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def create_mock_grain_data(self, num_grains=500):
        """Create mock grain data for testing."""
        # Create mock DataFrame
        data = {
            'Area (µm²)': np.random.uniform(10, 1000, num_grains),
            'Perimeter (µm)': np.random.uniform(20, 200, num_grains),
            'Equivalent_Diameter': np.random.uniform(5, 50, num_grains),
            'Solidity': np.random.uniform(0.7, 1.0, num_grains),
            'Convexity': np.random.uniform(0.8, 1.0, num_grains),
            'Compactness': np.random.uniform(0.5, 1.0, num_grains),
        }
        self.mock_df = pd.DataFrame(data)
        
        # Create mock annotations (PyTorch tensors)
        self.mock_annotations = []
        for i in range(num_grains):
            # Create random binary mask
            mask = torch.randint(0, 2, (100, 100), dtype=torch.uint8)
            self.mock_annotations.append(mask)
        
        # Create mock uploaded image
        self.mock_image = Mock()
        self.mock_image.width = 1000
        self.mock_image.height = 1000
        
        # Set up widget with mock data
        self.widget.grain_df = self.mock_df.copy()
        self.widget.grain_annotations = self.mock_annotations.copy()
        self.widget.grain_uploaded_image = self.mock_image
        self.widget.grain_current_scale_factor = 1.0
        
        # Mock UI components
        self.widget.results_widget = Mock()
        self.widget.results_widget.selected_df_indices = set()
        self.widget.scene = Mock()
        self.widget.grain_pixmap_item = Mock()
        self.widget.grain_pixmap_item.pixmap.return_value.width.return_value = 1000
        self.widget.grain_pixmap_item.pixmap.return_value.height.return_value = 1000
        
        # Mock methods that require UI
        self.widget.update_status = Mock()
        self.widget.mark_state_as_modified = Mock()
        self.widget.save_grain_analysis_state = Mock()
        self.widget.update_action_states = Mock()
        self.widget.clear_scene_graphics_items = Mock()
        self.widget.display_image_on_scene = Mock()
        self.widget._show_error_message = Mock()
    
    def test_data_consistency_after_deletion(self):
        """Test that data remains consistent after grain deletion."""
        print("Testing data consistency after deletion...")
        
        # Select some grains to delete
        indices_to_delete = {0, 1, 2, 10, 20}
        original_count = len(self.widget.grain_df)
        
        # Perform deletion
        success = self.widget._delete_grains_optimized(indices_to_delete)
        
        # Verify success
        self.assertTrue(success, "Optimized deletion should succeed")
        
        # Check data consistency
        remaining_count = len(self.widget.grain_df)
        expected_count = original_count - len(indices_to_delete)
        
        self.assertEqual(remaining_count, expected_count, 
                        f"Expected {expected_count} grains, got {remaining_count}")
        
        # Check that DataFrame and annotations have same length
        df_length = len(self.widget.grain_df)
        annotations_length = len(self.widget.grain_annotations)
        
        self.assertEqual(df_length, annotations_length,
                        f"DataFrame length ({df_length}) should match annotations length ({annotations_length})")
        
        # Verify deleted indices are not in remaining data
        remaining_indices = set(self.widget.grain_df.index)
        self.assertTrue(indices_to_delete.isdisjoint(remaining_indices),
                       "Deleted indices should not be in remaining data")
        
        print(f"✓ Data consistency verified: {remaining_count}/{original_count} grains remaining")
    
    def test_performance_with_large_dataset(self):
        """Test performance improvements with large datasets."""
        print("Testing performance with large dataset...")
        
        # Create large dataset
        self.create_mock_grain_data(num_grains=1000)
        
        # Test small batch deletion (should use optimized method)
        small_batch = set(range(10))
        start_time = time.time()
        success = self.widget._delete_grains_optimized(small_batch)
        small_batch_time = time.time() - start_time
        
        self.assertTrue(success, "Small batch deletion should succeed")
        print(f"✓ Small batch (10 grains): {small_batch_time:.3f}s")
        
        # Reset data for large batch test
        self.create_mock_grain_data(num_grains=1000)
        
        # Test large batch deletion (should use batch-optimized method)
        large_batch = set(range(200))
        start_time = time.time()
        success = self.widget._delete_grains_batch_optimized(large_batch)
        large_batch_time = time.time() - start_time
        
        self.assertTrue(success, "Large batch deletion should succeed")
        print(f"✓ Large batch (200 grains): {large_batch_time:.3f}s")
        
        # Performance should be reasonable (less than 5 seconds for 200 grains)
        self.assertLess(large_batch_time, 5.0, 
                       f"Large batch deletion took too long: {large_batch_time:.3f}s")
    
    def test_batch_processing_optimization(self):
        """Test batch processing capabilities."""
        print("Testing batch processing optimization...")
        
        # Create dataset
        self.create_mock_grain_data(num_grains=300)
        
        # Test different batch sizes
        batch_sizes = [5, 50, 150]
        
        for batch_size in batch_sizes:
            with self.subTest(batch_size=batch_size):
                # Reset data
                self.create_mock_grain_data(num_grains=300)
                
                # Select batch to delete
                indices_to_delete = set(range(batch_size))
                
                # Time the deletion
                start_time = time.time()
                
                if batch_size > 100:
                    success = self.widget._delete_grains_batch_optimized(indices_to_delete)
                else:
                    success = self.widget._delete_grains_optimized(indices_to_delete)
                
                elapsed_time = time.time() - start_time
                
                self.assertTrue(success, f"Batch deletion of {batch_size} grains should succeed")
                
                # Verify correct number of grains remain
                expected_remaining = 300 - batch_size
                actual_remaining = len(self.widget.grain_df)
                
                self.assertEqual(actual_remaining, expected_remaining,
                               f"Expected {expected_remaining} grains, got {actual_remaining}")
                
                print(f"✓ Batch size {batch_size}: {elapsed_time:.3f}s, {actual_remaining} grains remaining")
    
    def test_performance_monitoring(self):
        """Test performance monitoring functionality."""
        print("Testing performance monitoring...")
        
        # Ensure performance history is initialized
        if not hasattr(self.widget, '_performance_history'):
            self.widget._performance_history = []
        
        # Test performance logging
        self.widget._log_performance_metrics("test_operation", 1.5, 100, "optimized")
        
        # Check that metrics were logged
        self.assertTrue(hasattr(self.widget, '_performance_history'))
        self.assertGreater(len(self.widget._performance_history), 0)
        
        # Test performance summary
        summary = self.widget.get_performance_summary()
        
        self.assertIsInstance(summary, dict)
        if "error" not in summary and "message" not in summary:
            self.assertIn("recent_operations", summary)
            self.assertIn("total_grains_processed", summary)
            self.assertIn("average_rate", summary)
        
        print("✓ Performance monitoring functionality verified")
    
    def test_memory_efficiency(self):
        """Test memory efficiency of deletion operations."""
        print("Testing memory efficiency...")
        
        # Create large dataset
        self.create_mock_grain_data(num_grains=500)
        
        # Monitor memory usage (simplified)
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Perform multiple deletions
        for i in range(5):
            # Delete 50 grains each iteration
            indices_to_delete = set(range(i * 50, (i + 1) * 50))
            if indices_to_delete.intersection(set(self.widget.grain_df.index)):
                success = self.widget._delete_grains_optimized(indices_to_delete)
                self.assertTrue(success, f"Deletion iteration {i} should succeed")
        
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory
        
        print(f"✓ Memory usage: {initial_memory:.1f}MB → {final_memory:.1f}MB (Δ{memory_increase:.1f}MB)")
        
        # Memory increase should be reasonable (less than 100MB for this test)
        self.assertLess(memory_increase, 100, 
                       f"Memory increase too large: {memory_increase:.1f}MB")


def run_performance_tests():
    """Run the performance test suite."""
    print("=" * 60)
    print("GRAIN DELETION OPTIMIZATION TEST SUITE")
    print("=" * 60)
    
    # Create test suite
    suite = unittest.TestLoader().loadTestsFromTestCase(TestGrainDeletionOptimization)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    
    if result.failures:
        print("\nFAILURES:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")
    
    if result.errors:
        print("\nERRORS:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")
    
    success = len(result.failures) == 0 and len(result.errors) == 0
    print(f"\nOverall result: {'PASS' if success else 'FAIL'}")
    
    return success


if __name__ == "__main__":
    success = run_performance_tests()
    sys.exit(0 if success else 1)
