# src/gui/handlers/project_hub_handler.py
import os
import logging
import cv2
import numpy as np
from typing import List, Optional, Union, Dict
from PySide6.QtWidgets import QFileDialog, QMessageBox, QListWidgetItem, QWidget, QMenu, QApplication, QDialog
from PySide6.QtCore import Qt, Slot, QPoint
from src.core.project_data import Project, ImageInfo
from src.core.grainsight_project import VisionLabProject
from src.utils.image_utils import resize_image, convert_cvimage_to_qpixmap
from src.utils.settings_manager import settings_manager
from src.gui.image_optimization_dialog import ImageOptimizationDialog
from src.widgets.recent_projects_menu import RecentProjectsMenu

logger = logging.getLogger(__name__)

class ProjectHubHandler:
    """Handles logic for the Project Hub page."""

    def __init__(self):
        # Needs to be initialized properly in the main app's __init__
        self.current_project: Optional[Union[Project, VisionLabProject]] = None
        self.last_project_dir: str = os.path.expanduser("~")
        self.is_vlp_project: bool = False  # Flag to track if current project is .vlp format

    def _setup_project_hub_connections(self):
        """Connect signals specific to the Project Hub. Call this in main app's init."""
        print("DEBUG: Setting up Project Hub connections...")

        # Add new button for creating VLP project
        if hasattr(self, 'new_vlp_project_button') and self.new_vlp_project_button:
            print("DEBUG: Connecting new_vlp_project_button")
            self.new_vlp_project_button.clicked.connect(self.create_new_vlp_project)
        else:
            print("DEBUG: new_vlp_project_button not found!")

        # Add button for opening recent projects
        if hasattr(self, 'open_recent_button') and self.open_recent_button:
            print("DEBUG: Connecting open_recent_button")
            self.setup_recent_projects_menu()
        else:
            print("DEBUG: open_recent_button not found!")

        # Add button for opening VLP project
        if hasattr(self, 'open_vlp_project_button') and self.open_vlp_project_button:
            print("DEBUG: Connecting open_vlp_project_button")
            self.open_vlp_project_button.clicked.connect(self.open_vlp_project)
        else:
            print("DEBUG: open_vlp_project_button not found!")

        if hasattr(self, 'import_images_button') and self.import_images_button:
            print("DEBUG: Connecting import_images_button")
            self.import_images_button.clicked.connect(self.import_images_to_project)
        else:
            print("DEBUG: import_images_button not found!")

        if hasattr(self, 'project_gallery_list'):
            self.project_gallery_list.itemSelectionChanged.connect(self.on_gallery_selection_changed)
            self.project_gallery_list.itemDoubleClicked.connect(self.on_gallery_item_double_clicked)

            # Set up context menu for gallery list
            self.project_gallery_list.setContextMenuPolicy(Qt.CustomContextMenu)
            self.project_gallery_list.customContextMenuRequested.connect(self.show_gallery_context_menu)
        if hasattr(self, 'save_metadata_button'):
            self.save_metadata_button.clicked.connect(self.save_current_metadata)

        # Connect analysis buttons
        if hasattr(self, 'analyze_unsupervised_button'):
             self.analyze_unsupervised_button.clicked.connect(
                  lambda: self.trigger_analysis('unsupervised_segmentation')
             )
        if hasattr(self, 'analyze_trainable_button'):
             self.analyze_trainable_button.clicked.connect(
                  lambda: self.trigger_analysis('trainable_segmentation')
             )
        if hasattr(self, 'analyze_grain_size_button'):
             self.analyze_grain_size_button.clicked.connect(
                  lambda: self.trigger_analysis('grain_size_analysis')
             )
        if hasattr(self, 'analyze_porosity_button'):
             self.analyze_porosity_button.clicked.connect(
                  lambda: self.trigger_analysis('image_lab')
             )
        if hasattr(self, 'analyze_ai_assistant_button'):
             self.analyze_ai_assistant_button.clicked.connect(
                  lambda: self.trigger_analysis('ai_assistant')
             )
        if hasattr(self, 'analyze_point_counting_button'):
             self.analyze_point_counting_button.clicked.connect(
                  lambda: self.trigger_analysis('point_counting')
             )
        if hasattr(self, 'analyze_advanced_segmentation_button'):
             self.analyze_advanced_segmentation_button.clicked.connect(
                  lambda: self.trigger_analysis('advanced_segmentation')
             )
        if hasattr(self, 'batch_process_button'):
             self.batch_process_button.clicked.connect(self.start_batch_processing)

    def _update_hub_ui_state(self):
        """Updates enabled/disabled state of Hub UI elements."""
        project_loaded = self.current_project is not None
        items_selected = bool(self.project_gallery_list.selectedItems()) if hasattr(self, 'project_gallery_list') else False

        if hasattr(self, 'import_images_button'): self.import_images_button.setEnabled(project_loaded)
        if hasattr(self, 'metadata_groupbox'): self.metadata_groupbox.setEnabled(project_loaded and items_selected)
        if hasattr(self, 'actions_groupbox'): self.actions_groupbox.setEnabled(project_loaded and items_selected)
        if hasattr(self, 'project_name_label'):
             project_type = "VLP Project" if self.is_vlp_project else "Project"
             self.project_name_label.setText(f"{project_type}: {self.current_project.name}" if project_loaded else "No Project Loaded")


    @Slot()
    def create_new_project(self):
        """Prompts user for a new project directory and creates it."""
        print("DEBUG: create_new_project method called")
        # Add confirmation if a project is already open
        if self.current_project:
            # Use the required confirmation message
            reply = QMessageBox.question(
                self,
                "New Project",
                "Warning: Opening a new project will clear all current project data and results. This action cannot be undone. Continue?",
                QMessageBox.Yes | QMessageBox.No
            )
            if reply == QMessageBox.No:
                return

            # Reset application state before creating new project
            app = self._find_main_app()
            if app and hasattr(app, 'reset_application_state'):
                logger.info("Resetting application state before creating new project")
                app.reset_application_state(show_loading_indicator=True)

        dir_path = QFileDialog.getExistingDirectory(self, "Select New Project Directory", self.last_project_dir)
        if dir_path:
            # Check if directory is empty or contains existing project
            if os.path.exists(os.path.join(dir_path, Project.PROJECT_FILE_NAME)):
                 QMessageBox.warning(self, "Project Exists", "Selected directory already contains a project. Please choose 'Open Project' or select a different directory.")
                 return
            if os.listdir(dir_path):
                 reply = QMessageBox.question(self, "Non-Empty Directory", "Selected directory is not empty. Create project here anyway?", QMessageBox.Yes | QMessageBox.Cancel)
                 if reply == QMessageBox.Cancel: return

            try:
                self.current_project = Project(dir_path) # Creates dirs
                self.current_project.save() # Save the initial empty project file
                self.is_vlp_project = False
                self.last_project_dir = os.path.dirname(dir_path) # Go up one level for next dialog
                logger.info(f"Created new project at: {dir_path}")
                
                # Add to recent projects
                settings_manager.add_recent_project(dir_path, self.current_project.name, "standard")
                
                self._update_project_display()
                self._show_message("Project Created", f"New project '{self.current_project.name}' created.")
                
                # Refresh recent projects menu if it exists
                if hasattr(self, 'recent_projects_menu'):
                    self.recent_projects_menu.refresh()
                    
            except Exception as e:
                logger.exception("Failed to create new project.")
                self._show_error_message("Error", f"Failed to create project: {e}")
                self.current_project = None
            self._update_hub_ui_state()


    @Slot()
    def open_project(self):
        """Prompts user to select a project directory and loads it."""
        print("DEBUG: open_project method called")
        # Add confirmation if a project is already open
        if self.current_project:
            # Use the required confirmation message
            reply = QMessageBox.question(
                self,
                "Open Project",
                "Warning: Opening a new project will clear all current project data and results. This action cannot be undone. Continue?",
                QMessageBox.Yes | QMessageBox.No
            )
            if reply == QMessageBox.No:
                return

            # Reset application state before loading new project
            # Find the main application instance
            app = self._find_main_app()
            if app and hasattr(app, 'reset_application_state'):
                logger.info("Resetting application state before opening new project")
                app.reset_application_state(show_loading_indicator=True)

        dir_path = QFileDialog.getExistingDirectory(self, "Select Project Directory", self.last_project_dir)
        if dir_path:
             # Basic check if it looks like a project directory
             if not os.path.exists(os.path.join(dir_path, Project.PROJECT_FILE_NAME)):
                  reply = QMessageBox.question(self, "Open Project", f"Directory doesn't contain '{Project.PROJECT_FILE_NAME}'. Attempt to treat as project directory anyway?", QMessageBox.Yes | QMessageBox.Cancel)
                  if reply == QMessageBox.Cancel: return

             try:
                  self.current_project = Project.load(dir_path)
                  self.is_vlp_project = False
                  self.last_project_dir = os.path.dirname(dir_path)
                  logger.info(f"Opened project: {dir_path}")
                  
                  # Add to recent projects
                  settings_manager.add_recent_project(dir_path, self.current_project.name, "standard")
                  
                  self._update_project_display()
                  self._show_message("Project Opened", f"Project '{self.current_project.name}' opened.")
                  
                  # Refresh recent projects menu if it exists
                  if hasattr(self, 'recent_projects_menu'):
                      self.recent_projects_menu.refresh()
                      
             except Exception as e:
                  logger.exception("Failed to load project.")
                  self._show_error_message("Error", f"Failed to load project: {e}")
                  self.current_project = None
             self._update_hub_ui_state()


    @Slot()
    def save_project(self):
        """Saves the current project."""
        print("DEBUG: save_project method called")
        if not self.current_project:
            self._show_error_message("Error", "No project is open.")
            return
        try:
            self.current_project.save()
            self._show_message("Project Saved", f"Project '{self.current_project.name}' saved.")
        except Exception as e:
            logger.exception("Failed to save project.")
            self._show_error_message("Error", f"Failed to save project: {e}")

    @Slot()
    def create_new_vlp_project(self):
        """Creates a new project in .vlp format."""
        print("DEBUG: create_new_vlp_project method called")
        # Add confirmation if a project is already open
        if self.current_project:
            # Use the required confirmation message
            reply = QMessageBox.question(
                self,
                "New VLP Project",
                "Warning: Opening a new project will clear all current project data and results. This action cannot be undone. Continue?",
                QMessageBox.Yes | QMessageBox.No
            )
            if reply == QMessageBox.No:
                return

            # Reset application state before creating new project
            app = self._find_main_app()
            if app and hasattr(app, 'reset_application_state'):
                logger.info("Resetting application state before creating new VLP project")
                app.reset_application_state(show_loading_indicator=True)

        file_path, _ = QFileDialog.getSaveFileName(
            self, "Create New VLP Project",
            os.path.join(self.last_project_dir, "new_project.vlp"),
            "Vision Lab Project (*.vlp)"
        )

        if file_path:
            # Ensure the file has .vlp extension
            if not file_path.lower().endswith('.vlp'):
                file_path += '.vlp'

            try:
                # Create a new VisionLabProject
                self.current_project = VisionLabProject()
                self.current_project.name = os.path.basename(file_path).replace('.vlp', '')

                # Save the project
                if self.current_project.save(file_path):
                    self.is_vlp_project = True
                    self.last_project_dir = os.path.dirname(file_path)
                    logger.info(f"Created new VLP project at: {file_path}")
                    
                    # Add to recent projects
                    settings_manager.add_recent_project(file_path, self.current_project.name, "vlp")
                    
                    self._update_project_display()
                    self._show_message("Project Created", f"New VLP project '{self.current_project.name}' created.")
                    
                    # Refresh recent projects menu if it exists
                    if hasattr(self, 'recent_projects_menu'):
                        self.recent_projects_menu.refresh()
                else:
                    raise Exception("Failed to save project file")
            except Exception as e:
                logger.exception("Failed to create new VLP project.")
                self._show_error_message("Error", f"Failed to create VLP project: {e}")
                self.is_vlp_project = False
            self._update_hub_ui_state()

    @Slot()
    def open_vlp_project(self):
        """Opens an existing .vlp project file."""
        print("DEBUG: open_vlp_project method called")
        # Add confirmation if a project is already open
        if self.current_project:
            # Use the required confirmation message
            reply = QMessageBox.question(
                self,
                "Open VLP Project",
                "Warning: Opening a new project will clear all current project data and results. This action cannot be undone. Continue?",
                QMessageBox.Yes | QMessageBox.No
            )
            if reply == QMessageBox.No:
                return

            # Reset application state before loading new project
            # Find the main application instance
            app = self._find_main_app()
            if app and hasattr(app, 'reset_application_state'):
                logger.info("Resetting application state before opening new VLP project")
                app.reset_application_state(show_loading_indicator=True)

        file_path, _ = QFileDialog.getOpenFileName(
            self, "Open VLP Project",
            self.last_project_dir,
            "Vision Lab Project (*.vlp)"
        )

        if file_path:
            try:
                # Load the VisionLabProject
                self.current_project = VisionLabProject(file_path)
                if not self.current_project.images:
                    raise Exception("Project file is empty or invalid")

                self.is_vlp_project = True
                self.last_project_dir = os.path.dirname(file_path)
                logger.info(f"Opened VLP project: {file_path}")
                
                # Add to recent projects
                settings_manager.add_recent_project(file_path, self.current_project.name, "vlp")
                
                self._update_project_display()
                self._show_message("Project Opened", f"VLP project '{self.current_project.name}' opened.")
                
                # Refresh recent projects menu if it exists
                if hasattr(self, 'recent_projects_menu'):
                    self.recent_projects_menu.refresh()
                    
            except Exception as e:
                logger.exception("Failed to load VLP project.")
                self._show_error_message("Error", f"Failed to load VLP project: {e}")
                self.is_vlp_project = False
                self.current_project = None
            self._update_hub_ui_state()


    @Slot()
    def import_images_to_project(self):
        """Imports images into the current project with optimization for large images."""
        print("DEBUG: import_images_to_project method called")
        if not self.current_project:
            self._show_error_message("Error", "No project is open.")
            return

        file_paths, _ = QFileDialog.getOpenFileNames(
            self, "Import Images", self.last_project_dir,
            "Image Files (*.png *.jpg *.jpeg *.tif *.tiff *.bmp);;All Files (*)"
        )

        if file_paths:
            # Check if any images need optimization
            optimization_dialog = ImageOptimizationDialog(file_paths, self)
            optimized_images = {}
            optimization_info = {}

            # If the dialog is accepted, use optimized images
            if optimization_dialog.exec():
                optimized_images = optimization_dialog.get_optimized_images()
                optimization_info = optimization_dialog.get_optimization_info()
                logger.info(f"Optimized images, created {len(optimized_images)} output images/patches")

            imported_count = 0
            patch_count = 0

            # Create temporary directory for saving optimized images
            temp_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..\\..\\temp")
            os.makedirs(temp_dir, exist_ok=True)

            # Process each original image path
            for path in file_paths:
                # Check if this image has patches
                if path in optimization_info and optimization_info[path].get("has_patches", False):
                    # This image was split into patches
                    patch_keys = [k for k in optimized_images.keys() if k.startswith(f"{path}#patch")]

                    # Process each patch
                    for patch_key in patch_keys:
                        img = optimized_images[patch_key]
                        info = optimization_info[patch_key]

                        # Convert RGB to BGR for OpenCV saving
                        img_bgr = cv2.cvtColor(img, cv2.COLOR_RGB2BGR)

                        # Determine file extension
                        if "format" in info:
                            ext = f".{info['format']}"
                        else:
                            _, ext = os.path.splitext(path)

                        # Create patch filename
                        patch_index = info.get("patch_index", 0)
                        base_name = os.path.basename(path)
                        name_without_ext, _ = os.path.splitext(base_name)
                        patch_filename = f"{name_without_ext}_patch{patch_index}{ext}"

                        # Save patch to temporary file
                        temp_path = os.path.join(temp_dir, patch_filename)
                        cv2.imwrite(temp_path, img_bgr)
                        logger.info(f"Saved patch to {temp_path}")

                        # Add patch to project
                        project_info = self.current_project.add_image(temp_path)
                        if project_info:
                            # Store patch info in metadata
                            metadata = project_info.metadata or {}
                            metadata["optimization"] = info
                            metadata["is_patch"] = True
                            metadata["original_image"] = path
                            metadata["patch_index"] = patch_index

                            # Add patch position if available
                            if "patch_position" in info:
                                metadata["patch_position"] = info["patch_position"]

                            self.current_project.set_metadata(project_info.id, metadata)
                            patch_count += 1

                        # Clean up temporary file
                        try:
                            os.remove(temp_path)
                        except Exception as e:
                            logger.warning(f"Failed to remove temporary file {temp_path}: {e}")

                    # Also add the original image for reference
                    info = self.current_project.add_image(path)
                    if info:
                        # Store info that this image has patches
                        metadata = info.metadata or {}
                        metadata["has_patches"] = True
                        metadata["patch_count"] = len(patch_keys)
                        metadata["optimization"] = optimization_info[path]
                        self.current_project.set_metadata(info.id, metadata)
                        imported_count += 1

                # Check if this image was optimized (but not patched)
                elif path in optimized_images:
                    # Save optimized image to a temporary file
                    img = optimized_images[path]
                    info = optimization_info[path]

                    # Convert RGB to BGR for OpenCV saving
                    img_bgr = cv2.cvtColor(img, cv2.COLOR_RGB2BGR)

                    # Determine file extension based on optimization method
                    if info.get("format_conversion", False) or "new_format" in info:
                        ext = f".{info.get('new_format', 'jpg')}"
                    else:
                        # Keep original extension for resize
                        _, ext = os.path.splitext(path)

                    # Create temporary file path
                    base_name = os.path.basename(path)
                    name_without_ext, _ = os.path.splitext(base_name)
                    temp_path = os.path.join(temp_dir, f"optimized_{name_without_ext}{ext}")

                    # Save optimized image
                    cv2.imwrite(temp_path, img_bgr)
                    logger.info(f"Saved optimized image to {temp_path}")

                    # Add optimized image to project
                    project_info = self.current_project.add_image(temp_path)
                    if project_info:
                        # Store optimization info in image metadata
                        metadata = project_info.metadata or {}
                        metadata["optimization"] = info
                        self.current_project.set_metadata(project_info.id, metadata)
                        imported_count += 1

                    # Clean up temporary file
                    try:
                        os.remove(temp_path)
                    except Exception as e:
                        logger.warning(f"Failed to remove temporary file {temp_path}: {e}")
                else:
                    # Add original image without optimization
                    info = self.current_project.add_image(path)
                    if info:
                        imported_count += 1

            if imported_count > 0 or patch_count > 0:
                self._update_gallery_display()
                self.current_project.save() # Save project after adding images

            # Show completion message with patch count if applicable
            if patch_count > 0:
                self._show_message("Import Complete",
                                 f"Imported {imported_count} / {len(file_paths)} original images and created {patch_count} patches.")
            else:
                self._show_message("Import Complete",
                                 f"Imported {imported_count} / {len(file_paths)} images.")

            if imported_count > 0 or patch_count > 0:
                 self.last_project_dir = os.path.dirname(file_paths[0]) # Update last dir

    def _convert_legacy_to_vlp(self):
        """Converts the current legacy project to VLP format."""
        if not self.current_project or not hasattr(self.current_project, 'is_legacy') or not self.current_project.is_legacy:
            self._show_error_message("Error", "No legacy project is open or current project is already VLP format.")
            return

        try:
            # Get save file path from user
            file_path, _ = QFileDialog.getSaveFileName(
                self, "Save As VLP Project",
                "",
                "Vision Lab Project (*.vlp);;All Files (*)"
            )

            if file_path:
                # Ensure the file has the .vlp extension
                if not file_path.lower().endswith('.vlp'):
                    file_path += '.vlp'

                # Convert the legacy project to the new format
                success = self.current_project.convert_to_new_format(file_path)

                if success and os.path.exists(file_path):
                    # Reset the application state before loading the new project
                    try:
                        logger.info("Resetting application state before loading converted VLP project")
                        self._reset_application_state()
                        
                        # Load the newly converted project
                        self._load_project(file_path)
                        logger.info(f"Converted project to VLP format: {file_path}")
                        
                        # Show success message
                        self._show_message("Project Converted", f"Project converted to VLP format and saved as '{file_path}'.")
                    except Exception as e:
                        logger.error(f"Error after project conversion: {e}")
                        raise
        except Exception as e:
            logger.exception("Failed to convert project to VLP format.")
            self._show_error_message("Conversion Error", f"Failed to convert project: {str(e)}")


    def _update_project_display(self):
        """Updates the UI elements related to the current project."""
        self._update_gallery_display()
        self.on_gallery_selection_changed() # Clear/update metadata panel
        self._update_hub_ui_state()

    def _update_gallery_display(self):
        """Populates the image gallery list."""
        if not hasattr(self, 'project_gallery_list'): return
        self.project_gallery_list.clear()
        if self.current_project:
            # Sort images by filename or ID? Let's use filename for now.
            sorted_image_ids = sorted(self.current_project.images.keys(),
                                      key=lambda img_id: self.current_project.images[img_id].filename)
            for image_id in sorted_image_ids:
                info = self.current_project.images[image_id]
                item = QListWidgetItem(f"{info.filename} (ID: {info.id})")
                item.setData(Qt.UserRole, image_id) # Store image ID in the item
                self.project_gallery_list.addItem(item)

    def on_gallery_selection_changed(self):
        """Updates the metadata panel and image preview when gallery selection changes."""
        if not hasattr(self, 'project_gallery_list'): return
        selected_items = self.project_gallery_list.selectedItems()

        # Clear image preview by default
        if hasattr(self, 'image_preview_view'):
            self.image_preview_view.clear()
            self.image_info_label.setText("No image selected")
            self.image_preview_groupbox.setEnabled(False)

        if len(selected_items) == 1 and self.current_project:
            item = selected_items[0]
            image_id = item.data(Qt.UserRole)
            info = self.current_project.get_image_info(image_id)
            if info:
                # Enable UI elements
                self.metadata_groupbox.setEnabled(True)
                self.actions_groupbox.setEnabled(True)
                if hasattr(self, 'image_preview_groupbox'):
                    self.image_preview_groupbox.setEnabled(True)

                # Update metadata fields
                self.metadata_filename_label.setText(info.filename)
                self.metadata_sampleid_edit.setText(info.metadata.get("sample_id", ""))
                self.metadata_magnification_edit.setText(info.metadata.get("magnification", ""))
                self.metadata_notes_edit.setPlainText(info.metadata.get("notes", ""))

                # Load and display the image preview
                if hasattr(self, 'image_preview_view'):
                    image_path = self.current_project.get_image_path(image_id)
                    if image_path and os.path.exists(image_path):
                        try:
                            # Load the image using OpenCV
                            image = cv2.imread(image_path)
                            if image is not None:
                                # Convert BGR to RGB for display
                                image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

                                # Get image dimensions
                                height, width, _ = image.shape

                                # Resize for preview (maintain aspect ratio)
                                preview_height = 250  # Match the minimum height we set for the preview
                                preview_width = int(width * (preview_height / height))
                                preview_size = (preview_width, preview_height)
                                image_resized = resize_image(image, preview_size)

                                # Convert to QPixmap and display
                                pixmap = convert_cvimage_to_qpixmap(image_resized, already_rgb=True)
                                self.image_preview_view.setPixmap(pixmap)

                                # Update image info label
                                self.image_info_label.setText(f"{info.filename} ({width}x{height})")
                            else:
                                self.image_info_label.setText(f"Error: Could not load image {info.filename}")
                        except Exception as e:
                            logger.exception(f"Error loading image preview: {e}")
                            self.image_info_label.setText(f"Error: {str(e)}")
                    else:
                        self.image_info_label.setText(f"Error: Image file not found")
            else:
                 self._clear_metadata_fields()
                 self.metadata_groupbox.setEnabled(False)
                 self.actions_groupbox.setEnabled(False)
        else:
            # Multiple or no selection
            self._clear_metadata_fields()
            self.metadata_groupbox.setEnabled(False)
            # Enable actions only if > 0 items selected
            self.actions_groupbox.setEnabled(bool(selected_items))
            if len(selected_items) > 1:
                 self.metadata_filename_label.setText(f"{len(selected_items)} images selected")

    def _clear_metadata_fields(self):
        """Clears the metadata editor fields and image preview."""
        if hasattr(self, 'metadata_filename_label'): self.metadata_filename_label.setText("-")
        if hasattr(self, 'metadata_sampleid_edit'): self.metadata_sampleid_edit.clear()
        if hasattr(self, 'metadata_magnification_edit'): self.metadata_magnification_edit.clear()
        if hasattr(self, 'metadata_notes_edit'): self.metadata_notes_edit.clear()

        # Clear image preview
        if hasattr(self, 'image_preview_view'):
            self.image_preview_view.clear()
            self.image_info_label.setText("No image selected")
            self.image_preview_groupbox.setEnabled(False)


    def save_current_metadata(self):
        """Saves the metadata entered in the UI for the selected image."""
        if not hasattr(self, 'project_gallery_list'): return
        selected_items = self.project_gallery_list.selectedItems()
        if len(selected_items) != 1 or not self.current_project:
            self._show_error_message("Metadata Error", "Select exactly one image to save metadata.")
            return

        item = selected_items[0]
        image_id = item.data(Qt.UserRole)

        metadata = {
            "sample_id": self.metadata_sampleid_edit.text(),
            "magnification": self.metadata_magnification_edit.text(),
            "notes": self.metadata_notes_edit.toPlainText()
        }

        self.current_project.set_metadata(image_id, metadata)
        self.current_project.save() # Save project file after metadata change
        self._show_message("Metadata Saved", "Metadata updated successfully.")


    def get_selected_image_infos(self) -> List[ImageInfo]:
         """Gets the ImageInfo objects for the currently selected gallery items."""
         if not hasattr(self, 'project_gallery_list') or not self.current_project:
              return []

         selected_infos = []
         for item in self.project_gallery_list.selectedItems():
              image_id = item.data(Qt.UserRole)
              info = self.current_project.get_image_info(image_id)
              if info:
                   selected_infos.append(info)
         return selected_infos

    def on_gallery_item_double_clicked(self, item):
        """Handle double-click on gallery item."""
        if not self.current_project or not item:
            return

        image_id = item.data(Qt.UserRole)
        if not image_id:
            return

        # Get image info
        info = self.current_project.get_image_info(image_id)
        if not info:
            return

        # Get image path
        image_path = self.current_project.get_image_path(image_id)
        if not image_path or not os.path.exists(image_path):
            self._show_error_message("Error", f"Image file not found: {info.filename}")
            return

        # Emit signal to switch to AI Assistant page
        self.switch_page_requested.emit("ai_assistant", [image_path], [info])

    def show_gallery_context_menu(self, position: QPoint):
        """Show context menu for gallery items."""
        if not self.current_project:
            return

        # Get selected items
        selected_items = self.project_gallery_list.selectedItems()
        if not selected_items:
            return

        # Create context menu
        context_menu = QMenu()

        # Add actions
        process_action = context_menu.addAction("Open in Unsupervised Segmentation")
        enhance_action = context_menu.addAction("Open in Image Lab")
        trainable_action = context_menu.addAction("Open in Trainable Segmentation")
        advanced_segmentation_action = context_menu.addAction("Open in Advanced Segmentation")
        grain_action = context_menu.addAction("Open in Grain Analysis")
        ai_assistant_action = context_menu.addAction("Open in AI Assistant")
        point_counting_action = context_menu.addAction("Open in Point Counting")
        context_menu.addSeparator()
        optimize_action = context_menu.addAction("Optimize Image")
        context_menu.addSeparator()
        delete_action = context_menu.addAction("Delete Image")

        # Show the menu and get the selected action
        action = context_menu.exec(self.project_gallery_list.mapToGlobal(position))

        # Handle the selected action
        if action == process_action:
            self.open_selected_in_page("unsupervised_segmentation")
        elif action == enhance_action:
            self.open_selected_in_page("image_lab")
        elif action == trainable_action:
            self.open_selected_in_page("trainable_segmentation")
        elif action == advanced_segmentation_action:
            self.open_selected_in_page("advanced_segmentation")
        elif action == grain_action:
            self.open_selected_in_page("grain_size_analysis")
        elif action == ai_assistant_action:
            self.open_selected_in_page("ai_assistant")
        elif action == point_counting_action:
            self.open_selected_in_page("point_counting")
        elif action == optimize_action:
            self.optimize_selected_image()
        elif action == delete_action:
            self.delete_selected_images()

    def open_selected_in_page(self, page_type):
        """Open selected images in the specified page."""
        if not self.current_project:
            return

        # Get selected items
        selected_items = self.project_gallery_list.selectedItems()
        if not selected_items:
            return

        # Collect image paths and infos
        image_paths = []
        image_infos = []

        for item in selected_items:
            image_id = item.data(Qt.UserRole)
            if not image_id:
                continue

            # Get image info
            info = self.current_project.get_image_info(image_id)
            if not info:
                continue

            # Get image path
            image_path = self.current_project.get_image_path(image_id)
            if not image_path or not os.path.exists(image_path):
                self._show_error_message("Error", f"Image file not found: {info.filename}")
                continue

            image_paths.append(image_path)
            image_infos.append(info)

        if not image_paths:
            return

        # Emit signal to switch to the specified page
        self.switch_page_requested.emit(page_type, image_paths, image_infos)

    @Slot()
    def start_batch_processing(self):
        """Start batch processing for selected images."""
        if not self.current_project:
            self._show_error_message("Error", "No project is open.")
            return

        # Get selected items
        selected_items = self.project_gallery_list.selectedItems()
        if not selected_items:
            self._show_error_message("Error", "Please select images to process.")
            return

        # Collect image paths and infos
        image_paths = []
        image_infos = []

        for item in selected_items:
            image_id = item.data(Qt.UserRole)
            if not image_id:
                continue

            # Get image info
            info = self.current_project.get_image_info(image_id)
            if not info:
                continue

            # Get image path
            image_path = self.current_project.get_image_path(image_id)
            if not image_path or not os.path.exists(image_path):
                self._show_error_message("Error", f"Image file not found: {info.filename}")
                continue

            image_paths.append(image_path)
            image_infos.append(info)

        if not image_paths:
            self._show_error_message("Error", "No valid images selected.")
            return

        logger.info(f"Starting batch processing for {len(image_paths)} images")
        
        # Emit signal to switch to batch processing page
        self.switch_page_requested.emit("batch_processing", image_paths, image_infos)

    def delete_selected_images(self):
        """Delete selected images from the project."""
        if not self.current_project:
            return

        # Get selected items
        selected_items = self.project_gallery_list.selectedItems()
        if not selected_items:
            return

        # Confirm deletion
        num_selected = len(selected_items)
        plural = 's' if num_selected > 1 else ''
        message = f"Are you sure you want to delete {num_selected} image{plural}?"
        message += f"\nThis will permanently remove the image{plural} from the project."

        reply = QMessageBox.question(
            self, "Confirm Deletion", message,
            QMessageBox.Yes | QMessageBox.No, QMessageBox.No
        )

        if reply != QMessageBox.Yes:
            return

        # Delete selected images
        deleted_count = 0
        for item in selected_items:
            image_id = item.data(Qt.UserRole)
            if not image_id:
                continue

            # Delete the image
            if self.is_vlp_project:
                success = self.current_project.remove_image(image_id)
            else:
                success = self.current_project.remove_image(image_id, delete_file=True)

            if success:
                deleted_count += 1

        # Update the gallery display
        if deleted_count > 0:
            self._update_gallery_display()
            self.current_project.save()  # Save project after deleting images

            # Clear image preview if all images were deleted
            if hasattr(self, 'image_preview_view') and self.project_gallery_list.count() == 0:
                self.image_preview_view.clear()
                self.image_info_label.setText("No image selected")
                self.image_preview_groupbox.setEnabled(False)

            # Show success message
            plural = 's' if deleted_count > 1 else ''
            self._show_message("Images Deleted", f"Successfully deleted {deleted_count} image{plural}.")

    def trigger_analysis(self, analysis_type: str):
        """Handles button clicks to start analysis on selected images."""
        if not self.current_project: return self._show_error_message("Error", "No project open.")
        selected_infos = self.get_selected_image_infos()
        if not selected_infos: return self._show_error_message("Selection Error", "No images selected.")

        logger.info(f"Triggering '{analysis_type}' for {len(selected_infos)} image(s).")

        # Handle both single and multiple image selection
        # ProjectHubPage has the switch_to_analysis_page method
        # Since ProjectHubHandler is used within ProjectHubPage, we need to access it through self
        if isinstance(self, QWidget) and hasattr(self, 'switch_to_analysis_page'):
            # Direct call if this is a ProjectHubPage instance
            self.switch_to_analysis_page(selected_infos, analysis_type)
        else:
            # Try to find the method in the parent class/object
            parent = getattr(self, 'parent', lambda: None)()
            if hasattr(parent, 'switch_to_analysis_page'):
                parent.switch_to_analysis_page(selected_infos, analysis_type)
            else:
                logger.error("switch_to_analysis_page method not found in handler or parent.")
                self._show_error_message("Error", "Cannot switch to analysis page.")

    def start_batch_processing(self):
        """Starts batch processing for selected images based on their assigned workflows."""
        if not self.current_project: return self._show_error_message("Error", "No project open.")
        selected_infos = self.get_selected_image_infos()
        if not selected_infos: return self._show_error_message("Selection Error", "No images selected.")

        # Check if BatchProcessingDialog is available
        try:
            from src.gui.batch_processing_dialog import BatchProcessingDialog

            # Create and show the batch processing dialog
            # The dialog needs a parent widget, so we need to check if self is a widget
            # or find a parent widget
            if isinstance(self, QWidget):
                dialog = BatchProcessingDialog(
                    parent=self,
                    project=self.current_project,
                    selected_images=selected_infos
                )
                dialog.exec()

                # After batch processing, refresh the gallery to show any new results
                if hasattr(self, '_update_gallery_display'):
                    self._update_gallery_display()
            else:
                # Try to get parent widget
                parent = getattr(self, 'parent', lambda: None)()
                if parent and isinstance(parent, QWidget):
                    dialog = BatchProcessingDialog(
                        parent=parent,
                        project=self.current_project,
                        selected_images=selected_infos
                    )
                    dialog.exec()

                    # After batch processing, refresh the gallery to show any new results
                    if hasattr(self, '_update_gallery_display'):
                        self._update_gallery_display()
                else:
                    self._show_error_message("Error", "Cannot create batch processing dialog without a parent widget.")
        except ImportError:
            logger.error("BatchProcessingDialog not found.")
            self._show_error_message("Error", "Batch processing dialog not available.")
        except Exception as e:
            logger.exception(f"Error starting batch processing: {e}")
            self._show_error_message("Error", f"Failed to start batch processing: {e}")

    # --- Helper methods for messages (should be part of the main app ideally) ---
    def _show_error_message(self, title: str, message: str):
        """Shows an error message box."""
        if hasattr(self, 'parent') and callable(getattr(self, 'parent', None)): # Check if it's a QWidget with parent
             QMessageBox.critical(self.parent(), title, message)
        else:
             # Fallback if parent context is unclear (might happen if handler used standalone)
             msgBox = QMessageBox()
             msgBox.setIcon(QMessageBox.Critical)
             msgBox.setWindowTitle(title)
             msgBox.setText(message)
             msgBox.exec()

    def _show_message(self, title: str, message: str):
        """Shows an informational message box."""
        if hasattr(self, 'parent') and callable(getattr(self, 'parent', None)): # Check if it's a QWidget with parent
             QMessageBox.information(self.parent(), title, message)
        else:
             # Fallback if parent context is unclear (might happen if handler used standalone)
             msgBox = QMessageBox()
             msgBox.setIcon(QMessageBox.Information)
             msgBox.setWindowTitle(title)
             msgBox.setText(message)
             msgBox.exec()

    def optimize_selected_image(self):
        """Open the optimization dialog for the selected image."""
        if not self.current_project:
            self._show_error_message("Error", "No project open.")
            return

        # Get selected items
        selected_items = self.project_gallery_list.selectedItems()
        if not selected_items:
            self._show_error_message("Error", "No image selected.")
            return

        # For now, we'll only optimize the first selected image
        # You can modify this to handle multiple images if needed
        item = selected_items[0]
        image_id = item.data(Qt.UserRole)
        if not image_id:
            return

        # Get image info
        info = self.current_project.get_image_info(image_id)
        if not info:
            self._show_error_message("Error", "Could not get image information.")
            return

        # Get image path
        image_path = self.current_project.get_image_path(image_id)
        if not image_path or not os.path.exists(image_path):
            self._show_error_message("Error", f"Image file not found: {info.filename}")
            return

        try:
            # Create and show the optimization dialog
            dialog = ImageOptimizationDialog(
                parent=self.parent() if hasattr(self, 'parent') else None,
                image_paths=[image_path]  # Pass as a list
            )
            
            # Show the dialog
            if dialog.exec() == QDialog.Accepted:
                # Get optimized images and their info
                optimized_images = dialog.get_optimized_images()
                optimization_info = dialog.get_optimization_info()
                
                if not optimized_images:
                    self._show_message("No Changes", "No optimizations were applied to the image.")
                    return
                
                # Create a temporary directory to save optimized images
                import tempfile
                temp_dir = tempfile.mkdtemp()
                
                try:
                    # Save and add each optimized image to the project
                    for img_path, img in optimized_images.items():
                        # Handle patches separately
                        if '#' in str(img_path):
                            # This is a patch - extract original path and patch info
                            original_path, patch_info = str(img_path).split('#', 1)
                            patch_optimization_info = optimization_info.get(img_path, {})
                            
                            # Get the optimization info for the original image to understand patch settings
                            original_info = optimization_info.get(original_path, {})
                            
                            # Determine file extension based on optimization
                            if patch_optimization_info.get('format_conversion', False) or original_info.get('format_conversion', False):
                                ext = f".{patch_optimization_info.get('format', original_info.get('new_format', 'jpg'))}"
                            else:
                                _, ext = os.path.splitext(original_path)
                            
                            # Create a descriptive filename for the patch
                            base_name = os.path.basename(original_path)
                            name_without_ext = os.path.splitext(base_name)[0]
                            patch_index = patch_optimization_info.get('patch_index', 0)
                            temp_path = os.path.join(temp_dir, f"{name_without_ext}_patch_{patch_index:03d}{ext}")
                            
                            # Convert RGB to BGR for OpenCV saving
                            img_bgr = cv2.cvtColor(img, cv2.COLOR_RGB2BGR)
                            
                            # Save the patch
                            cv2.imwrite(temp_path, img_bgr)
                            
                            # Add the patch to the project
                            project_info = self.current_project.add_image(temp_path)
                            if project_info:
                                # Store patch metadata
                                metadata = project_info.metadata or {}
                                metadata["optimization"] = patch_optimization_info
                                metadata["is_patch"] = True
                                metadata["original_image"] = os.path.basename(original_path)
                                metadata["patch_info"] = {
                                    "index": patch_index,
                                    "position": patch_optimization_info.get('patch_position'),
                                    "size": patch_optimization_info.get('patch_size'),
                                    "overlap": patch_optimization_info.get('patch_overlap')
                                }
                                self.current_project.set_metadata(project_info.id, metadata)
                            continue
                            
                        # Get the optimization info for this image
                        info = optimization_info.get(img_path, {})
                        
                        # Determine file extension based on optimization
                        if info.get('format_conversion', False):
                            ext = f".{info.get('new_format', 'jpg')}"
                        else:
                            _, ext = os.path.splitext(img_path)
                        
                        # Create a temporary file path
                        base_name = os.path.basename(img_path)
                        name_without_ext = os.path.splitext(base_name)[0]
                        temp_path = os.path.join(temp_dir, f"optimized_{name_without_ext}{ext}")
                        
                        # Convert RGB to BGR for OpenCV saving
                        img_bgr = cv2.cvtColor(img, cv2.COLOR_RGB2BGR)
                        
                        # Save the optimized image
                        cv2.imwrite(temp_path, img_bgr)
                        
                        # Add the optimized image to the project
                        project_info = self.current_project.add_image(temp_path)
                        if project_info:
                            # Store optimization info in metadata
                            metadata = project_info.metadata or {}
                            metadata["optimization"] = info
                            self.current_project.set_metadata(project_info.id, metadata)
                    
                    # Save the project and update the gallery
                    self.current_project.save()
                    self._update_gallery_display()
                    
                    self._show_message("Success", "Optimized images have been added to the project.")
                    
                finally:
                    # Clean up temporary directory
                    import shutil
                    try:
                        shutil.rmtree(temp_dir)
                    except Exception as e:
                        logger.warning(f"Failed to remove temporary directory {temp_dir}: {e}")
            
        except Exception as e:
            logger.exception("Error in image optimization dialog:")
            self._show_error_message("Error", f"Failed to process optimized images: {str(e)}")

    def _find_main_app(self):
        """Finds the main application instance.

        Returns:
            The main application instance or None if not found.
        """
        # Try to find the main app through parent hierarchy
        parent = self
        while parent:
            # Check if this is the main app (VisionLabAiApp)
            if hasattr(parent, 'reset_application_state'):
                return parent

            # Move up the parent hierarchy
            if hasattr(parent, 'parent') and callable(getattr(parent, 'parent', None)):
                parent = parent.parent()
            else:
                break

        # Alternative approach: check if we're part of a ProjectHubPage that has a reference to the main app
        if hasattr(self, 'parent') and callable(getattr(self, 'parent', None)):
            parent = self.parent()
            if hasattr(parent, 'app'):
                return parent.app

        # Last resort: try to get the main window from QApplication
        app = QApplication.instance()
        if app:
            for widget in app.topLevelWidgets():
                if hasattr(widget, 'reset_application_state'):
                    return widget

        logger.warning("Could not find main application instance for state reset")
        return None

    def setup_recent_projects_menu(self):
        """Set up the recent projects menu for the open recent button."""
        self.recent_projects_menu = RecentProjectsMenu(self.open_recent_button)
        self.recent_projects_menu.project_selected.connect(self.open_recent_project)
        self.recent_projects_menu.clear_recent_requested.connect(self.clear_recent_projects)
        
        # Set the menu to the button
        self.open_recent_button.setMenu(self.recent_projects_menu)
    
    @Slot(str)
    def open_recent_project(self, project_path):
        """Open a recent project by path.
        
        Args:
            project_path (str): Path to the project directory
        """
        if not os.path.exists(project_path):
            self._show_error_message("Error", f"Project path no longer exists: {project_path}")
            # Remove from recent projects
            settings_manager.remove_recent_project(project_path)
            self.recent_projects_menu.refresh()
            return
        
        # Add confirmation if a project is already open
        if self.current_project:
            reply = QMessageBox.question(
                self,
                "Open Recent Project",
                "Warning: Opening a new project will clear all current project data and results. This action cannot be undone. Continue?",
                QMessageBox.Yes | QMessageBox.No
            )
            if reply == QMessageBox.No:
                return

            # Reset application state before loading new project
            app = self._find_main_app()
            if app and hasattr(app, 'reset_application_state'):
                logger.info("Resetting application state before opening recent project")
                app.reset_application_state(show_loading_indicator=True)
        
        try:
            # Check if it's a VLP project
            if project_path.endswith('.vlp'):
                self.current_project = VisionLabProject(project_path)
                self.is_vlp_project = True
                project_type = "vlp"
            else:
                # Check if it's a standard project
                if os.path.exists(os.path.join(project_path, Project.PROJECT_FILE_NAME)):
                    self.current_project = Project.load(project_path)
                    self.is_vlp_project = False
                    project_type = "standard"
                else:
                    # Try to treat as project directory anyway
                    self.current_project = Project.load(project_path)
                    self.is_vlp_project = False
                    project_type = "standard"
            
            self.last_project_dir = os.path.dirname(project_path)
            logger.info(f"Opened recent project: {project_path}")
            
            # Add to recent projects (this will move it to the top)
            project_name = self.current_project.name if self.current_project else os.path.basename(project_path)
            settings_manager.add_recent_project(project_path, project_name, project_type)
            
            self._update_project_display()
            self._show_message("Project Opened", f"Recent project '{project_name}' opened.")
            
            # Refresh the recent projects menu
            self.recent_projects_menu.refresh()
            
        except Exception as e:
            logger.exception("Failed to load recent project.")
            self._show_error_message("Error", f"Failed to load recent project: {e}")
            self.current_project = None
            # Remove from recent projects if it failed to load
            settings_manager.remove_recent_project(project_path)
            self.recent_projects_menu.refresh()
        
        self._update_hub_ui_state()
    
    @Slot()
    def clear_recent_projects(self):
        """Clear all recent projects."""
        reply = QMessageBox.question(
            self,
            "Clear Recent Projects",
            "Are you sure you want to clear all recent projects?",
            QMessageBox.Yes | QMessageBox.No
        )
        if reply == QMessageBox.Yes:
            settings_manager.clear_recent_projects()
            self.recent_projects_menu.refresh()
            self._show_message("Cleared", "Recent projects list has been cleared.")